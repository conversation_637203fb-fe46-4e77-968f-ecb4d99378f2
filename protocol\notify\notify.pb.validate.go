// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: notify/notify.proto

package notify

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on LackMessage with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LackMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LackMessage with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LackMessageMultiError, or
// nil if none found.
func (m *LackMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *LackMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Title

	// no validation rules for Content

	// no validation rules for UnionId

	if all {
		switch v := interface{}(m.GetTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LackMessageValidationError{
					field:  "Time",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LackMessageValidationError{
					field:  "Time",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LackMessageValidationError{
				field:  "Time",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LackType

	if len(errors) > 0 {
		return LackMessageMultiError(errors)
	}

	return nil
}

// LackMessageMultiError is an error wrapping multiple validation errors
// returned by LackMessage.ValidateAll() if the designated constraints aren't met.
type LackMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LackMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LackMessageMultiError) AllErrors() []error { return m }

// LackMessageValidationError is the validation error returned by
// LackMessage.Validate if the designated constraints aren't met.
type LackMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LackMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LackMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LackMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LackMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LackMessageValidationError) ErrorName() string { return "LackMessageValidationError" }

// Error satisfies the builtin error interface
func (e LackMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLackMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LackMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LackMessageValidationError{}

// Validate checks the field values on ExpireMsgCardMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExpireMsgCardMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExpireMsgCardMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExpireMsgCardMessageMultiError, or nil if none found.
func (m *ExpireMsgCardMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *ExpireMsgCardMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Select

	if all {
		switch v := interface{}(m.GetTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExpireMsgCardMessageValidationError{
					field:  "Time",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExpireMsgCardMessageValidationError{
					field:  "Time",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExpireMsgCardMessageValidationError{
				field:  "Time",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RespInfo

	// no validation rules for BackGroundColor

	if len(errors) > 0 {
		return ExpireMsgCardMessageMultiError(errors)
	}

	return nil
}

// ExpireMsgCardMessageMultiError is an error wrapping multiple validation
// errors returned by ExpireMsgCardMessage.ValidateAll() if the designated
// constraints aren't met.
type ExpireMsgCardMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExpireMsgCardMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExpireMsgCardMessageMultiError) AllErrors() []error { return m }

// ExpireMsgCardMessageValidationError is the validation error returned by
// ExpireMsgCardMessage.Validate if the designated constraints aren't met.
type ExpireMsgCardMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExpireMsgCardMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExpireMsgCardMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExpireMsgCardMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExpireMsgCardMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExpireMsgCardMessageValidationError) ErrorName() string {
	return "ExpireMsgCardMessageValidationError"
}

// Error satisfies the builtin error interface
func (e ExpireMsgCardMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExpireMsgCardMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExpireMsgCardMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExpireMsgCardMessageValidationError{}

// Validate checks the field values on EmailMessage with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EmailMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmailMessage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EmailMessageMultiError, or
// nil if none found.
func (m *EmailMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *EmailMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	// no validation rules for Content

	// no validation rules for Receiver

	// no validation rules for Cc

	if len(errors) > 0 {
		return EmailMessageMultiError(errors)
	}

	return nil
}

// EmailMessageMultiError is an error wrapping multiple validation errors
// returned by EmailMessage.ValidateAll() if the designated constraints aren't met.
type EmailMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmailMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmailMessageMultiError) AllErrors() []error { return m }

// EmailMessageValidationError is the validation error returned by
// EmailMessage.Validate if the designated constraints aren't met.
type EmailMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmailMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmailMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmailMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmailMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmailMessageValidationError) ErrorName() string { return "EmailMessageValidationError" }

// Error satisfies the builtin error interface
func (e EmailMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmailMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmailMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmailMessageValidationError{}

// Validate checks the field values on Result with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Result) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Result with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ResultMultiError, or nil if none found.
func (m *Result) ValidateAll() error {
	return m.validate(true)
}

func (m *Result) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Err

	if len(errors) > 0 {
		return ResultMultiError(errors)
	}

	return nil
}

// ResultMultiError is an error wrapping multiple validation errors returned by
// Result.ValidateAll() if the designated constraints aren't met.
type ResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResultMultiError) AllErrors() []error { return m }

// ResultValidationError is the validation error returned by Result.Validate if
// the designated constraints aren't met.
type ResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResultValidationError) ErrorName() string { return "ResultValidationError" }

// Error satisfies the builtin error interface
func (e ResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResultValidationError{}
