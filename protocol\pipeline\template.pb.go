// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        (unknown)
// source: pipeline/template.proto

package pipeline

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateTemplateTaskImageConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"imageID"
	ImageId int64 `protobuf:"varint,1,opt,name=image_id,json=imageID,proto3" json:"image_id,omitempty"`
	// @gotags: json:"imageAddress"
	ImageAddress string `protobuf:"bytes,2,opt,name=image_address,json=imageAddress,proto3" json:"image_address,omitempty"`
}

func (x *UpdateTemplateTaskImageConfigReq) Reset() {
	*x = UpdateTemplateTaskImageConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_template_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTemplateTaskImageConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTemplateTaskImageConfigReq) ProtoMessage() {}

func (x *UpdateTemplateTaskImageConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_template_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTemplateTaskImageConfigReq.ProtoReflect.Descriptor instead.
func (*UpdateTemplateTaskImageConfigReq) Descriptor() ([]byte, []int) {
	return file_pipeline_template_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateTemplateTaskImageConfigReq) GetImageId() int64 {
	if x != nil {
		return x.ImageId
	}
	return 0
}

func (x *UpdateTemplateTaskImageConfigReq) GetImageAddress() string {
	if x != nil {
		return x.ImageAddress
	}
	return ""
}

type FindTemplateBYImageIDReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"imageID"
	ImageId int64 `protobuf:"varint,1,opt,name=image_id,json=imageID,proto3" json:"image_id,omitempty"`
}

func (x *FindTemplateBYImageIDReq) Reset() {
	*x = FindTemplateBYImageIDReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_template_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindTemplateBYImageIDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindTemplateBYImageIDReq) ProtoMessage() {}

func (x *FindTemplateBYImageIDReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_template_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindTemplateBYImageIDReq.ProtoReflect.Descriptor instead.
func (*FindTemplateBYImageIDReq) Descriptor() ([]byte, []int) {
	return file_pipeline_template_proto_rawDescGZIP(), []int{1}
}

func (x *FindTemplateBYImageIDReq) GetImageId() int64 {
	if x != nil {
		return x.ImageId
	}
	return 0
}

type PipelineTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *PipelineTemplate) Reset() {
	*x = PipelineTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_template_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineTemplate) ProtoMessage() {}

func (x *PipelineTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_template_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineTemplate.ProtoReflect.Descriptor instead.
func (*PipelineTemplate) Descriptor() ([]byte, []int) {
	return file_pipeline_template_proto_rawDescGZIP(), []int{2}
}

func (x *PipelineTemplate) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PipelineTemplate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type FindTemplateBYImageIDResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Templates []*PipelineTemplate `protobuf:"bytes,1,rep,name=templates,proto3" json:"templates,omitempty"`
}

func (x *FindTemplateBYImageIDResp) Reset() {
	*x = FindTemplateBYImageIDResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_template_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindTemplateBYImageIDResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindTemplateBYImageIDResp) ProtoMessage() {}

func (x *FindTemplateBYImageIDResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_template_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindTemplateBYImageIDResp.ProtoReflect.Descriptor instead.
func (*FindTemplateBYImageIDResp) Descriptor() ([]byte, []int) {
	return file_pipeline_template_proto_rawDescGZIP(), []int{3}
}

func (x *FindTemplateBYImageIDResp) GetTemplates() []*PipelineTemplate {
	if x != nil {
		return x.Templates
	}
	return nil
}

var File_pipeline_template_proto protoreflect.FileDescriptor

var file_pipeline_template_proto_rawDesc = []byte{
	0x0a, 0x17, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x62, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x49, 0x44, 0x12,
	0x23, 0x0a, 0x0d, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x22, 0x35, 0x0a, 0x18, 0x46, 0x69, 0x6e, 0x64, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x42, 0x59, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x49, 0x44, 0x52, 0x65, 0x71,
	0x12, 0x19, 0x0a, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x49, 0x44, 0x22, 0x36, 0x0a, 0x10, 0x50,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0x55, 0x0a, 0x19, 0x46, 0x69, 0x6e, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x42, 0x59, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x38, 0x0a, 0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52,
	0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x32, 0xe0, 0x01, 0x0a, 0x17, 0x50,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x63, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2a, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x60, 0x0a, 0x15, 0x46,
	0x69, 0x6e, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x79, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x49, 0x44, 0x12, 0x22, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x46, 0x69, 0x6e, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x59, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x49, 0x44, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x42, 0x59, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x42, 0x37, 0x5a,
	0x35, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x61, 0x72,
	0x6d, 0x6f, 0x6e, 0x79, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x3b, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pipeline_template_proto_rawDescOnce sync.Once
	file_pipeline_template_proto_rawDescData = file_pipeline_template_proto_rawDesc
)

func file_pipeline_template_proto_rawDescGZIP() []byte {
	file_pipeline_template_proto_rawDescOnce.Do(func() {
		file_pipeline_template_proto_rawDescData = protoimpl.X.CompressGZIP(file_pipeline_template_proto_rawDescData)
	})
	return file_pipeline_template_proto_rawDescData
}

var file_pipeline_template_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_pipeline_template_proto_goTypes = []interface{}{
	(*UpdateTemplateTaskImageConfigReq)(nil), // 0: pipeline.UpdateTemplateTaskImageConfigReq
	(*FindTemplateBYImageIDReq)(nil),         // 1: pipeline.FindTemplateBYImageIDReq
	(*PipelineTemplate)(nil),                 // 2: pipeline.PipelineTemplate
	(*FindTemplateBYImageIDResp)(nil),        // 3: pipeline.FindTemplateBYImageIDResp
	(*emptypb.Empty)(nil),                    // 4: google.protobuf.Empty
}
var file_pipeline_template_proto_depIdxs = []int32{
	2, // 0: pipeline.FindTemplateBYImageIDResp.templates:type_name -> pipeline.PipelineTemplate
	0, // 1: pipeline.PipelineTemplateService.UpdateTemplateTaskImageConfig:input_type -> pipeline.UpdateTemplateTaskImageConfigReq
	1, // 2: pipeline.PipelineTemplateService.FindTemplateByImageID:input_type -> pipeline.FindTemplateBYImageIDReq
	4, // 3: pipeline.PipelineTemplateService.UpdateTemplateTaskImageConfig:output_type -> google.protobuf.Empty
	3, // 4: pipeline.PipelineTemplateService.FindTemplateByImageID:output_type -> pipeline.FindTemplateBYImageIDResp
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pipeline_template_proto_init() }
func file_pipeline_template_proto_init() {
	if File_pipeline_template_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pipeline_template_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTemplateTaskImageConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_template_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindTemplateBYImageIDReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_template_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_template_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindTemplateBYImageIDResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pipeline_template_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pipeline_template_proto_goTypes,
		DependencyIndexes: file_pipeline_template_proto_depIdxs,
		MessageInfos:      file_pipeline_template_proto_msgTypes,
	}.Build()
	File_pipeline_template_proto = out.File
	file_pipeline_template_proto_rawDesc = nil
	file_pipeline_template_proto_goTypes = nil
	file_pipeline_template_proto_depIdxs = nil
}
