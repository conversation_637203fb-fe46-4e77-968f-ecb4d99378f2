package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/go-playground/validator"
	"github.com/golang/glog"
	"github.com/mitchellh/mapstructure"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"
	"sigs.k8s.io/sig-storage-lib-external-provisioner/v9/controller"

	"golang.ttyuyin.com/harmony/csi-driver/pkg/repo"
	"golang.ttyuyin.com/harmony/csi-driver/pkg/volume"
	reposyncpb "golang.ttyuyin.com/harmony/csi-driver/protocol/rdp/reposync/v1"
)

const (
	provisionerName = "52tt.io/overlay-nfs-provisioner"
	nfsLable        = "52tt.io/nfsFsid"
	version1        = "v1"
	version2        = "v2"
	defaultBranch   = "master"
)

type nfsProvisioner struct {
	client     kubernetes.Interface
	server     string
	grpcClient reposyncpb.RepoSyncServiceClient
	grpcConn   *grpc.ClientConn
}

type pvcAnnotations struct {
	RepoAddress string `mapstructure:"pipelinerun.cicd.work/repoAddress" validate:"required"`
	RequestDate string `mapstructure:"pipelinerun.cicd.work/requestDate" validate:"required"`
	PipelineID  string `mapstructure:"pipelinerun.cicd.work/pipelineId" validate:"required"`
	BuildNumber string `mapstructure:"pipelinerun.cicd.work/buildNumber" validate:"required"`
	Env         string `mapstructure:"pipelinerun.cicd.work/env" validate:"required"`
	Branch      string `mapstructure:"pipelinerun.cicd.work/branch"`
	Version     string `mapstructure:"pipelinerun.cicd.work/csiVersion"` // CSI 的版本号
}

// split write and read to different mountpoint for performance(mount different disk)
const (
	mountPath = "/data/csi-v2"
	repoDir   = "/gitlab-repo"
)

var _ controller.Provisioner = &nfsProvisioner{}
var logger *zap.SugaredLogger
var validate *validator.Validate

func (p *nfsProvisioner) Provision(ctx context.Context, options controller.ProvisionOptions) (*v1.PersistentVolume, controller.ProvisioningState, error) {
	if options.PVC.ObjectMeta.Annotations == nil {
		return nil, controller.ProvisioningFinished, fmt.Errorf("nfs provisioner: PVC annotations missing")
	}
	logger.Infof("nfs provisioner: VolumeOptions %v", options)

	var annotations pvcAnnotations
	err := mapstructure.Decode(options.PVC.Annotations, &annotations)
	if err != nil {
		return nil, controller.ProvisioningFinished, fmt.Errorf("error decoding annotations: %v", err)
	}

	err = validate.Struct(annotations)
	if err != nil {
		return nil, controller.ProvisioningFinished, fmt.Errorf("error validating annotations: %v", err)
	}

	if annotations.Version == "" {
		annotations.Version = version1
	}
	if annotations.Branch == "" {
		annotations.Branch = defaultBranch
	}

	lowerdir, err := getLowerDir(ctx, p, annotations)
	if err != nil {
		return nil, controller.ProvisioningFinished, err
	}

	upperdir := filepath.Join(mountPath, annotations.Env, annotations.PipelineID, annotations.BuildNumber, "upperdir")
	workdir := filepath.Join(mountPath, annotations.Env, annotations.PipelineID, annotations.BuildNumber, "workdir")
	mergeddir := filepath.Join(mountPath, annotations.Env, annotations.PipelineID, annotations.BuildNumber, "merged")

	volume := volume.NewVolume(lowerdir, upperdir, workdir, mergeddir)

	logger.Infof("creating overlayfs lower %s - upper %s - work %s - merged %s", lowerdir, upperdir, workdir, mergeddir)
	err = volume.Mount()
	if err != nil {
		return nil, controller.ProvisioningFinished, fmt.Errorf("error mounting volume: %v", err)
	}

	logger.Infof("export overlayfs %s", mergeddir)
	err = volume.Export(annotations.PipelineID + annotations.BuildNumber)
	if err != nil {
		return nil, controller.ProvisioningFinished, fmt.Errorf("error exporting volume: %v", err)
	}

	logger.Debugf("export overlayfs successful %s", mergeddir)
	pv := &v1.PersistentVolume{
		ObjectMeta: metav1.ObjectMeta{
			Name: options.PVName,
			Labels: map[string]string{
				nfsLable: annotations.PipelineID + annotations.BuildNumber,
			},
		},
		Spec: v1.PersistentVolumeSpec{
			PersistentVolumeReclaimPolicy: *options.StorageClass.ReclaimPolicy,
			AccessModes:                   options.PVC.Spec.AccessModes,
			MountOptions:                  options.StorageClass.MountOptions,
			Capacity: v1.ResourceList{
				v1.ResourceName(v1.ResourceStorage): options.PVC.Spec.Resources.Requests[v1.ResourceName(v1.ResourceStorage)],
			},
			PersistentVolumeSource: v1.PersistentVolumeSource{
				NFS: &v1.NFSVolumeSource{
					Server:   p.server,
					Path:     mergeddir,
					ReadOnly: false,
				},
			},
		},
	}

	logger.Debugf("generated PV for PVC %s: %v", options.PVC.Name, pv)
	return pv, controller.ProvisioningFinished, nil
}

func (p *nfsProvisioner) Delete(ctx context.Context, pv *v1.PersistentVolume) error {
	fsid := pv.GetObjectMeta().GetLabels()[nfsLable]
	path := pv.Spec.PersistentVolumeSource.NFS.Path

	exists, err := hasOtherPVsUsingNFSID(p.client, fsid)

	if err != nil {
		return fmt.Errorf("Delete pv error: %v", err)
	}

	if exists {
		logger.Infof("other PVs still using NFS path %s, skip deleting", path)
		return nil
	}

	// Unmount the volume
	err = volume.Umount(path)
	if err != nil {
		return fmt.Errorf("error unmounting volume: %v", err)
	}

	return nil
}

// when a retry pipeline running and its parent pipeline is deleting, we should skip umount nfs because retry pipeline need to use
func hasOtherPVsUsingNFSID(clientset kubernetes.Interface, nfsID string) (bool, error) {
	listOptions := metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s=%s", nfsLable, nfsID),
	}

	pvList, err := clientset.CoreV1().PersistentVolumes().List(context.TODO(), listOptions)
	logger.Infof("find pvList: %v", pvList)
	if err != nil {
		return false, fmt.Errorf("failed to list PVs: %v", err)
	}

	return len(pvList.Items) > 1, nil
}

func getRepoGroupAndRepoName(repoAddr string) (string, string, error) {
	reg := regexp.MustCompile(`^https?://`)
	matched := reg.MatchString(repoAddr)
	if !matched {
		return "", "", fmt.Errorf("repo url must be starts with http or https")
	}
	addr, err := url.Parse(repoAddr)
	if err != nil {
		return "", "", err
	}
	path := strings.Split(addr.Path, "/")
	if len(path) < 2 {
		return "", "", fmt.Errorf("repo url must be contain repoGroup and repoName")
	}
	repoName := path[len(path)-1]
	repoGroup := path[len(path)-2]
	if strings.HasSuffix(repoName, ".git") {
		dotIndex := strings.IndexRune(repoName, '.')
		repoName = repoName[0:dotIndex]
	}
	return repoGroup, repoName, nil
}

func dirExist(path string) bool {
	_, err := os.Stat(path)
	return !os.IsNotExist(err)
}

func getLowerDir(ctx context.Context, p *nfsProvisioner, as pvcAnnotations) (string, error) {
	repoGroup, repoName, err := getRepoGroupAndRepoName(as.RepoAddress)
	if err != nil {
		return "", fmt.Errorf("获取代码库的组名和代码库名称出错,错误原因:%v", err)
	}

	var lowerDir string
	v1defaultPath := filepath.Join(repoDir, repoGroup, repoName, as.RequestDate) // v1 默认路径，每月创建
	if as.Version == version1 {
		lowerDir = v1defaultPath
	} else {
		// version 2
		r := repo.New(as.RepoAddress, filepath.Join(repoDir, version2, as.RequestDate))
		v2defaultMasterPath, v2defaultMainPath := r.TargetDir(), r.MainBranchTargetDir() // v2 版本的默认master分支路径

		r.SetBranch(as.Branch)
		branchPath := r.TargetDir()
		if dirExist(branchPath) {
			lowerDir = branchPath
		} else {
			logger.Infof("version 2 path [%s] not exist, notify repo-sync to clone", branchPath)
			req := &reposyncpb.CreateLowerDirRequest{
				FullPath: branchPath,
				RepoAddr: as.RepoAddress,
				Branch:   as.Branch,
			}
			switch as.Version {
			case version1:
				req.Version = reposyncpb.Version_VERSION_V1
			case version2:
				req.Version = reposyncpb.Version_VERSION_V2
			default:
				req.Version = reposyncpb.Version_VERSION_UNKNOWN
			}

			// 异步同步通知 reposync服务 同步流水线分支只读层
			go p.asyncLowerDir(ctx, req)

			if dirExist(v2defaultMasterPath) {
				lowerDir = v2defaultMasterPath
			} else if dirExist(v2defaultMainPath) {
				lowerDir = v2defaultMainPath
			} else {
				return "", fmt.Errorf("version 2 path [%s] not exist, notify repo-sync to clone", v2defaultMasterPath)
			}
		}
	}

	return lowerDir, nil
}

// asyncLowerDir 异步通知同步服务，需要新构造一个只读层。
// CSI 层面可能会有多个请求发出，接收端需要做好并发处理。（同一时间只处理一个创建只读层，其他的请求并不创建只读层而是直接返回。）
func (p *nfsProvisioner) asyncLowerDir(ctx context.Context, req *reposyncpb.CreateLowerDirRequest) {
	// handle painc
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("asyncLowerDir panic: %v", err)
		}
	}()

	// 5分钟 clone 不出来，就等下一次了
	ctx, cancel := context.WithTimeout(ctx, 5*time.Minute)
	defer cancel()

	ensureRpcConnection(ctx, p)
	resp, err := p.grpcClient.CreateLowerDir(ctx, req)
	if err != nil {
		logger.Errorf("CreateLowerDir with %s error: %v", req.String(), err)
	}
	logger.Infof("CreateLowerDir with %s response: %s", req.String(), resp.String())
}

func ensureRpcConnection(ctx context.Context, p *nfsProvisioner) {
	if p.grpcConn == nil {
		c, err := connectRpcServer(ctx)
		if err != nil {
			logger.Errorf("connectRpcServer error: %v", err)
		} else {
			p.grpcConn = c
			p.grpcClient = reposyncpb.NewRepoSyncServiceClient(c)
		}
	}
}

func connectRpcServer(ctx context.Context) (*grpc.ClientConn, error) {
	grpcTarget := os.Getenv("REPOSYNC_GRPC_TARGET")
	if grpcTarget == "" {
		glog.Errorln("environment variable REPOSYNC_GRPC_TARGET is not set! Please set it.")
		return nil, fmt.Errorf("env REPOSYNC_GRPC_TARGET is not set")
	}
	c, err := grpc.DialContext(ctx, grpcTarget, grpc.WithInsecure())
	if err != nil {
		return nil, err
	}
	glog.Infof("connect to grpc server %s success", grpcTarget)
	return c, nil
}

func disConnectRpcServer(p *nfsProvisioner) {
	if p.grpcConn != nil {
		p.grpcConn.Close()
	}
}

func main() {

	zapLogger, err := zap.NewProduction()
	if err != nil {
		log.Fatalf("can't initialize zap logger: %v", err)
	}
	logger = zapLogger.Sugar()
	defer logger.Sync()

	validate = validator.New()

	flag.Parse()
	flag.Set("logtostderr", "true")

	server := os.Getenv("NFS_SERVER")
	if server == "" {
		glog.Fatalf("environment variable NFS_SERVER is not set! Please set it.")
	}

	var config *rest.Config
	environment := os.Getenv("ENVIRONMENT")

	// Get insecure env to decide whether to verify the server's certificate, default verify, used for vm deploy
	config = &rest.Config{}
	insecure := os.Getenv("INSECURE")
	if insecure == "" {
		insecure = "false"
	}

	if environment == "debug" {
		// use kubeconfig for local testing
		var kubeconfig string
		if home := homedir.HomeDir(); home != "" {
			kubeconfig = filepath.Join(home, ".kube", "config")
		}

		config, err = clientcmd.BuildConfigFromFlags("", kubeconfig)
		config.TLSClientConfig = rest.TLSClientConfig{Insecure: insecure == "true"}
		if err != nil {
			panic(err.Error())
		}
	} else {
		// use service account for security reason, used for k8s deploy, default insecure
		config, err = rest.InClusterConfig()
		if err != nil {
			log.Fatalf(err.Error())
		}
	}

	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		glog.Fatalf("Failed to create client: %v", err)
	}

	// The controller needs to know what the server version is because out-of-tree
	// provisioners aren't officially supported until 1.5
	leaderElection := true
	leaderElectionEnv := os.Getenv("ENABLE_LEADER_ELECTION")
	if leaderElectionEnv != "" {
		leaderElection, err = strconv.ParseBool(leaderElectionEnv)
		if err != nil {
			glog.Fatalf("Unable to parse ENABLE_LEADER_ELECTION env var: %v", err)
		}
	}

	clientNFSProvisioner := &nfsProvisioner{
		client: clientset,
		server: server,
	}

	// init grpc connection
	ctx, _ := context.WithTimeout(context.Background(), time.Duration(2)*time.Second)
	c, err := connectRpcServer(ctx)
	if err != nil {
		logger.Errorf("connectRpcServer error: %v", err)
	} else {
		clientNFSProvisioner.grpcConn = c
		clientNFSProvisioner.grpcClient = reposyncpb.NewRepoSyncServiceClient(c)
	}
	defer disConnectRpcServer(clientNFSProvisioner)

	// Start the provision controller which will dynamically provision efs NFS
	// PVs
	pc := controller.NewProvisionController(clientset,
		provisionerName,
		clientNFSProvisioner,
		controller.LeaderElection(leaderElection),
	)
	// Never stops.
	pc.Run(context.Background())
}
