// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: rdp/reposync/v1/repo_sync.proto

package reposync

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	RepoSyncService_CreateLowerDir_FullMethodName = "/rdp.reposync.v1.RepoSyncService/CreateLowerDir"
)

// RepoSyncServiceClient is the client API for RepoSyncService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RepoSyncServiceClient interface {
	// CreateLowerDir creates a lower dir for the given full path.
	CreateLowerDir(ctx context.Context, in *CreateLowerDirRequest, opts ...grpc.CallOption) (*CreateLowerDirResponse, error)
}

type repoSyncServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRepoSyncServiceClient(cc grpc.ClientConnInterface) RepoSyncServiceClient {
	return &repoSyncServiceClient{cc}
}

func (c *repoSyncServiceClient) CreateLowerDir(ctx context.Context, in *CreateLowerDirRequest, opts ...grpc.CallOption) (*CreateLowerDirResponse, error) {
	out := new(CreateLowerDirResponse)
	err := c.cc.Invoke(ctx, RepoSyncService_CreateLowerDir_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RepoSyncServiceServer is the server API for RepoSyncService service.
// All implementations must embed UnimplementedRepoSyncServiceServer
// for forward compatibility
type RepoSyncServiceServer interface {
	// CreateLowerDir creates a lower dir for the given full path.
	CreateLowerDir(context.Context, *CreateLowerDirRequest) (*CreateLowerDirResponse, error)
	mustEmbedUnimplementedRepoSyncServiceServer()
}

// UnimplementedRepoSyncServiceServer must be embedded to have forward compatible implementations.
type UnimplementedRepoSyncServiceServer struct {
}

func (UnimplementedRepoSyncServiceServer) CreateLowerDir(context.Context, *CreateLowerDirRequest) (*CreateLowerDirResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLowerDir not implemented")
}
func (UnimplementedRepoSyncServiceServer) mustEmbedUnimplementedRepoSyncServiceServer() {}

// UnsafeRepoSyncServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RepoSyncServiceServer will
// result in compilation errors.
type UnsafeRepoSyncServiceServer interface {
	mustEmbedUnimplementedRepoSyncServiceServer()
}

func RegisterRepoSyncServiceServer(s grpc.ServiceRegistrar, srv RepoSyncServiceServer) {
	s.RegisterService(&RepoSyncService_ServiceDesc, srv)
}

func _RepoSyncService_CreateLowerDir_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLowerDirRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RepoSyncServiceServer).CreateLowerDir(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RepoSyncService_CreateLowerDir_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RepoSyncServiceServer).CreateLowerDir(ctx, req.(*CreateLowerDirRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RepoSyncService_ServiceDesc is the grpc.ServiceDesc for RepoSyncService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RepoSyncService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "rdp.reposync.v1.RepoSyncService",
	HandlerType: (*RepoSyncServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateLowerDir",
			Handler:    _RepoSyncService_CreateLowerDir_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rdp/reposync/v1/repo_sync.proto",
}
