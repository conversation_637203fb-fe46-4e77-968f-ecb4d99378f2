package server

import (
	"context"
	"golang.ttyuyin.com/harmony/csi-driver/pkg/bootstrap"
	"golang.ttyuyin.com/harmony/csi-driver/pkg/task"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"path"

	"golang.ttyuyin.com/harmony/csi-driver/pkg/repo"
	repsyncpb "golang.ttyuyin.com/harmony/csi-driver/protocol/rdp/reposync/v1"
	"golang.ttyuyin.com/harmony/pkg/log"
)

// 把 pb 枚举转成实际使用的字符串
var versionMapping = map[repsyncpb.Version]string{
	repsyncpb.Version_VERSION_UNKNOWN: "unknown",
	repsyncpb.Version_VERSION_V1:      "v1",
	repsyncpb.Version_VERSION_V2:      "v2",
}

func NewRepoSyncService(rootDir string, tm *task.Manager) *RepoSyncService {
	return &RepoSyncService{
		rootDir: rootDir,
		tm:      tm,
	}
}

type RepoSyncService struct {
	repsyncpb.UnimplementedRepoSyncServiceServer

	tm      *task.Manager
	rootDir string
}

func (b *RepoSyncService) CreateLowerDir(ctx context.Context, req *repsyncpb.CreateLowerDirRequest) (*repsyncpb.CreateLowerDirResponse, error) {
	if req.GetRepoAddr() == "" {
		log.Warnf("repo addr is empty")
		return nil, status.Error(codes.InvalidArgument, "repo addr is empty")
	}
	if req.GetBranch() == "" {
		log.Warnf("branch is empty")
		return nil, status.Error(codes.InvalidArgument, "branch is empty")
	}

	// todo 这些可以不用的，待移除
	version := versionMapping[req.GetVersion()]
	rootDir := path.Join(b.rootDir, version, task.GetCurrentDateString())
	log.Debugf("[CreateLowerDir] start create lower dir, rootDir: %s", rootDir)

	manager := b.tm.CurrentManager()
	r := repo.New(req.GetRepoAddr(), rootDir)
	r.SetBranch(req.GetBranch())

	// check has target dir
	if r.HasTargetDir() || r.HasTempTargetDir() {
		log.Infof("[CreateLowerDir] create lower dir completed, the target dir %s already exists, skip", r.TargetDir())
		return &repsyncpb.CreateLowerDirResponse{}, nil
	}

	if !bootstrap.DebugEnabled() {
		//r.Clone(ctx)
		manager.Clone(ctx, req.GetRepoAddr(), req.GetBranch())
	}

	log.Infof("[CreateLowerDir] create lower dir completed, targetPath: %s", r.TargetDir())
	return &repsyncpb.CreateLowerDirResponse{}, nil
}
