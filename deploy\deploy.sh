#!/bin/bash

# 功能整理
# 1. vm 重启后执行 Remount
# 2. csi-driver repo-sync 两个项目的编译、重启功能

RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color



function remount() {
    # Add your remount logic here
    echo "Executing remount"
}

function echo_green() {
    echo -e "${GREEN}$1${NC}"
}

function echo_red() {
    echo -e "${RED}$1${NC}"
}

function upgrade() {
    local project=$1
    local csiBin="./bin/controller"
    local syncBin="./bin/repo-sync"
    local superDir="/ttyuyin/bin"
    local defaultCsiFile="controller.bak"
    local defaultSyncFile="repo-sync.bak"

    case "$project" in
        csi-driver)
            echo_green "Executing csi-driver upgrade"
            echo "-----------------------------------"
            echo ""
            echo_green "Build csi-driver"
            make compile-test-csi-driver
            echo ""
            echo_red "Build to ${csiBin}"
            echo "-----------------------------------"

            # echo -e "${GREEN}Backup current controller bin file${NC}"
            # read -p $'Please input backup file name, or press enter use (\e[0;32m controller.bak \e[0m):' input
            # if [[ -z $input ]]; then
            #     input="${superDir}/${defaultCsiFile}"
            # else
            #     input="${superDir}/${input}"
            # fi
            # backupFile="${input}"
            # currentFile="${superDir}/controller"
            # echo_green "Moveing ${currentFile} to ${backupFile}"
            # sudo mv $currentFile $backupFile
            # echo "-----------------------------------"

            # echo_green "Moving csi-driver to supervisord target directory"
            # echo_green "Moving ${csiBin} to ${currentFile}"
            # sudo mv $csiBin $currentFile


            # echo_green "Restart controller in supervisor"
            # sudo supervisorctl -c ./ci/supervisord.conf controller restart
            ;;
        repo-sync)
            echo_green "Executing repo-sync upgrade"
            echo "-----------------------------------"
            echo ""
            echo_green "Build repo-sync"
            make compile-test-repo-sync
            echo ""
            echo_red "Build to ${syncBin}"
            echo "-----------------------------------"

            # echo -e "${GREEN}Backup current repo-sync bin file${NC}"
            # read -p $'Please input backup file name, or press enter use (\e[0;32m repo-sync.bak \e[0m):' input
            # if [[ -z $input ]]; then
            #     input="${superDir}/${defaultSyncFile}"
            # else
            #     input="${superDir}/${input}"
            # fi
            # backupFile="${input}"
            # currentFile="${superDir}/repo-sync"
            # echo_green "Moveing ${currentFile} to ${backupFile}"
            # sudo mv $currentFile $backupFile
            # echo "-----------------------------------"

            # echo_green "Moving repo-sync to supervisord target directory"
            # echo_green "Moving ${syncBin} to ${currentFile}"
            # sudo mv $csiBin $currentFile


            # echo_green "Restart repo-sync in supervisor"
            # sudo supervisorctl -c ./ci/supervisord.conf repo-sync restart
            ;;
        *)
            echo "Invalid project $project. Usage: upgrade [csi-driver|repo-sync]"
            exit 1
            ;;
    esac
}

function main() {
    if [[ $0 != "deploy/deploy.sh" ]]; then
        echo -e "${RED}Please run this script from the root directory of the project ${NC}"
        echo "You are currently in $(pwd)"
        exit 1
    fi

    case "$1" in
        remount)
            remount
            ;;
        upgrade)
            upgrade "$2"
            ;;
        *)
            echo "Invalid command. Usage: $0 [remount|upgrade]"
            exit 1
            ;;
    esac
}

main "$@"