# -*- coding:utf-8 -*-
import logging
import os
import subprocess
import time

import pymysql
from dotenv import load_dotenv


logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s]:[%(levelname)s]%(name)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)

def release_expired_pv_space(path_):
    if not os.path.exists(path_):
        logging.warning(f"路径 {path_} 不存在或者已清除")
        return

    merged = f"{path_}/merged"
    if os.path.ismount(merged):
        logging.warning(f"路径 {merged} 是挂载点，不允许删除")
        return

    logging.info(f"路径 {path_} 不被使用，开始删除")

    # 在此处执行删除路径的操作
    try:
        # 暂停两秒操作一次
        time.sleep(2)

        subprocess.run(['rm', '-rf', path_], check=True)
        logging.info(f"路径 {path_} 删除完成")
    except subprocess.CalledProcessError as e:
        logging.error(f"路径 {path_} 删除时发生错误: {e}")
        exit(1)


def get_db_connection():
    host = os.getenv("MYSQL_HOST")
    password = os.getenv("MYSQL_PASSWORD")
    username = os.getenv("MYSQL_USERNAME")
    dbname = os.getenv("MYSQL_DBNAME")
    conn = pymysql.connect(host=host, user=username, password=password, database=dbname, port=3306, charset='utf8')

    return conn


def get_expired_30_pr(cursor):
    query_sql = '''select id, pipeline_id, build_number, trigger_by_chinese_name, started_time, completed_time, created_at, updated_at from pipeline_run 
                    where id in(select MAX(id) as id from pipeline_run group by pipeline_id, build_number ) 
                    and DATE_SUB(CURDATE(), INTERVAL 30 DAY) > date(started_time)  
                    '''
    cursor.execute(query_sql)
    results = cursor.fetchall()

    return results

def get_succ_pr(cursor):
    query_sql = '''select id, pipeline_id, build_number, trigger_by_chinese_name, started_time, completed_time, created_at, updated_at from pipeline_run 
                    where id in(select MAX(id) as id from pipeline_run group by pipeline_id, build_number ) 
                    and DATE_SUB(CURDATE(), INTERVAL 30 DAY) < date(started_time)  and (status='SUCCESSFUL' or status='CANCEL')
                    '''
    cursor.execute(query_sql)
    results = cursor.fetchall()

    return results

class PipelineRun:
    def __init__(self, data):
        self.id = data[0]
        self.pipeline_id = data[1]
        self.build_number = data[2]
        self.trigger_by_chinese_name = data[3]
        self.started_time = data[4]
        self.completed_time = data[5]
        self.created_at = data[6]
        self.updated_at = data[7]

    def __str__(self):
        return f"PipelineRun(pipeline_id={self.pipeline_id}, build_number={self.build_number}, " \
               f"trigger_by_chinese_name={self.trigger_by_chinese_name}, started_time={self.started_time}, " \
               f"completed_time={self.completed_time}"

    def get_pv_path(self):
        root_path = "/data/csi-v2"
        _env = os.getenv("CSI_ENV")
        _env = _env.lower() if _env else "dev"
        return f"{root_path}/{_env}/{self.pipeline_id}/{self.build_number}"


def main():
    load_dotenv(verbose=True)

    conn = get_db_connection()
    cursor = conn.cursor()

    logging.info("释放30天前的 PV挂载...")
    # 数据库查询过期的流水线信息
    results = get_expired_30_pr(cursor)
    for result in results:
        pr = PipelineRun(result)
        release_expired_pv_space(pr.get_pv_path())
    
    logging.info("释放成功状态的 PV挂载...")
    results = get_succ_pr(cursor)
    for result in results:
        pr = PipelineRun(result)
        release_expired_pv_space(pr.get_pv_path())


if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        logging.info("停止清除...")
        exit(0)
