// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        (unknown)
// source: app/project.proto

package app

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Project struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *Project) Reset() {
	*x = Project{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_project_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Project) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Project) ProtoMessage() {}

func (x *Project) ProtoReflect() protoreflect.Message {
	mi := &file_app_project_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Project.ProtoReflect.Descriptor instead.
func (*Project) Descriptor() ([]byte, []int) {
	return file_app_project_proto_rawDescGZIP(), []int{0}
}

func (x *Project) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Project) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Project) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type ProjectListItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	// @gotags: json:"totalMember,omitempty"
	TotalMember int64  `protobuf:"varint,4,opt,name=total_member,json=totalMember,proto3" json:"total_member,omitempty"`
	Managers    string `protobuf:"bytes,5,opt,name=managers,proto3" json:"managers,omitempty"`
}

func (x *ProjectListItem) Reset() {
	*x = ProjectListItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_project_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectListItem) ProtoMessage() {}

func (x *ProjectListItem) ProtoReflect() protoreflect.Message {
	mi := &file_app_project_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectListItem.ProtoReflect.Descriptor instead.
func (*ProjectListItem) Descriptor() ([]byte, []int) {
	return file_app_project_proto_rawDescGZIP(), []int{1}
}

func (x *ProjectListItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ProjectListItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProjectListItem) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ProjectListItem) GetTotalMember() int64 {
	if x != nil {
		return x.TotalMember
	}
	return 0
}

func (x *ProjectListItem) GetManagers() string {
	if x != nil {
		return x.Managers
	}
	return ""
}

type ProjectList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Projects []*ProjectListItem `protobuf:"bytes,1,rep,name=projects,proto3" json:"projects,omitempty"`
}

func (x *ProjectList) Reset() {
	*x = ProjectList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_project_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectList) ProtoMessage() {}

func (x *ProjectList) ProtoReflect() protoreflect.Message {
	mi := &file_app_project_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectList.ProtoReflect.Descriptor instead.
func (*ProjectList) Descriptor() ([]byte, []int) {
	return file_app_project_proto_rawDescGZIP(), []int{2}
}

func (x *ProjectList) GetProjects() []*ProjectListItem {
	if x != nil {
		return x.Projects
	}
	return nil
}

type ProjectResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *ProjectResult) Reset() {
	*x = ProjectResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_project_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectResult) ProtoMessage() {}

func (x *ProjectResult) ProtoReflect() protoreflect.Message {
	mi := &file_app_project_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectResult.ProtoReflect.Descriptor instead.
func (*ProjectResult) Descriptor() ([]byte, []int) {
	return file_app_project_proto_rawDescGZIP(), []int{3}
}

func (x *ProjectResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ProjectParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *ProjectParam) Reset() {
	*x = ProjectParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_project_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectParam) ProtoMessage() {}

func (x *ProjectParam) ProtoReflect() protoreflect.Message {
	mi := &file_app_project_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectParam.ProtoReflect.Descriptor instead.
func (*ProjectParam) Descriptor() ([]byte, []int) {
	return file_app_project_proto_rawDescGZIP(), []int{4}
}

func (x *ProjectParam) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ProjectParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ProjectListParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"pageNum,omitempty"
	PageNum int64 `protobuf:"varint,1,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	// @gotags: json:"pageSize,omitempty"
	PageSize int64 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ProjectListParam) Reset() {
	*x = ProjectListParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_project_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectListParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectListParam) ProtoMessage() {}

func (x *ProjectListParam) ProtoReflect() protoreflect.Message {
	mi := &file_app_project_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectListParam.ProtoReflect.Descriptor instead.
func (*ProjectListParam) Descriptor() ([]byte, []int) {
	return file_app_project_proto_rawDescGZIP(), []int{5}
}

func (x *ProjectListParam) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ProjectListParam) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

var File_app_project_proto protoreflect.FileDescriptor

var file_app_project_proto_rawDesc = []byte{
	0x0a, 0x11, 0x61, 0x70, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x03, 0x61, 0x70, 0x70, 0x22, 0x4f, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x88, 0x01, 0x0a, 0x0f, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x73, 0x22, 0x3f, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x22, 0x1f, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x32, 0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x4a, 0x0a, 0x10, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x19,
	0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x32, 0x96, 0x02, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2e, 0x0a, 0x0a, 0x4e, 0x65, 0x77,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x0c, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x1a, 0x12, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x31, 0x0a, 0x0d, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x0c, 0x2e, 0x61, 0x70, 0x70,
	0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x1a, 0x12, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x39, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x15,
	0x2e, 0x61, 0x70, 0x70, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x1a, 0x10, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x42, 0x79, 0x49, 0x64, 0x12, 0x11, 0x2e, 0x61, 0x70, 0x70, 0x2e,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x1a, 0x0c, 0x2e, 0x61,
	0x70, 0x70, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x33, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x42, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x11,
	0x2e, 0x61, 0x70, 0x70, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x1a, 0x0c, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x42,
	0x2d, 0x5a, 0x2b, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69,
	0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68,
	0x61, 0x72, 0x6d, 0x6f, 0x6e, 0x79, 0x2f, 0x61, 0x70, 0x70, 0x3b, 0x61, 0x70, 0x70, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_app_project_proto_rawDescOnce sync.Once
	file_app_project_proto_rawDescData = file_app_project_proto_rawDesc
)

func file_app_project_proto_rawDescGZIP() []byte {
	file_app_project_proto_rawDescOnce.Do(func() {
		file_app_project_proto_rawDescData = protoimpl.X.CompressGZIP(file_app_project_proto_rawDescData)
	})
	return file_app_project_proto_rawDescData
}

var file_app_project_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_app_project_proto_goTypes = []interface{}{
	(*Project)(nil),          // 0: app.Project
	(*ProjectListItem)(nil),  // 1: app.ProjectListItem
	(*ProjectList)(nil),      // 2: app.ProjectList
	(*ProjectResult)(nil),    // 3: app.ProjectResult
	(*ProjectParam)(nil),     // 4: app.ProjectParam
	(*ProjectListParam)(nil), // 5: app.ProjectListParam
}
var file_app_project_proto_depIdxs = []int32{
	1, // 0: app.ProjectList.projects:type_name -> app.ProjectListItem
	0, // 1: app.ProjectService.NewProject:input_type -> app.Project
	0, // 2: app.ProjectService.DeleteProject:input_type -> app.Project
	5, // 3: app.ProjectService.GetProjectList:input_type -> app.ProjectListParam
	4, // 4: app.ProjectService.GetProjectById:input_type -> app.ProjectParam
	4, // 5: app.ProjectService.GetProjectByName:input_type -> app.ProjectParam
	3, // 6: app.ProjectService.NewProject:output_type -> app.ProjectResult
	3, // 7: app.ProjectService.DeleteProject:output_type -> app.ProjectResult
	2, // 8: app.ProjectService.GetProjectList:output_type -> app.ProjectList
	0, // 9: app.ProjectService.GetProjectById:output_type -> app.Project
	0, // 10: app.ProjectService.GetProjectByName:output_type -> app.Project
	6, // [6:11] is the sub-list for method output_type
	1, // [1:6] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_app_project_proto_init() }
func file_app_project_proto_init() {
	if File_app_project_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_app_project_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Project); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_project_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectListItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_project_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_project_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_project_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_project_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectListParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_app_project_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_app_project_proto_goTypes,
		DependencyIndexes: file_app_project_proto_depIdxs,
		MessageInfos:      file_app_project_proto_msgTypes,
	}.Build()
	File_app_project_proto = out.File
	file_app_project_proto_rawDesc = nil
	file_app_project_proto_goTypes = nil
	file_app_project_proto_depIdxs = nil
}
