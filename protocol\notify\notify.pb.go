// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        (unknown)
// source: notify/notify.proto

package notify

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 飞书消息类型
type LackMessageType int32

const (
	LackMessageType_text        LackMessageType = 0 //文本
	LackMessageType_post        LackMessageType = 1 //富文本
	LackMessageType_image       LackMessageType = 2 //图片
	LackMessageType_interactive LackMessageType = 3 //消息卡片
	LackMessageType_share_chat  LackMessageType = 4 //分享群名片
	LackMessageType_share_user  LackMessageType = 5 //分享个人名片
	LackMessageType_audio       LackMessageType = 6 //语音
	LackMessageType_media       LackMessageType = 7 //视频
	LackMessageType_file        LackMessageType = 8 //文件
	LackMessageType_sticker     LackMessageType = 9 //表情包
)

// Enum value maps for LackMessageType.
var (
	LackMessageType_name = map[int32]string{
		0: "text",
		1: "post",
		2: "image",
		3: "interactive",
		4: "share_chat",
		5: "share_user",
		6: "audio",
		7: "media",
		8: "file",
		9: "sticker",
	}
	LackMessageType_value = map[string]int32{
		"text":        0,
		"post":        1,
		"image":       2,
		"interactive": 3,
		"share_chat":  4,
		"share_user":  5,
		"audio":       6,
		"media":       7,
		"file":        8,
		"sticker":     9,
	}
)

func (x LackMessageType) Enum() *LackMessageType {
	p := new(LackMessageType)
	*p = x
	return p
}

func (x LackMessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LackMessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_notify_notify_proto_enumTypes[0].Descriptor()
}

func (LackMessageType) Type() protoreflect.EnumType {
	return &file_notify_notify_proto_enumTypes[0]
}

func (x LackMessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LackMessageType.Descriptor instead.
func (LackMessageType) EnumDescriptor() ([]byte, []int) {
	return file_notify_notify_proto_rawDescGZIP(), []int{0}
}

type LackType int32

const (
	LackType_ticket LackType = 0 //工单 需要模板渲染
	LackType_common LackType = 1 //通用 不需要渲染
)

// Enum value maps for LackType.
var (
	LackType_name = map[int32]string{
		0: "ticket",
		1: "common",
	}
	LackType_value = map[string]int32{
		"ticket": 0,
		"common": 1,
	}
)

func (x LackType) Enum() *LackType {
	p := new(LackType)
	*p = x
	return p
}

func (x LackType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LackType) Descriptor() protoreflect.EnumDescriptor {
	return file_notify_notify_proto_enumTypes[1].Descriptor()
}

func (LackType) Type() protoreflect.EnumType {
	return &file_notify_notify_proto_enumTypes[1]
}

func (x LackType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LackType.Descriptor instead.
func (LackType) EnumDescriptor() ([]byte, []int) {
	return file_notify_notify_proto_rawDescGZIP(), []int{1}
}

// 飞书消息
type LackMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type     LackMessageType        `protobuf:"varint,1,opt,name=type,proto3,enum=notify.LackMessageType" json:"type,omitempty"`
	Title    string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Content  string                 `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	UnionId  string                 `protobuf:"bytes,4,opt,name=unionId,proto3" json:"unionId,omitempty"`
	Time     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=time,proto3" json:"time,omitempty"`
	LackType LackType               `protobuf:"varint,6,opt,name=LackType,proto3,enum=notify.LackType" json:"LackType,omitempty"`
}

func (x *LackMessage) Reset() {
	*x = LackMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_notify_notify_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LackMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LackMessage) ProtoMessage() {}

func (x *LackMessage) ProtoReflect() protoreflect.Message {
	mi := &file_notify_notify_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LackMessage.ProtoReflect.Descriptor instead.
func (*LackMessage) Descriptor() ([]byte, []int) {
	return file_notify_notify_proto_rawDescGZIP(), []int{0}
}

func (x *LackMessage) GetType() LackMessageType {
	if x != nil {
		return x.Type
	}
	return LackMessageType_text
}

func (x *LackMessage) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *LackMessage) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *LackMessage) GetUnionId() string {
	if x != nil {
		return x.UnionId
	}
	return ""
}

func (x *LackMessage) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *LackMessage) GetLackType() LackType {
	if x != nil {
		return x.LackType
	}
	return LackType_ticket
}

// 延迟更新消息卡片
type ExpireMsgCardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type            LackMessageType        `protobuf:"varint,1,opt,name=type,proto3,enum=notify.LackMessageType" json:"type,omitempty"`
	Select          string                 `protobuf:"bytes,2,opt,name=select,proto3" json:"select,omitempty"`
	Time            *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=time,proto3" json:"time,omitempty"`
	RespInfo        string                 `protobuf:"bytes,4,opt,name=resp_info,json=respInfo,proto3" json:"resp_info,omitempty"`                        // 对应飞书通知消息卡片最下端提示内容
	BackGroundColor string                 `protobuf:"bytes,5,opt,name=back_ground_color,json=backGroundColor,proto3" json:"back_ground_color,omitempty"` // 根据工单审批结果展示，对应飞书通知消息卡片背景颜色
}

func (x *ExpireMsgCardMessage) Reset() {
	*x = ExpireMsgCardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_notify_notify_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpireMsgCardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpireMsgCardMessage) ProtoMessage() {}

func (x *ExpireMsgCardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_notify_notify_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpireMsgCardMessage.ProtoReflect.Descriptor instead.
func (*ExpireMsgCardMessage) Descriptor() ([]byte, []int) {
	return file_notify_notify_proto_rawDescGZIP(), []int{1}
}

func (x *ExpireMsgCardMessage) GetType() LackMessageType {
	if x != nil {
		return x.Type
	}
	return LackMessageType_text
}

func (x *ExpireMsgCardMessage) GetSelect() string {
	if x != nil {
		return x.Select
	}
	return ""
}

func (x *ExpireMsgCardMessage) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *ExpireMsgCardMessage) GetRespInfo() string {
	if x != nil {
		return x.RespInfo
	}
	return ""
}

func (x *ExpireMsgCardMessage) GetBackGroundColor() string {
	if x != nil {
		return x.BackGroundColor
	}
	return ""
}

// 邮件消息
type EmailMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 消息主题
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// 消息内容
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	// 接收者
	Receiver string `protobuf:"bytes,3,opt,name=receiver,proto3" json:"receiver,omitempty"`
	// 抄送
	Cc string `protobuf:"bytes,4,opt,name=cc,proto3" json:"cc,omitempty"`
}

func (x *EmailMessage) Reset() {
	*x = EmailMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_notify_notify_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailMessage) ProtoMessage() {}

func (x *EmailMessage) ProtoReflect() protoreflect.Message {
	mi := &file_notify_notify_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailMessage.ProtoReflect.Descriptor instead.
func (*EmailMessage) Descriptor() ([]byte, []int) {
	return file_notify_notify_proto_rawDescGZIP(), []int{2}
}

func (x *EmailMessage) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *EmailMessage) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *EmailMessage) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

func (x *EmailMessage) GetCc() string {
	if x != nil {
		return x.Cc
	}
	return ""
}

// 消息通知返回模型
type Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Err    string `protobuf:"bytes,2,opt,name=err,proto3" json:"err,omitempty"`
}

func (x *Result) Reset() {
	*x = Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_notify_notify_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Result) ProtoMessage() {}

func (x *Result) ProtoReflect() protoreflect.Message {
	mi := &file_notify_notify_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Result.ProtoReflect.Descriptor instead.
func (*Result) Descriptor() ([]byte, []int) {
	return file_notify_notify_proto_rawDescGZIP(), []int{3}
}

func (x *Result) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Result) GetErr() string {
	if x != nil {
		return x.Err
	}
	return ""
}

var File_notify_notify_proto protoreflect.FileDescriptor

var file_notify_notify_proto_rawDesc = []byte{
	0x0a, 0x13, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe2,
	0x01, 0x0a, 0x0b, 0x4c, 0x61, 0x63, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2b,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x4c, 0x61, 0x63, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x75,
	0x6e, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x6e,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x4c, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x2e, 0x4c, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x4c, 0x61, 0x63, 0x6b, 0x54,
	0x79, 0x70, 0x65, 0x22, 0xd4, 0x01, 0x0a, 0x14, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x4d, 0x73,
	0x67, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2b, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x2e, 0x4c, 0x61, 0x63, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a,
	0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x47,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x6a, 0x0a, 0x0c, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x63, 0x63, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x63, 0x63, 0x22, 0x32, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x72, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x72, 0x72, 0x2a, 0x8e, 0x01, 0x0a, 0x0f, 0x4c,
	0x61, 0x63, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08,
	0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x70, 0x6f, 0x73, 0x74,
	0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x10, 0x02, 0x12, 0x0f, 0x0a,
	0x0b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x10, 0x03, 0x12, 0x0e,
	0x0a, 0x0a, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x10, 0x04, 0x12, 0x0e,
	0x0a, 0x0a, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x10, 0x05, 0x12, 0x09,
	0x0a, 0x05, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x10, 0x06, 0x12, 0x09, 0x0a, 0x05, 0x6d, 0x65, 0x64,
	0x69, 0x61, 0x10, 0x07, 0x12, 0x08, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x10, 0x08, 0x12, 0x0b,
	0x0a, 0x07, 0x73, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x72, 0x10, 0x09, 0x2a, 0x22, 0x0a, 0x08, 0x4c,
	0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x10, 0x01, 0x32,
	0xab, 0x01, 0x0a, 0x06, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x31, 0x0a, 0x09, 0x53, 0x65,
	0x6e, 0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x14, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x0e, 0x2e,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2f, 0x0a,
	0x08, 0x53, 0x65, 0x6e, 0x64, 0x4c, 0x61, 0x63, 0x6b, 0x12, 0x13, 0x2e, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x2e, 0x4c, 0x61, 0x63, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x0e,
	0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3d,
	0x0a, 0x0d, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x4d, 0x73, 0x67, 0x43, 0x61, 0x72, 0x64, 0x12,
	0x1c, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x4d,
	0x73, 0x67, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x0e, 0x2e,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x33, 0x5a,
	0x31, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x61, 0x72,
	0x6d, 0x6f, 0x6e, 0x79, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x3b, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_notify_notify_proto_rawDescOnce sync.Once
	file_notify_notify_proto_rawDescData = file_notify_notify_proto_rawDesc
)

func file_notify_notify_proto_rawDescGZIP() []byte {
	file_notify_notify_proto_rawDescOnce.Do(func() {
		file_notify_notify_proto_rawDescData = protoimpl.X.CompressGZIP(file_notify_notify_proto_rawDescData)
	})
	return file_notify_notify_proto_rawDescData
}

var file_notify_notify_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_notify_notify_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_notify_notify_proto_goTypes = []interface{}{
	(LackMessageType)(0),          // 0: notify.LackMessageType
	(LackType)(0),                 // 1: notify.LackType
	(*LackMessage)(nil),           // 2: notify.LackMessage
	(*ExpireMsgCardMessage)(nil),  // 3: notify.ExpireMsgCardMessage
	(*EmailMessage)(nil),          // 4: notify.EmailMessage
	(*Result)(nil),                // 5: notify.Result
	(*timestamppb.Timestamp)(nil), // 6: google.protobuf.Timestamp
}
var file_notify_notify_proto_depIdxs = []int32{
	0, // 0: notify.LackMessage.type:type_name -> notify.LackMessageType
	6, // 1: notify.LackMessage.time:type_name -> google.protobuf.Timestamp
	1, // 2: notify.LackMessage.LackType:type_name -> notify.LackType
	0, // 3: notify.ExpireMsgCardMessage.type:type_name -> notify.LackMessageType
	6, // 4: notify.ExpireMsgCardMessage.time:type_name -> google.protobuf.Timestamp
	4, // 5: notify.Notify.SendEmail:input_type -> notify.EmailMessage
	2, // 6: notify.Notify.SendLack:input_type -> notify.LackMessage
	3, // 7: notify.Notify.ExpireMsgCard:input_type -> notify.ExpireMsgCardMessage
	5, // 8: notify.Notify.SendEmail:output_type -> notify.Result
	5, // 9: notify.Notify.SendLack:output_type -> notify.Result
	5, // 10: notify.Notify.ExpireMsgCard:output_type -> notify.Result
	8, // [8:11] is the sub-list for method output_type
	5, // [5:8] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_notify_notify_proto_init() }
func file_notify_notify_proto_init() {
	if File_notify_notify_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_notify_notify_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LackMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_notify_notify_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpireMsgCardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_notify_notify_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmailMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_notify_notify_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_notify_notify_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_notify_notify_proto_goTypes,
		DependencyIndexes: file_notify_notify_proto_depIdxs,
		EnumInfos:         file_notify_notify_proto_enumTypes,
		MessageInfos:      file_notify_notify_proto_msgTypes,
	}.Build()
	File_notify_notify_proto = out.File
	file_notify_notify_proto_rawDesc = nil
	file_notify_notify_proto_goTypes = nil
	file_notify_notify_proto_depIdxs = nil
}
