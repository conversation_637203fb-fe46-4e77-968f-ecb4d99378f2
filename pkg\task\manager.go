package task

import (
	"context"
	"path"
	"sync"

	"github.com/robfig/cron/v3"
	"golang.ttyuyin.com/harmony/csi-driver/pkg/repo"
	"golang.ttyuyin.com/harmony/pkg/log"
	"golang.ttyuyin.com/harmony/pkg/timex"
)

type Handler interface {
	Handle(ctx context.Context, rootPath, url, branch string)
	RootPath() string
	Repos() []Params
}

func NewManager(lowerRootDir, lowerDirVersion string) *Manager {
	m := &Manager{
		lowerRootDir:    lowerRootDir,
		lowerDirVersion: lowerDirVersion,
		localCache:      make(map[string]*repo.Manager),
	}
	dateString := GetCurrentDateString()

	m.localCache[dateString] = repo.NewManager(path.Join(lowerRootDir, lowerDirVersion, dateString))
	return m
}

type Manager struct {
	mu sync.Mutex

	lowerRootDir    string
	lowerDirVersion string
	// dateString: [repoURL: first cloneDir]
	localCache map[string]*repo.Manager
}

func (m *Manager) Handle(ctx context.Context, rootPath, url, branch string) {
	repository := repo.New(url, rootPath)
	repository.SetBranch(branch)

	managerCache := m.currentManager()
	managerCache.Clone(ctx, url, branch)
}

func (m *Manager) currentManager() *repo.Manager {
	var (
		repoM *repo.Manager
		ok    bool
	)

	m.mu.Lock()
	defer m.mu.Unlock()
	repoM, ok = m.localCache[GetCurrentDateString()]
	if !ok {
		dateString := GetCurrentDateString()
		repoM = repo.NewManager(path.Join(m.lowerRootDir, m.lowerDirVersion, dateString))
		m.localCache[dateString] = repoM
	}

	return repoM
}

func (m *Manager) CurrentManager() *repo.Manager {
	return m.currentManager()
}

func (m *Manager) GetCurrentLowerDirRootPath() string {
	return path.Join(m.lowerRootDir, m.lowerDirVersion, GetCurrentDateString())
}

func (m *Manager) GetNextLowerDirRootPath() string {
	return path.Join(m.lowerRootDir, m.lowerDirVersion, GetNextDateString())
}

func NewCronManager() *CronManager {
	return &CronManager{
		c: cron.New(cron.WithSeconds()),
	}
}

type CronManager struct {
	c  *cron.Cron
	mu sync.Mutex
}

func (m *CronManager) Start() {
	m.c.Start()
}

func (m *CronManager) Stop() {
	m.c.Stop()
}

func (m *CronManager) handle(ctx context.Context, handler Handler) {
	m.mu.Lock()
	defer m.mu.Unlock()

	for _, param := range handler.Repos() {
		log.Infof("start clone the repo(%s) branch(%s)", param.RepoURL, param.Branch)
		handler.Handle(ctx, handler.RootPath(), param.RepoURL, param.Branch)
	}
}

func (m *CronManager) RegisterCronSpec(spec string, h Handler) (cron.EntryID, error) {
	log.Infof("register cron spec `%s`", spec)

	return m.c.AddFunc(spec, func() {
		log.Infof("start handle cron spec `%s`", spec)

		m.handle(context.Background(), h)
	})
}

type Params struct {
	RepoURL string
	Branch  string
}

func GetNextDateString() string {
	return timex.DefaultTimeFunc().AddDate(0, 1, 0).Format("2006-01")
}

func GetCurrentDateString() string {
	return timex.DefaultTimeFunc().Format("2006-01")
}
