// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        (unknown)
// source: pipeline/pipeline_run.proto

package pipeline

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetPRStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: uri:"id" binding:"required"
	Rid int64 `protobuf:"varint,1,opt,name=rid,proto3" json:"rid,omitempty"`
}

func (x *GetPRStatusReq) Reset() {
	*x = GetPRStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPRStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPRStatusReq) ProtoMessage() {}

func (x *GetPRStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPRStatusReq.ProtoReflect.Descriptor instead.
func (*GetPRStatusReq) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{0}
}

func (x *GetPRStatusReq) GetRid() int64 {
	if x != nil {
		return x.Rid
	}
	return 0
}

type TaskStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *TaskStatus) Reset() {
	*x = TaskStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskStatus) ProtoMessage() {}

func (x *TaskStatus) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskStatus.ProtoReflect.Descriptor instead.
func (*TaskStatus) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{1}
}

func (x *TaskStatus) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TaskStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type UpdateStatusResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *UpdateStatusResp) Reset() {
	*x = UpdateStatusResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStatusResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStatusResp) ProtoMessage() {}

func (x *UpdateStatusResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStatusResp.ProtoReflect.Descriptor instead.
func (*UpdateStatusResp) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateStatusResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateStatusResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// new message named TaskRunReq with fields repeated int64 ids
type TaskRunReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *TaskRunReq) Reset() {
	*x = TaskRunReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskRunReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskRunReq) ProtoMessage() {}

func (x *TaskRunReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskRunReq.ProtoReflect.Descriptor instead.
func (*TaskRunReq) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{3}
}

func (x *TaskRunReq) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type TaskRunListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"pipelineRunList"
	PipelineRunIdList []int64 `protobuf:"varint,1,rep,packed,name=pipeline_run_id_list,json=pipelineRunIdList,proto3" json:"pipeline_run_id_list,omitempty"`
	// @gotags: json:"types"
	Types []string `protobuf:"bytes,2,rep,name=types,proto3" json:"types,omitempty"`
}

func (x *TaskRunListReq) Reset() {
	*x = TaskRunListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskRunListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskRunListReq) ProtoMessage() {}

func (x *TaskRunListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskRunListReq.ProtoReflect.Descriptor instead.
func (*TaskRunListReq) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{4}
}

func (x *TaskRunListReq) GetPipelineRunIdList() []int64 {
	if x != nil {
		return x.PipelineRunIdList
	}
	return nil
}

func (x *TaskRunListReq) GetTypes() []string {
	if x != nil {
		return x.Types
	}
	return nil
}

// new message named TaskRunResp with fields repeated TaskRun tasks
type TaskRunResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tasks []*TaskRun `protobuf:"bytes,1,rep,name=tasks,proto3" json:"tasks,omitempty"`
}

func (x *TaskRunResp) Reset() {
	*x = TaskRunResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskRunResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskRunResp) ProtoMessage() {}

func (x *TaskRunResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskRunResp.ProtoReflect.Descriptor instead.
func (*TaskRunResp) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{5}
}

func (x *TaskRunResp) GetTasks() []*TaskRun {
	if x != nil {
		return x.Tasks
	}
	return nil
}

// new message named TaskRun with fields, int64 id, string name, string status,string type
type TaskRun struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int64            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string           `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Status        string           `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	Type          string           `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	Config        *structpb.Struct `protobuf:"bytes,5,opt,name=config,proto3" json:"config,omitempty"`
	PipelineRunId int64            `protobuf:"varint,6,opt,name=pipeline_run_id,json=pipelineRunId,proto3" json:"pipeline_run_id,omitempty"`
	// @gotags: json:"tektonName"
	TektonName string `protobuf:"bytes,7,opt,name=tekton_name,json=tektonName,proto3" json:"tekton_name,omitempty"`
}

func (x *TaskRun) Reset() {
	*x = TaskRun{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskRun) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskRun) ProtoMessage() {}

func (x *TaskRun) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskRun.ProtoReflect.Descriptor instead.
func (*TaskRun) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{6}
}

func (x *TaskRun) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TaskRun) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TaskRun) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *TaskRun) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TaskRun) GetConfig() *structpb.Struct {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *TaskRun) GetPipelineRunId() int64 {
	if x != nil {
		return x.PipelineRunId
	}
	return 0
}

func (x *TaskRun) GetTektonName() string {
	if x != nil {
		return x.TektonName
	}
	return ""
}

type GetPRStatusResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// @gotags: json:"startedTime"
	StartedTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=started_time,json=startedTime,proto3" json:"started_time,omitempty"`
	// @gotags: json:"completedTime"
	CompletedTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=completed_time,json=completedTime,proto3" json:"completed_time,omitempty"`
	// @gotags: json:"elapsedTime"
	ElapsedTime *durationpb.Duration     `protobuf:"bytes,5,opt,name=elapsed_time,json=elapsedTime,proto3" json:"elapsed_time,omitempty"`
	Stages      []*GetPRStatusResp_Stage `protobuf:"bytes,6,rep,name=stages,proto3" json:"stages,omitempty"`
	Status      string                   `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	Branch      string                   `protobuf:"bytes,8,opt,name=Branch,proto3" json:"Branch,omitempty"`
	// @gotags: json:"buildNumber"
	BuildNumber int64 `protobuf:"varint,9,opt,name=build_number,json=buildNumber,proto3" json:"build_number,omitempty"`
	// @gotags: json:"pipelineId"
	PipelineId int64 `protobuf:"varint,10,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id,omitempty"`
	// @gotags: json:"changeSetId"
	ChangeSetId int64 `protobuf:"varint,11,opt,name=change_set_id,json=changeSetId,proto3" json:"change_set_id,omitempty"`
	// @gotags: json:"hasTestApprovalTask"
	HasTestApprovalTask bool `protobuf:"varint,12,opt,name=has_test_approval_task,json=hasTestApprovalTask,proto3" json:"has_test_approval_task,omitempty"`
	// @gotags: json:"hasUpgradeApprovalTask"
	HasUpgradeApprovalTask bool `protobuf:"varint,13,opt,name=has_upgrade_approval_task,json=hasUpgradeApprovalTask,proto3" json:"has_upgrade_approval_task,omitempty"`
	// @gotags: json:"hasGrayUpgradeApprovalTask"
	HasGrayUpgradeApprovalTask bool `protobuf:"varint,14,opt,name=has_gray_upgrade_approval_task,json=hasGrayUpgradeApprovalTask,proto3" json:"has_gray_upgrade_approval_task,omitempty"`
}

func (x *GetPRStatusResp) Reset() {
	*x = GetPRStatusResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPRStatusResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPRStatusResp) ProtoMessage() {}

func (x *GetPRStatusResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPRStatusResp.ProtoReflect.Descriptor instead.
func (*GetPRStatusResp) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{7}
}

func (x *GetPRStatusResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPRStatusResp) GetStartedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedTime
	}
	return nil
}

func (x *GetPRStatusResp) GetCompletedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedTime
	}
	return nil
}

func (x *GetPRStatusResp) GetElapsedTime() *durationpb.Duration {
	if x != nil {
		return x.ElapsedTime
	}
	return nil
}

func (x *GetPRStatusResp) GetStages() []*GetPRStatusResp_Stage {
	if x != nil {
		return x.Stages
	}
	return nil
}

func (x *GetPRStatusResp) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetPRStatusResp) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *GetPRStatusResp) GetBuildNumber() int64 {
	if x != nil {
		return x.BuildNumber
	}
	return 0
}

func (x *GetPRStatusResp) GetPipelineId() int64 {
	if x != nil {
		return x.PipelineId
	}
	return 0
}

func (x *GetPRStatusResp) GetChangeSetId() int64 {
	if x != nil {
		return x.ChangeSetId
	}
	return 0
}

func (x *GetPRStatusResp) GetHasTestApprovalTask() bool {
	if x != nil {
		return x.HasTestApprovalTask
	}
	return false
}

func (x *GetPRStatusResp) GetHasUpgradeApprovalTask() bool {
	if x != nil {
		return x.HasUpgradeApprovalTask
	}
	return false
}

func (x *GetPRStatusResp) GetHasGrayUpgradeApprovalTask() bool {
	if x != nil {
		return x.HasGrayUpgradeApprovalTask
	}
	return false
}

type GetPRTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: uri:"id" binding:"required" json:"taskRunId"
	Tid int64 `protobuf:"varint,1,opt,name=tid,proto3" json:"tid,omitempty"`
}

func (x *GetPRTaskReq) Reset() {
	*x = GetPRTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPRTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPRTaskReq) ProtoMessage() {}

func (x *GetPRTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPRTaskReq.ProtoReflect.Descriptor instead.
func (*GetPRTaskReq) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{8}
}

func (x *GetPRTaskReq) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

type TaskRunQueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"stageRunId"
	StageRunId int64  `protobuf:"varint,1,opt,name=stage_run_id,json=stageRunId,proto3" json:"stage_run_id,omitempty"`
	Type       string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *TaskRunQueryReq) Reset() {
	*x = TaskRunQueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskRunQueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskRunQueryReq) ProtoMessage() {}

func (x *TaskRunQueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskRunQueryReq.ProtoReflect.Descriptor instead.
func (*TaskRunQueryReq) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{9}
}

func (x *TaskRunQueryReq) GetStageRunId() int64 {
	if x != nil {
		return x.StageRunId
	}
	return 0
}

func (x *TaskRunQueryReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type GetPRTaskResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// @gotags: json:"startedTime"
	StartedTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=started_time,json=startedTime,proto3" json:"started_time,omitempty"`
	// @gotags: json:"completedTime"
	CompletedTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=completed_time,json=completedTime,proto3" json:"completed_time,omitempty"`
	// @gotags: json:"elapsedTime"
	ElapsedTime *durationpb.Duration `protobuf:"bytes,5,opt,name=elapsed_time,json=elapsedTime,proto3" json:"elapsed_time,omitempty"`
	// @gotags: json:"tektonName"
	TektonName string `protobuf:"bytes,6,opt,name=tekton_name,json=tektonName,proto3" json:"tekton_name,omitempty"`
	// @gotags: json:"stageRunId"
	StageRunId int64 `protobuf:"varint,7,opt,name=stage_run_id,json=stageRunId,proto3" json:"stage_run_id,omitempty"`
	// @gotags: json:"taskId"
	TaskId int64  `protobuf:"varint,8,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Config []byte `protobuf:"bytes,9,opt,name=config,proto3" json:"config,omitempty"`
	// @gotags: json:"pipelineRunId"
	PipelineRunId       int64  `protobuf:"varint,10,opt,name=pipeline_run_id,json=pipelineRunId,proto3" json:"pipeline_run_id,omitempty"`
	Status              string `protobuf:"bytes,11,opt,name=status,proto3" json:"status,omitempty"`
	PipelineBuildNumber int64  `protobuf:"varint,12,opt,name=pipeline_build_number,json=pipelineBuildNumber,proto3" json:"pipeline_build_number,omitempty"`
	PipelineId          int64  `protobuf:"varint,13,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id,omitempty"`
	Type                string `protobuf:"bytes,14,opt,name=type,proto3" json:"type,omitempty"`
	// @gotags: json:"changeSetId"
	ChangeSetId int64 `protobuf:"varint,15,opt,name=change_set_id,json=changeSetId,proto3" json:"change_set_id,omitempty"`
}

func (x *GetPRTaskResp) Reset() {
	*x = GetPRTaskResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPRTaskResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPRTaskResp) ProtoMessage() {}

func (x *GetPRTaskResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPRTaskResp.ProtoReflect.Descriptor instead.
func (*GetPRTaskResp) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{10}
}

func (x *GetPRTaskResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPRTaskResp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetPRTaskResp) GetStartedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedTime
	}
	return nil
}

func (x *GetPRTaskResp) GetCompletedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedTime
	}
	return nil
}

func (x *GetPRTaskResp) GetElapsedTime() *durationpb.Duration {
	if x != nil {
		return x.ElapsedTime
	}
	return nil
}

func (x *GetPRTaskResp) GetTektonName() string {
	if x != nil {
		return x.TektonName
	}
	return ""
}

func (x *GetPRTaskResp) GetStageRunId() int64 {
	if x != nil {
		return x.StageRunId
	}
	return 0
}

func (x *GetPRTaskResp) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *GetPRTaskResp) GetConfig() []byte {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *GetPRTaskResp) GetPipelineRunId() int64 {
	if x != nil {
		return x.PipelineRunId
	}
	return 0
}

func (x *GetPRTaskResp) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetPRTaskResp) GetPipelineBuildNumber() int64 {
	if x != nil {
		return x.PipelineBuildNumber
	}
	return 0
}

func (x *GetPRTaskResp) GetPipelineId() int64 {
	if x != nil {
		return x.PipelineId
	}
	return 0
}

func (x *GetPRTaskResp) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetPRTaskResp) GetChangeSetId() int64 {
	if x != nil {
		return x.ChangeSetId
	}
	return 0
}

type GetPRTaskRespList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tasks []*GetPRTaskResp `protobuf:"bytes,1,rep,name=tasks,proto3" json:"tasks,omitempty"`
}

func (x *GetPRTaskRespList) Reset() {
	*x = GetPRTaskRespList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPRTaskRespList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPRTaskRespList) ProtoMessage() {}

func (x *GetPRTaskRespList) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPRTaskRespList.ProtoReflect.Descriptor instead.
func (*GetPRTaskRespList) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{11}
}

func (x *GetPRTaskRespList) GetTasks() []*GetPRTaskResp {
	if x != nil {
		return x.Tasks
	}
	return nil
}

type GetPipelineRunResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PipelineRunId int64 `protobuf:"varint,1,opt,name=pipelineRunId,proto3" json:"pipelineRunId,omitempty"`
}

func (x *GetPipelineRunResp) Reset() {
	*x = GetPipelineRunResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPipelineRunResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPipelineRunResp) ProtoMessage() {}

func (x *GetPipelineRunResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPipelineRunResp.ProtoReflect.Descriptor instead.
func (*GetPipelineRunResp) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{12}
}

func (x *GetPipelineRunResp) GetPipelineRunId() int64 {
	if x != nil {
		return x.PipelineRunId
	}
	return 0
}

type PushImageTaskResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtifactVersion string `protobuf:"bytes,1,opt,name=artifactVersion,proto3" json:"artifactVersion,omitempty"`
}

func (x *PushImageTaskResp) Reset() {
	*x = PushImageTaskResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushImageTaskResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushImageTaskResp) ProtoMessage() {}

func (x *PushImageTaskResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushImageTaskResp.ProtoReflect.Descriptor instead.
func (*PushImageTaskResp) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{13}
}

func (x *PushImageTaskResp) GetArtifactVersion() string {
	if x != nil {
		return x.ArtifactVersion
	}
	return ""
}

type TaskRunRecordsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"taskRuns"
	TaskRuns []*TaskRunRecordsResp_TaskRunRecord `protobuf:"bytes,1,rep,name=task_runs,json=taskRuns,proto3" json:"task_runs,omitempty"`
}

func (x *TaskRunRecordsResp) Reset() {
	*x = TaskRunRecordsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskRunRecordsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskRunRecordsResp) ProtoMessage() {}

func (x *TaskRunRecordsResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskRunRecordsResp.ProtoReflect.Descriptor instead.
func (*TaskRunRecordsResp) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{14}
}

func (x *TaskRunRecordsResp) GetTaskRuns() []*TaskRunRecordsResp_TaskRunRecord {
	if x != nil {
		return x.TaskRuns
	}
	return nil
}

type NewApprovalTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskRunId   int64  `protobuf:"varint,1,opt,name=task_run_id,json=taskRunId,proto3" json:"task_run_id,omitempty"`
	UserId      int64  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	EmployeeNo  string `protobuf:"bytes,3,opt,name=employee_no,json=employeeNo,proto3" json:"employee_no,omitempty"`
	ChineseName string `protobuf:"bytes,4,opt,name=chinese_name,json=chineseName,proto3" json:"chinese_name,omitempty"`
}

func (x *NewApprovalTaskReq) Reset() {
	*x = NewApprovalTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewApprovalTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewApprovalTaskReq) ProtoMessage() {}

func (x *NewApprovalTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewApprovalTaskReq.ProtoReflect.Descriptor instead.
func (*NewApprovalTaskReq) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{15}
}

func (x *NewApprovalTaskReq) GetTaskRunId() int64 {
	if x != nil {
		return x.TaskRunId
	}
	return 0
}

func (x *NewApprovalTaskReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *NewApprovalTaskReq) GetEmployeeNo() string {
	if x != nil {
		return x.EmployeeNo
	}
	return ""
}

func (x *NewApprovalTaskReq) GetChineseName() string {
	if x != nil {
		return x.ChineseName
	}
	return ""
}

type NewApprovalTaskResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskRunId int64 `protobuf:"varint,1,opt,name=taskRunId,proto3" json:"taskRunId,omitempty"`
}

func (x *NewApprovalTaskResp) Reset() {
	*x = NewApprovalTaskResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewApprovalTaskResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewApprovalTaskResp) ProtoMessage() {}

func (x *NewApprovalTaskResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewApprovalTaskResp.ProtoReflect.Descriptor instead.
func (*NewApprovalTaskResp) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{16}
}

func (x *NewApprovalTaskResp) GetTaskRunId() int64 {
	if x != nil {
		return x.TaskRunId
	}
	return 0
}

type TaskTimeoutReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PipelineRunId int64 `protobuf:"varint,1,opt,name=pipeline_run_id,json=pipelineRunId,proto3" json:"pipeline_run_id,omitempty"`
	TaskRunId     int64 `protobuf:"varint,2,opt,name=task_run_id,json=taskRunId,proto3" json:"task_run_id,omitempty"`
	TicketId      int64 `protobuf:"varint,3,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
}

func (x *TaskTimeoutReq) Reset() {
	*x = TaskTimeoutReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskTimeoutReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskTimeoutReq) ProtoMessage() {}

func (x *TaskTimeoutReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskTimeoutReq.ProtoReflect.Descriptor instead.
func (*TaskTimeoutReq) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{17}
}

func (x *TaskTimeoutReq) GetPipelineRunId() int64 {
	if x != nil {
		return x.PipelineRunId
	}
	return 0
}

func (x *TaskTimeoutReq) GetTaskRunId() int64 {
	if x != nil {
		return x.TaskRunId
	}
	return 0
}

func (x *TaskTimeoutReq) GetTicketId() int64 {
	if x != nil {
		return x.TicketId
	}
	return 0
}

type TaskTimeoutResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CanRetry     bool   `protobuf:"varint,1,opt,name=can_retry,json=canRetry,proto3" json:"can_retry,omitempty"`
	RetryTimeOut string `protobuf:"bytes,2,opt,name=retry_time_out,json=retryTimeOut,proto3" json:"retry_time_out,omitempty"`
	Days         string `protobuf:"bytes,3,opt,name=days,proto3" json:"days,omitempty"`
	ExpireDate   string `protobuf:"bytes,4,opt,name=expire_date,json=expireDate,proto3" json:"expire_date,omitempty"`
}

func (x *TaskTimeoutResp) Reset() {
	*x = TaskTimeoutResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskTimeoutResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskTimeoutResp) ProtoMessage() {}

func (x *TaskTimeoutResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskTimeoutResp.ProtoReflect.Descriptor instead.
func (*TaskTimeoutResp) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{18}
}

func (x *TaskTimeoutResp) GetCanRetry() bool {
	if x != nil {
		return x.CanRetry
	}
	return false
}

func (x *TaskTimeoutResp) GetRetryTimeOut() string {
	if x != nil {
		return x.RetryTimeOut
	}
	return ""
}

func (x *TaskTimeoutResp) GetDays() string {
	if x != nil {
		return x.Days
	}
	return ""
}

func (x *TaskTimeoutResp) GetExpireDate() string {
	if x != nil {
		return x.ExpireDate
	}
	return ""
}

type ListPRTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *ListPRTaskReq) Reset() {
	*x = ListPRTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPRTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPRTaskReq) ProtoMessage() {}

func (x *ListPRTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPRTaskReq.ProtoReflect.Descriptor instead.
func (*ListPRTaskReq) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{19}
}

func (x *ListPRTaskReq) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type ListPRTaskResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tasks []*GetPRTaskResp `protobuf:"bytes,1,rep,name=tasks,proto3" json:"tasks,omitempty"`
}

func (x *ListPRTaskResp) Reset() {
	*x = ListPRTaskResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPRTaskResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPRTaskResp) ProtoMessage() {}

func (x *ListPRTaskResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPRTaskResp.ProtoReflect.Descriptor instead.
func (*ListPRTaskResp) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{20}
}

func (x *ListPRTaskResp) GetTasks() []*GetPRTaskResp {
	if x != nil {
		return x.Tasks
	}
	return nil
}

type Env struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cluster   string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Senv      string `protobuf:"bytes,3,opt,name=senv,proto3" json:"senv,omitempty"`
	EnvTarget string `protobuf:"bytes,4,opt,name=env_target,json=envTarget,proto3" json:"env_target,omitempty"`
	Env       string `protobuf:"bytes,5,opt,name=env,proto3" json:"env,omitempty"`
	ConfigId  int64  `protobuf:"varint,6,opt,name=config_id,json=configID,proto3" json:"config_id,omitempty"`
	// @gotags:json:"offlineTaskRunID"
	OfflineTaskRunId int64 `protobuf:"varint,7,opt,name=offline_task_run_id,json=offlineTaskRunID,proto3" json:"offline_task_run_id,omitempty"`
	// @gotags:json:"offlineSubtaskRunID"
	OfflineSubtaskRunId int64 `protobuf:"varint,8,opt,name=offline_subtask_run_id,json=offlineSubtaskRunID,proto3" json:"offline_subtask_run_id,omitempty"`
}

func (x *Env) Reset() {
	*x = Env{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Env) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Env) ProtoMessage() {}

func (x *Env) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Env.ProtoReflect.Descriptor instead.
func (*Env) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{21}
}

func (x *Env) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *Env) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *Env) GetSenv() string {
	if x != nil {
		return x.Senv
	}
	return ""
}

func (x *Env) GetEnvTarget() string {
	if x != nil {
		return x.EnvTarget
	}
	return ""
}

func (x *Env) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *Env) GetConfigId() int64 {
	if x != nil {
		return x.ConfigId
	}
	return 0
}

func (x *Env) GetOfflineTaskRunId() int64 {
	if x != nil {
		return x.OfflineTaskRunId
	}
	return 0
}

func (x *Env) GetOfflineSubtaskRunId() int64 {
	if x != nil {
		return x.OfflineSubtaskRunId
	}
	return 0
}

type Operator struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// @gotags:json:"chineseName"
	ChineseName string `protobuf:"bytes,2,opt,name=chinese_name,json=chineseName,proto3" json:"chinese_name,omitempty"`
	// @gotags:json:"employeeNo"
	EmployeeNo string `protobuf:"bytes,3,opt,name=employee_no,json=employeeNo,proto3" json:"employee_no,omitempty"`
}

func (x *Operator) Reset() {
	*x = Operator{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Operator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Operator) ProtoMessage() {}

func (x *Operator) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Operator.ProtoReflect.Descriptor instead.
func (*Operator) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{22}
}

func (x *Operator) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Operator) GetChineseName() string {
	if x != nil {
		return x.ChineseName
	}
	return ""
}

func (x *Operator) GetEmployeeNo() string {
	if x != nil {
		return x.EmployeeNo
	}
	return ""
}

type LastRetryPipelineRunOfflineCanarySubTasksResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId    int64     `protobuf:"varint,1,opt,name=app_id,json=appID,proto3" json:"app_id,omitempty"`
	AppName  string    `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	ImageUrl string    `protobuf:"bytes,3,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	Branch   string    `protobuf:"bytes,4,opt,name=branch,proto3" json:"branch,omitempty"`
	Envs     []*Env    `protobuf:"bytes,5,rep,name=envs,proto3" json:"envs,omitempty"`
	Operator *Operator `protobuf:"bytes,6,opt,name=operator,proto3" json:"operator,omitempty"`
}

func (x *LastRetryPipelineRunOfflineCanarySubTasksResp) Reset() {
	*x = LastRetryPipelineRunOfflineCanarySubTasksResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LastRetryPipelineRunOfflineCanarySubTasksResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LastRetryPipelineRunOfflineCanarySubTasksResp) ProtoMessage() {}

func (x *LastRetryPipelineRunOfflineCanarySubTasksResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LastRetryPipelineRunOfflineCanarySubTasksResp.ProtoReflect.Descriptor instead.
func (*LastRetryPipelineRunOfflineCanarySubTasksResp) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{23}
}

func (x *LastRetryPipelineRunOfflineCanarySubTasksResp) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *LastRetryPipelineRunOfflineCanarySubTasksResp) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *LastRetryPipelineRunOfflineCanarySubTasksResp) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *LastRetryPipelineRunOfflineCanarySubTasksResp) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *LastRetryPipelineRunOfflineCanarySubTasksResp) GetEnvs() []*Env {
	if x != nil {
		return x.Envs
	}
	return nil
}

func (x *LastRetryPipelineRunOfflineCanarySubTasksResp) GetOperator() *Operator {
	if x != nil {
		return x.Operator
	}
	return nil
}

// 获取流水线运行 git repo top k 统计请求
type GetPipelineRunGitRepoTopKRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 仓库地址
	RepoAddr string `protobuf:"bytes,1,opt,name=repo_addr,json=repoAddr,proto3" json:"repo_addr,omitempty"`
	// 开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// top k
	TopK int32 `protobuf:"varint,4,opt,name=top_k,json=topK,proto3" json:"top_k,omitempty"`
}

func (x *GetPipelineRunGitRepoTopKRequest) Reset() {
	*x = GetPipelineRunGitRepoTopKRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPipelineRunGitRepoTopKRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPipelineRunGitRepoTopKRequest) ProtoMessage() {}

func (x *GetPipelineRunGitRepoTopKRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPipelineRunGitRepoTopKRequest.ProtoReflect.Descriptor instead.
func (*GetPipelineRunGitRepoTopKRequest) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{24}
}

func (x *GetPipelineRunGitRepoTopKRequest) GetRepoAddr() string {
	if x != nil {
		return x.RepoAddr
	}
	return ""
}

func (x *GetPipelineRunGitRepoTopKRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *GetPipelineRunGitRepoTopKRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *GetPipelineRunGitRepoTopKRequest) GetTopK() int32 {
	if x != nil {
		return x.TopK
	}
	return 0
}

// 获取流水线运行 git repo top k 统计响应
type GetPipelineRunGitRepoTopKResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分支统计列表
	Branches []*GetPipelineRunGitRepoTopKResponse_BranchStat `protobuf:"bytes,1,rep,name=branches,proto3" json:"branches,omitempty"`
}

func (x *GetPipelineRunGitRepoTopKResponse) Reset() {
	*x = GetPipelineRunGitRepoTopKResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPipelineRunGitRepoTopKResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPipelineRunGitRepoTopKResponse) ProtoMessage() {}

func (x *GetPipelineRunGitRepoTopKResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPipelineRunGitRepoTopKResponse.ProtoReflect.Descriptor instead.
func (*GetPipelineRunGitRepoTopKResponse) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{25}
}

func (x *GetPipelineRunGitRepoTopKResponse) GetBranches() []*GetPipelineRunGitRepoTopKResponse_BranchStat {
	if x != nil {
		return x.Branches
	}
	return nil
}

type GetPRStatusResp_Task struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// @gotags: json:"startedTime"
	StartedTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=started_time,json=startedTime,proto3" json:"started_time,omitempty"`
	// @gotags: json:"completedTime"
	CompletedTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=completed_time,json=completedTime,proto3" json:"completed_time,omitempty"`
	// @gotags: json:"elapsedTime"
	ElapsedTime *durationpb.Duration `protobuf:"bytes,5,opt,name=elapsed_time,json=elapsedTime,proto3" json:"elapsed_time,omitempty"`
	Status      string               `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
	Type        string               `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
	Ids         []int64              `protobuf:"varint,8,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	Config      []byte               `protobuf:"bytes,9,opt,name=config,proto3" json:"config,omitempty"`
	Result      []byte               `protobuf:"bytes,10,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *GetPRStatusResp_Task) Reset() {
	*x = GetPRStatusResp_Task{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPRStatusResp_Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPRStatusResp_Task) ProtoMessage() {}

func (x *GetPRStatusResp_Task) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPRStatusResp_Task.ProtoReflect.Descriptor instead.
func (*GetPRStatusResp_Task) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{7, 0}
}

func (x *GetPRStatusResp_Task) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPRStatusResp_Task) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetPRStatusResp_Task) GetStartedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedTime
	}
	return nil
}

func (x *GetPRStatusResp_Task) GetCompletedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedTime
	}
	return nil
}

func (x *GetPRStatusResp_Task) GetElapsedTime() *durationpb.Duration {
	if x != nil {
		return x.ElapsedTime
	}
	return nil
}

func (x *GetPRStatusResp_Task) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetPRStatusResp_Task) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetPRStatusResp_Task) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *GetPRStatusResp_Task) GetConfig() []byte {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *GetPRStatusResp_Task) GetResult() []byte {
	if x != nil {
		return x.Result
	}
	return nil
}

type GetPRStatusResp_Stage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// @gotags: json:"startedTime"
	StartedTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=started_time,json=startedTime,proto3" json:"started_time,omitempty"`
	// @gotags: json:"completedTime"
	CompletedTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=completed_time,json=completedTime,proto3" json:"completed_time,omitempty"`
	// @gotags: json:"elapsedTime"
	ElapsedTime *durationpb.Duration    `protobuf:"bytes,5,opt,name=elapsed_time,json=elapsedTime,proto3" json:"elapsed_time,omitempty"`
	Tasks       []*GetPRStatusResp_Task `protobuf:"bytes,6,rep,name=tasks,proto3" json:"tasks,omitempty"`
	Status      string                  `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	Type        string                  `protobuf:"bytes,8,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *GetPRStatusResp_Stage) Reset() {
	*x = GetPRStatusResp_Stage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPRStatusResp_Stage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPRStatusResp_Stage) ProtoMessage() {}

func (x *GetPRStatusResp_Stage) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPRStatusResp_Stage.ProtoReflect.Descriptor instead.
func (*GetPRStatusResp_Stage) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{7, 1}
}

func (x *GetPRStatusResp_Stage) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPRStatusResp_Stage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetPRStatusResp_Stage) GetStartedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedTime
	}
	return nil
}

func (x *GetPRStatusResp_Stage) GetCompletedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedTime
	}
	return nil
}

func (x *GetPRStatusResp_Stage) GetElapsedTime() *durationpb.Duration {
	if x != nil {
		return x.ElapsedTime
	}
	return nil
}

func (x *GetPRStatusResp_Stage) GetTasks() []*GetPRStatusResp_Task {
	if x != nil {
		return x.Tasks
	}
	return nil
}

func (x *GetPRStatusResp_Stage) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetPRStatusResp_Stage) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type TaskRunRecordsResp_TaskRunRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Config []byte `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *TaskRunRecordsResp_TaskRunRecord) Reset() {
	*x = TaskRunRecordsResp_TaskRunRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskRunRecordsResp_TaskRunRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskRunRecordsResp_TaskRunRecord) ProtoMessage() {}

func (x *TaskRunRecordsResp_TaskRunRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskRunRecordsResp_TaskRunRecord.ProtoReflect.Descriptor instead.
func (*TaskRunRecordsResp_TaskRunRecord) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{14, 0}
}

func (x *TaskRunRecordsResp_TaskRunRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TaskRunRecordsResp_TaskRunRecord) GetConfig() []byte {
	if x != nil {
		return x.Config
	}
	return nil
}

type GetPipelineRunGitRepoTopKResponse_BranchStat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分支名称
	Branch string `protobuf:"bytes,1,opt,name=branch,proto3" json:"branch,omitempty"`
	// 分支构建次数
	Count int32 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *GetPipelineRunGitRepoTopKResponse_BranchStat) Reset() {
	*x = GetPipelineRunGitRepoTopKResponse_BranchStat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_run_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPipelineRunGitRepoTopKResponse_BranchStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPipelineRunGitRepoTopKResponse_BranchStat) ProtoMessage() {}

func (x *GetPipelineRunGitRepoTopKResponse_BranchStat) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_run_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPipelineRunGitRepoTopKResponse_BranchStat.ProtoReflect.Descriptor instead.
func (*GetPipelineRunGitRepoTopKResponse_BranchStat) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_run_proto_rawDescGZIP(), []int{25, 0}
}

func (x *GetPipelineRunGitRepoTopKResponse_BranchStat) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *GetPipelineRunGitRepoTopKResponse_BranchStat) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

var File_pipeline_pipeline_run_proto protoreflect.FileDescriptor

var file_pipeline_pipeline_run_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x22, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x50, 0x52, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x72, 0x69, 0x64, 0x22, 0x34, 0x0a, 0x0a, 0x54, 0x61,
	0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x38, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x1e, 0x0a, 0x0a, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x57, 0x0a, 0x0e, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x75, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a, 0x14,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x11, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x22, 0x36, 0x0a, 0x0b, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x27, 0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x75, 0x6e, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x22, 0xd3, 0x01, 0x0a, 0x07,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6b, 0x74, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6b, 0x74, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0x91, 0x0a, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x50, 0x52, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x65, 0x6c, 0x61, 0x70, 0x73,
	0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x6c, 0x61, 0x70, 0x73, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x67, 0x65, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x52, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x67, 0x65, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x21,
	0x0a, 0x0c, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x53, 0x65, 0x74, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x16, 0x68, 0x61, 0x73, 0x5f, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x68, 0x61, 0x73, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x39, 0x0a, 0x19, 0x68,
	0x61, 0x73, 0x5f, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16,
	0x68, 0x61, 0x73, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x61, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x42, 0x0a, 0x1e, 0x68, 0x61, 0x73, 0x5f, 0x67, 0x72,
	0x61, 0x79, 0x5f, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1a,
	0x68, 0x61, 0x73, 0x47, 0x72, 0x61, 0x79, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x41, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x1a, 0xd8, 0x02, 0x0a, 0x04, 0x54,
	0x61, 0x73, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x65, 0x6c, 0x61,
	0x70, 0x73, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x6c, 0x61, 0x70,
	0x73, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x1a, 0xcd, 0x02, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x52, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x20, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x50, 0x52, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x74, 0x69, 0x64, 0x22, 0x47, 0x0a, 0x0f, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x75, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0c, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x73, 0x74, 0x61, 0x67, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x22, 0xb4, 0x04, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x50, 0x52, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x65, 0x6c, 0x61, 0x70,
	0x73, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x6c, 0x61, 0x70, 0x73,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6b, 0x74, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6b,
	0x74, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x15, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x73, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x53, 0x65, 0x74, 0x49, 0x64, 0x22, 0x42, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x52,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x05,
	0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x52, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x65, 0x73, 0x70, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x22, 0x3a, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x22, 0x3d, 0x0a, 0x11, 0x50, 0x75, 0x73, 0x68, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x12, 0x28, 0x0a, 0x0f,
	0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x96, 0x01, 0x0a, 0x12, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x75, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x47, 0x0a,
	0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x72, 0x75, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x75, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x08, 0x74, 0x61,
	0x73, 0x6b, 0x52, 0x75, 0x6e, 0x73, 0x1a, 0x37, 0x0a, 0x0d, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75,
	0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22,
	0x91, 0x01, 0x0a, 0x12, 0x4e, 0x65, 0x77, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x72,
	0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x61, 0x73,
	0x6b, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x6e, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x4e, 0x6f,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x33, 0x0a, 0x13, 0x4e, 0x65, 0x77, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x61, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x61,
	0x73, 0x6b, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74,
	0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x22, 0x75, 0x0a, 0x0e, 0x54, 0x61, 0x73, 0x6b,
	0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x22,
	0x89, 0x01, 0x0a, 0x0f, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x74, 0x72, 0x79,
	0x12, 0x24, 0x0a, 0x0e, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6f,
	0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x74, 0x72, 0x79, 0x54,
	0x69, 0x6d, 0x65, 0x4f, 0x75, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x79, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x79, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x44, 0x61, 0x74, 0x65, 0x22, 0x21, 0x0a, 0x0d, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x52, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x3f,
	0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x52, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x2d, 0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x52,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x22,
	0x83, 0x02, 0x0a, 0x03, 0x45, 0x6e, 0x76, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x65, 0x6e, 0x76, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73,
	0x65, 0x6e, 0x76, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x76, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6e, 0x76, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x76, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x65, 0x6e, 0x76, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49,
	0x44, 0x12, 0x2d, 0x0a, 0x13, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10,
	0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x49, 0x44,
	0x12, 0x33, 0x0a, 0x16, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x75, 0x62, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x13, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x75, 0x62, 0x74, 0x61, 0x73, 0x6b,
	0x52, 0x75, 0x6e, 0x49, 0x44, 0x22, 0x5e, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65,
	0x5f, 0x6e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x65, 0x65, 0x4e, 0x6f, 0x22, 0xe9, 0x01, 0x0a, 0x2d, 0x4c, 0x61, 0x73, 0x74, 0x52, 0x65,
	0x74, 0x72, 0x79, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x4f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x61, 0x6e, 0x61, 0x72, 0x79, 0x53, 0x75, 0x62, 0x54, 0x61,
	0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x44, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x21,
	0x0a, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x45, 0x6e, 0x76, 0x52, 0x04, 0x65, 0x6e, 0x76,
	0x73, 0x12, 0x2e, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x22, 0xc6, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x52, 0x75, 0x6e, 0x47, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x54, 0x6f, 0x70, 0x4b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x41,
	0x64, 0x64, 0x72, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6b, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x6f, 0x70, 0x4b, 0x22, 0xb3, 0x01, 0x0a, 0x21, 0x47,
	0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x47, 0x69, 0x74,
	0x52, 0x65, 0x70, 0x6f, 0x54, 0x6f, 0x70, 0x4b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x52, 0x0a, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x47, 0x69, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x54, 0x6f, 0x70, 0x4b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x52, 0x08, 0x62, 0x72, 0x61, 0x6e,
	0x63, 0x68, 0x65, 0x73, 0x1a, 0x3a, 0x0a, 0x0a, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x53, 0x74,
	0x61, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x32, 0xc3, 0x0a, 0x0a, 0x12, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4b, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x52,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x52, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x45, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x16, 0x2e, 0x70, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x52, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x1a, 0x17, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x52, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4b, 0x0a, 0x17, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0x1a, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3e, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x42, 0x79, 0x49, 0x64, 0x73, 0x12, 0x14, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x52, 0x65,
	0x71, 0x1a, 0x15, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x42, 0x79, 0x12, 0x14, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x15,
	0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x75, 0x6e, 0x42, 0x79, 0x53, 0x74, 0x61, 0x67, 0x65, 0x41, 0x6e, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x19, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x75, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x52, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x42, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e,
	0x49, 0x64, 0x12, 0x16, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x52, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x52, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x5a, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x41, 0x72, 0x74, 0x69,
	0x66, 0x61, 0x63, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x50, 0x75, 0x73,
	0x68, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x16, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x52, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x50, 0x75, 0x73, 0x68, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x57, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x57,
	0x69, 0x74, 0x68, 0x53, 0x61, 0x6d, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x42, 0x79, 0x12, 0x16, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x52, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x53, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x77, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x54, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x12, 0x1c, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4e, 0x65,
	0x77, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71,
	0x1a, 0x1d, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4e, 0x65, 0x77, 0x41,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x46, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x52, 0x65, 0x74, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x12, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61,
	0x73, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x49, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x75, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x49, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x52, 0x75, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x17, 0x2e, 0x70, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x52, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x52, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x12, 0x7f, 0x0a,
	0x2c, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x74, 0x72, 0x79, 0x50, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x43,
	0x61, 0x6e, 0x61, 0x72, 0x79, 0x53, 0x75, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x16, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x52, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x37, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x2e, 0x4c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x74, 0x72, 0x79, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x52, 0x75, 0x6e, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x61, 0x6e, 0x61,
	0x72, 0x79, 0x53, 0x75, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x50,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x42, 0x79, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x19, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x52, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x74, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52,
	0x75, 0x6e, 0x47, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x54, 0x6f, 0x70, 0x4b, 0x12, 0x2a, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x47, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x54, 0x6f,
	0x70, 0x4b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x52, 0x75, 0x6e, 0x47, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x54, 0x6f, 0x70, 0x4b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x37, 0x5a, 0x35, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67,
	0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x67, 0x65, 0x6e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x61, 0x72, 0x6d, 0x6f, 0x6e, 0x79, 0x2f, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x3b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pipeline_pipeline_run_proto_rawDescOnce sync.Once
	file_pipeline_pipeline_run_proto_rawDescData = file_pipeline_pipeline_run_proto_rawDesc
)

func file_pipeline_pipeline_run_proto_rawDescGZIP() []byte {
	file_pipeline_pipeline_run_proto_rawDescOnce.Do(func() {
		file_pipeline_pipeline_run_proto_rawDescData = protoimpl.X.CompressGZIP(file_pipeline_pipeline_run_proto_rawDescData)
	})
	return file_pipeline_pipeline_run_proto_rawDescData
}

var file_pipeline_pipeline_run_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_pipeline_pipeline_run_proto_goTypes = []interface{}{
	(*GetPRStatusReq)(nil),      // 0: pipeline.GetPRStatusReq
	(*TaskStatus)(nil),          // 1: pipeline.TaskStatus
	(*UpdateStatusResp)(nil),    // 2: pipeline.UpdateStatusResp
	(*TaskRunReq)(nil),          // 3: pipeline.TaskRunReq
	(*TaskRunListReq)(nil),      // 4: pipeline.TaskRunListReq
	(*TaskRunResp)(nil),         // 5: pipeline.TaskRunResp
	(*TaskRun)(nil),             // 6: pipeline.TaskRun
	(*GetPRStatusResp)(nil),     // 7: pipeline.GetPRStatusResp
	(*GetPRTaskReq)(nil),        // 8: pipeline.GetPRTaskReq
	(*TaskRunQueryReq)(nil),     // 9: pipeline.TaskRunQueryReq
	(*GetPRTaskResp)(nil),       // 10: pipeline.GetPRTaskResp
	(*GetPRTaskRespList)(nil),   // 11: pipeline.GetPRTaskRespList
	(*GetPipelineRunResp)(nil),  // 12: pipeline.GetPipelineRunResp
	(*PushImageTaskResp)(nil),   // 13: pipeline.PushImageTaskResp
	(*TaskRunRecordsResp)(nil),  // 14: pipeline.TaskRunRecordsResp
	(*NewApprovalTaskReq)(nil),  // 15: pipeline.NewApprovalTaskReq
	(*NewApprovalTaskResp)(nil), // 16: pipeline.NewApprovalTaskResp
	(*TaskTimeoutReq)(nil),      // 17: pipeline.TaskTimeoutReq
	(*TaskTimeoutResp)(nil),     // 18: pipeline.TaskTimeoutResp
	(*ListPRTaskReq)(nil),       // 19: pipeline.ListPRTaskReq
	(*ListPRTaskResp)(nil),      // 20: pipeline.ListPRTaskResp
	(*Env)(nil),                 // 21: pipeline.Env
	(*Operator)(nil),            // 22: pipeline.Operator
	(*LastRetryPipelineRunOfflineCanarySubTasksResp)(nil), // 23: pipeline.LastRetryPipelineRunOfflineCanarySubTasksResp
	(*GetPipelineRunGitRepoTopKRequest)(nil),              // 24: pipeline.GetPipelineRunGitRepoTopKRequest
	(*GetPipelineRunGitRepoTopKResponse)(nil),             // 25: pipeline.GetPipelineRunGitRepoTopKResponse
	(*GetPRStatusResp_Task)(nil),                          // 26: pipeline.GetPRStatusResp.Task
	(*GetPRStatusResp_Stage)(nil),                         // 27: pipeline.GetPRStatusResp.Stage
	(*TaskRunRecordsResp_TaskRunRecord)(nil),              // 28: pipeline.TaskRunRecordsResp.TaskRunRecord
	(*GetPipelineRunGitRepoTopKResponse_BranchStat)(nil),  // 29: pipeline.GetPipelineRunGitRepoTopKResponse.BranchStat
	(*structpb.Struct)(nil),                               // 30: google.protobuf.Struct
	(*timestamppb.Timestamp)(nil),                         // 31: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),                           // 32: google.protobuf.Duration
}
var file_pipeline_pipeline_run_proto_depIdxs = []int32{
	6,  // 0: pipeline.TaskRunResp.tasks:type_name -> pipeline.TaskRun
	30, // 1: pipeline.TaskRun.config:type_name -> google.protobuf.Struct
	31, // 2: pipeline.GetPRStatusResp.started_time:type_name -> google.protobuf.Timestamp
	31, // 3: pipeline.GetPRStatusResp.completed_time:type_name -> google.protobuf.Timestamp
	32, // 4: pipeline.GetPRStatusResp.elapsed_time:type_name -> google.protobuf.Duration
	27, // 5: pipeline.GetPRStatusResp.stages:type_name -> pipeline.GetPRStatusResp.Stage
	31, // 6: pipeline.GetPRTaskResp.started_time:type_name -> google.protobuf.Timestamp
	31, // 7: pipeline.GetPRTaskResp.completed_time:type_name -> google.protobuf.Timestamp
	32, // 8: pipeline.GetPRTaskResp.elapsed_time:type_name -> google.protobuf.Duration
	10, // 9: pipeline.GetPRTaskRespList.tasks:type_name -> pipeline.GetPRTaskResp
	28, // 10: pipeline.TaskRunRecordsResp.task_runs:type_name -> pipeline.TaskRunRecordsResp.TaskRunRecord
	10, // 11: pipeline.ListPRTaskResp.tasks:type_name -> pipeline.GetPRTaskResp
	21, // 12: pipeline.LastRetryPipelineRunOfflineCanarySubTasksResp.envs:type_name -> pipeline.Env
	22, // 13: pipeline.LastRetryPipelineRunOfflineCanarySubTasksResp.operator:type_name -> pipeline.Operator
	31, // 14: pipeline.GetPipelineRunGitRepoTopKRequest.start_time:type_name -> google.protobuf.Timestamp
	31, // 15: pipeline.GetPipelineRunGitRepoTopKRequest.end_time:type_name -> google.protobuf.Timestamp
	29, // 16: pipeline.GetPipelineRunGitRepoTopKResponse.branches:type_name -> pipeline.GetPipelineRunGitRepoTopKResponse.BranchStat
	31, // 17: pipeline.GetPRStatusResp.Task.started_time:type_name -> google.protobuf.Timestamp
	31, // 18: pipeline.GetPRStatusResp.Task.completed_time:type_name -> google.protobuf.Timestamp
	32, // 19: pipeline.GetPRStatusResp.Task.elapsed_time:type_name -> google.protobuf.Duration
	31, // 20: pipeline.GetPRStatusResp.Stage.started_time:type_name -> google.protobuf.Timestamp
	31, // 21: pipeline.GetPRStatusResp.Stage.completed_time:type_name -> google.protobuf.Timestamp
	32, // 22: pipeline.GetPRStatusResp.Stage.elapsed_time:type_name -> google.protobuf.Duration
	26, // 23: pipeline.GetPRStatusResp.Stage.tasks:type_name -> pipeline.GetPRStatusResp.Task
	0,  // 24: pipeline.PipelineRunService.GetPipelineRunStatus:input_type -> pipeline.GetPRStatusReq
	8,  // 25: pipeline.PipelineRunService.GetPipelineRunTask:input_type -> pipeline.GetPRTaskReq
	1,  // 26: pipeline.PipelineRunService.UpdatePipelineRunStatus:input_type -> pipeline.TaskStatus
	3,  // 27: pipeline.PipelineRunService.GetTaskRunByIds:input_type -> pipeline.TaskRunReq
	3,  // 28: pipeline.PipelineRunService.GetTaskRunBy:input_type -> pipeline.TaskRunReq
	9,  // 29: pipeline.PipelineRunService.GetTaskRunByStageAndType:input_type -> pipeline.TaskRunQueryReq
	8,  // 30: pipeline.PipelineRunService.GetPipelineRunByTaskRunId:input_type -> pipeline.GetPRTaskReq
	8,  // 31: pipeline.PipelineRunService.GetArtifactVersionByPushImageTaskId:input_type -> pipeline.GetPRTaskReq
	8,  // 32: pipeline.PipelineRunService.GetTaskRunWithSameBuildNumberBy:input_type -> pipeline.GetPRTaskReq
	15, // 33: pipeline.PipelineRunService.GetNewApprovalTaskId:input_type -> pipeline.NewApprovalTaskReq
	17, // 34: pipeline.PipelineRunService.GetRetryTimeout:input_type -> pipeline.TaskTimeoutReq
	4,  // 35: pipeline.PipelineRunService.GetPipelineRunTaskList:input_type -> pipeline.TaskRunListReq
	19, // 36: pipeline.PipelineRunService.ListPipelineRunTasks:input_type -> pipeline.ListPRTaskReq
	8,  // 37: pipeline.PipelineRunService.GetLastRetryPipelineRunOfflineCanarySubTasks:input_type -> pipeline.GetPRTaskReq
	9,  // 38: pipeline.PipelineRunService.GetTaskRunByStageRunId:input_type -> pipeline.TaskRunQueryReq
	24, // 39: pipeline.PipelineRunService.GetPipelineRunGitRepoTopK:input_type -> pipeline.GetPipelineRunGitRepoTopKRequest
	7,  // 40: pipeline.PipelineRunService.GetPipelineRunStatus:output_type -> pipeline.GetPRStatusResp
	10, // 41: pipeline.PipelineRunService.GetPipelineRunTask:output_type -> pipeline.GetPRTaskResp
	2,  // 42: pipeline.PipelineRunService.UpdatePipelineRunStatus:output_type -> pipeline.UpdateStatusResp
	5,  // 43: pipeline.PipelineRunService.GetTaskRunByIds:output_type -> pipeline.TaskRunResp
	5,  // 44: pipeline.PipelineRunService.GetTaskRunBy:output_type -> pipeline.TaskRunResp
	10, // 45: pipeline.PipelineRunService.GetTaskRunByStageAndType:output_type -> pipeline.GetPRTaskResp
	7,  // 46: pipeline.PipelineRunService.GetPipelineRunByTaskRunId:output_type -> pipeline.GetPRStatusResp
	13, // 47: pipeline.PipelineRunService.GetArtifactVersionByPushImageTaskId:output_type -> pipeline.PushImageTaskResp
	14, // 48: pipeline.PipelineRunService.GetTaskRunWithSameBuildNumberBy:output_type -> pipeline.TaskRunRecordsResp
	16, // 49: pipeline.PipelineRunService.GetNewApprovalTaskId:output_type -> pipeline.NewApprovalTaskResp
	18, // 50: pipeline.PipelineRunService.GetRetryTimeout:output_type -> pipeline.TaskTimeoutResp
	5,  // 51: pipeline.PipelineRunService.GetPipelineRunTaskList:output_type -> pipeline.TaskRunResp
	20, // 52: pipeline.PipelineRunService.ListPipelineRunTasks:output_type -> pipeline.ListPRTaskResp
	23, // 53: pipeline.PipelineRunService.GetLastRetryPipelineRunOfflineCanarySubTasks:output_type -> pipeline.LastRetryPipelineRunOfflineCanarySubTasksResp
	11, // 54: pipeline.PipelineRunService.GetTaskRunByStageRunId:output_type -> pipeline.GetPRTaskRespList
	25, // 55: pipeline.PipelineRunService.GetPipelineRunGitRepoTopK:output_type -> pipeline.GetPipelineRunGitRepoTopKResponse
	40, // [40:56] is the sub-list for method output_type
	24, // [24:40] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_pipeline_pipeline_run_proto_init() }
func file_pipeline_pipeline_run_proto_init() {
	if File_pipeline_pipeline_run_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pipeline_pipeline_run_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPRStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStatusResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskRunReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskRunListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskRunResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskRun); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPRStatusResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPRTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskRunQueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPRTaskResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPRTaskRespList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPipelineRunResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushImageTaskResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskRunRecordsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewApprovalTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewApprovalTaskResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskTimeoutReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskTimeoutResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPRTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPRTaskResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Env); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Operator); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LastRetryPipelineRunOfflineCanarySubTasksResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPipelineRunGitRepoTopKRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPipelineRunGitRepoTopKResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPRStatusResp_Task); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPRStatusResp_Stage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskRunRecordsResp_TaskRunRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_run_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPipelineRunGitRepoTopKResponse_BranchStat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pipeline_pipeline_run_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pipeline_pipeline_run_proto_goTypes,
		DependencyIndexes: file_pipeline_pipeline_run_proto_depIdxs,
		MessageInfos:      file_pipeline_pipeline_run_proto_msgTypes,
	}.Build()
	File_pipeline_pipeline_run_proto = out.File
	file_pipeline_pipeline_run_proto_rawDesc = nil
	file_pipeline_pipeline_run_proto_goTypes = nil
	file_pipeline_pipeline_run_proto_depIdxs = nil
}
