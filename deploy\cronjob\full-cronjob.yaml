# only for cicd qa-dev environment, we need to set affnity to specify node for data persistence
apiVersion: batch/v1
kind: CronJob
metadata:
  name: full-sync
spec:
  schedule: "0 2 27 * *"
  jobTemplate:
    jobTemplate:
    spec:
      template:
        spec:
          nodeSelector:
            kubernetes.io/hostname: "************"
          containers:
            - name: sync
              image: cr.ttyuyin.com/devops/tekton/gitlab-sync:latest
              imagePullPolicy: Always
              command: ["/ttyuyin/full-sync.sh"]
              volumeMounts:
                - name: code
                  mountPath: /data
                - name: gitlab-http-auth
                  mountPath: /home/<USER>/
              env:
                - name: DB_HOST
                  value: "************"
                - name: DB_USER
                  value: "rd_dev"
                - name: DB_PASS
                  value: "vRcfj3W#2nGdBeu@"
                - name: MAX_PROCESSES
                  value: "4"
          restartPolicy: Never
          volumes:
            - name: code
              hostPath:
                # directory location on host
                path: /data/gitlab-sync/
                type: DirectoryOrCreate
            - name: gitlab-http-auth
              secret:
                secretName: gitlab-http-auth-secret
                items:
                  - key: ".git-credentials"
                    path: ".git-credentials"
                  - key: ".gitconfig"
                    path: ".gitconfig"
