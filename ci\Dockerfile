
FROM cr.ttyuyin.com/devops/golang:1.20 AS BuildStage

ARG serviceName

COPY . /go/src/52tt.com/approval

WORKDIR /go/src/52tt.com/approval


RUN export GOSUMDB=off && export GOPROXY=http://yw-nexus.ttyuyin.com:8081/repository/group-go/ && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o controller cmd/${serviceName}/*.go


FROM cr.ttyuyin.com/devops/csi-base:v1.0 as RUNNER

COPY --from=BuildStage /usr/share/zoneinfo/Asia/Shanghai /usr/share/zoneinfo/Asia/Shanghai
ENV TZ=Asia/Shanghai

EXPOSE 2049

# setup entrypoint
COPY ./ci/entrypoint.sh ./ci/supervisord.conf /ttyuyin/
COPY --from=BuildStage /go/src/52tt.com/approval/controller /ttyuyin/
ENTRYPOINT ["/usr/bin/supervisord", "-c", "/ttyuyin/supervisord.conf"]
