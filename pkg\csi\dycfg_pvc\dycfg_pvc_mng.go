package dycfgpvc

import (
	"context"
	"sync"
	"time"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"
)

const (
	DY_CFG_PVC_NAME = "tt-cicd-dy-config-pvc-t4322"
	TT_CSI_SC       = "cicd-csi-sc"
)

type IDycfgPvcMng interface {
	Start()
	Stop()
}

type dycfgPvcMng struct {
	schedulerLock   sync.Mutex
	schedulerTicker *time.Ticker
	client          kubernetes.Interface
}

func NewDycfgPvcMng(client kubernetes.Interface) IDycfgPvcMng {
	return &dycfgPvcMng{
		schedulerLock: sync.Mutex{},
		client:        client,
	}
}

func (mng *dycfgPvcMng) Start() {
	go func() {
		time.Sleep(time.Minute * 2)
		mng.schedule()
		// Create a periodic job that fills the scheduler with new pipelines.
		mng.schedulerTicker = time.NewTicker(time.Minute * 30)
		for {
			select {
			case <-mng.schedulerTicker.C:
				// Do the scheduling
				mng.schedule()
			}
		}
	}()
}

func (mng *dycfgPvcMng) Stop() {
	if mng.schedulerTicker == nil {
		return
	}
	mng.schedulerTicker.Stop()
	mng.schedulerTicker = nil
	return
}

func (mng *dycfgPvcMng) schedule() {
	defer func() {
		if r := recover(); r != nil {
			klog.Errorf("⚠⚠⚠⚠⚠ dycfgPvcMng scheduler panic: %v", r)
		}
	}()
	// 尝试获取锁，获取不到，则退出
	if !mng.schedulerLock.TryLock() {
		return
	}
	defer func() {
		mng.schedulerLock.Unlock()
	}()

	klog.Infof("🚀🚀🚀🚀🚀 dycfgPvcMng scheduler start")
	ctx := context.Background()

	allNs, err := mng.client.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
	if err != nil {
		klog.Errorf("list namespace failed: %v", err)
		return
	}

	for _, ns := range allNs.Items {
		_, errIn := mng.client.CoreV1().PersistentVolumeClaims(ns.Name).Get(ctx, DY_CFG_PVC_NAME, metav1.GetOptions{})
		// PVC 不存在 则创建
		if errors.IsNotFound(errIn) {
			scName := TT_CSI_SC
			_, errCreate := mng.client.CoreV1().PersistentVolumeClaims(ns.Name).Create(ctx, &v1.PersistentVolumeClaim{
				ObjectMeta: metav1.ObjectMeta{
					Name:      DY_CFG_PVC_NAME,
					Namespace: ns.Name,
				},
				Spec: v1.PersistentVolumeClaimSpec{
					AccessModes:      []v1.PersistentVolumeAccessMode{v1.ReadOnlyMany},
					Resources:        v1.ResourceRequirements{Requests: v1.ResourceList{v1.ResourceStorage: resource.MustParse("10Gi")}},
					StorageClassName: &scName,
				},
			}, metav1.CreateOptions{})

			if errCreate != nil {
				klog.Errorf("create pvc %s in ns %s failed: %v", DY_CFG_PVC_NAME, ns.Name, errCreate)
			}
			continue
		}

		if errIn != nil {
			klog.Errorf("get pvc %s in ns %s failed: %v", DY_CFG_PVC_NAME, ns.Name, errIn)
			continue
		}
	}
}
