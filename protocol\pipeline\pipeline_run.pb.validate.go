// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: pipeline/pipeline_run.proto

package pipeline

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetPRStatusReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetPRStatusReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPRStatusReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetPRStatusReqMultiError,
// or nil if none found.
func (m *GetPRStatusReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPRStatusReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Rid

	if len(errors) > 0 {
		return GetPRStatusReqMultiError(errors)
	}

	return nil
}

// GetPRStatusReqMultiError is an error wrapping multiple validation errors
// returned by GetPRStatusReq.ValidateAll() if the designated constraints
// aren't met.
type GetPRStatusReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPRStatusReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPRStatusReqMultiError) AllErrors() []error { return m }

// GetPRStatusReqValidationError is the validation error returned by
// GetPRStatusReq.Validate if the designated constraints aren't met.
type GetPRStatusReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPRStatusReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPRStatusReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPRStatusReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPRStatusReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPRStatusReqValidationError) ErrorName() string { return "GetPRStatusReqValidationError" }

// Error satisfies the builtin error interface
func (e GetPRStatusReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPRStatusReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPRStatusReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPRStatusReqValidationError{}

// Validate checks the field values on TaskStatus with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TaskStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TaskStatusMultiError, or
// nil if none found.
func (m *TaskStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Status

	if len(errors) > 0 {
		return TaskStatusMultiError(errors)
	}

	return nil
}

// TaskStatusMultiError is an error wrapping multiple validation errors
// returned by TaskStatus.ValidateAll() if the designated constraints aren't met.
type TaskStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskStatusMultiError) AllErrors() []error { return m }

// TaskStatusValidationError is the validation error returned by
// TaskStatus.Validate if the designated constraints aren't met.
type TaskStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskStatusValidationError) ErrorName() string { return "TaskStatusValidationError" }

// Error satisfies the builtin error interface
func (e TaskStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskStatusValidationError{}

// Validate checks the field values on UpdateStatusResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateStatusResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateStatusResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateStatusRespMultiError, or nil if none found.
func (m *UpdateStatusResp) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateStatusResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return UpdateStatusRespMultiError(errors)
	}

	return nil
}

// UpdateStatusRespMultiError is an error wrapping multiple validation errors
// returned by UpdateStatusResp.ValidateAll() if the designated constraints
// aren't met.
type UpdateStatusRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateStatusRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateStatusRespMultiError) AllErrors() []error { return m }

// UpdateStatusRespValidationError is the validation error returned by
// UpdateStatusResp.Validate if the designated constraints aren't met.
type UpdateStatusRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateStatusRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateStatusRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateStatusRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateStatusRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateStatusRespValidationError) ErrorName() string { return "UpdateStatusRespValidationError" }

// Error satisfies the builtin error interface
func (e UpdateStatusRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateStatusResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateStatusRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateStatusRespValidationError{}

// Validate checks the field values on TaskRunReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TaskRunReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskRunReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TaskRunReqMultiError, or
// nil if none found.
func (m *TaskRunReq) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskRunReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TaskRunReqMultiError(errors)
	}

	return nil
}

// TaskRunReqMultiError is an error wrapping multiple validation errors
// returned by TaskRunReq.ValidateAll() if the designated constraints aren't met.
type TaskRunReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskRunReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskRunReqMultiError) AllErrors() []error { return m }

// TaskRunReqValidationError is the validation error returned by
// TaskRunReq.Validate if the designated constraints aren't met.
type TaskRunReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskRunReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskRunReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskRunReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskRunReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskRunReqValidationError) ErrorName() string { return "TaskRunReqValidationError" }

// Error satisfies the builtin error interface
func (e TaskRunReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskRunReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskRunReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskRunReqValidationError{}

// Validate checks the field values on TaskRunListReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TaskRunListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskRunListReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TaskRunListReqMultiError,
// or nil if none found.
func (m *TaskRunListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskRunListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TaskRunListReqMultiError(errors)
	}

	return nil
}

// TaskRunListReqMultiError is an error wrapping multiple validation errors
// returned by TaskRunListReq.ValidateAll() if the designated constraints
// aren't met.
type TaskRunListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskRunListReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskRunListReqMultiError) AllErrors() []error { return m }

// TaskRunListReqValidationError is the validation error returned by
// TaskRunListReq.Validate if the designated constraints aren't met.
type TaskRunListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskRunListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskRunListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskRunListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskRunListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskRunListReqValidationError) ErrorName() string { return "TaskRunListReqValidationError" }

// Error satisfies the builtin error interface
func (e TaskRunListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskRunListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskRunListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskRunListReqValidationError{}

// Validate checks the field values on TaskRunResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TaskRunResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskRunResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TaskRunRespMultiError, or
// nil if none found.
func (m *TaskRunResp) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskRunResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTasks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TaskRunRespValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TaskRunRespValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TaskRunRespValidationError{
					field:  fmt.Sprintf("Tasks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TaskRunRespMultiError(errors)
	}

	return nil
}

// TaskRunRespMultiError is an error wrapping multiple validation errors
// returned by TaskRunResp.ValidateAll() if the designated constraints aren't met.
type TaskRunRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskRunRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskRunRespMultiError) AllErrors() []error { return m }

// TaskRunRespValidationError is the validation error returned by
// TaskRunResp.Validate if the designated constraints aren't met.
type TaskRunRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskRunRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskRunRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskRunRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskRunRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskRunRespValidationError) ErrorName() string { return "TaskRunRespValidationError" }

// Error satisfies the builtin error interface
func (e TaskRunRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskRunResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskRunRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskRunRespValidationError{}

// Validate checks the field values on TaskRun with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TaskRun) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskRun with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TaskRunMultiError, or nil if none found.
func (m *TaskRun) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskRun) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Status

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TaskRunValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TaskRunValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TaskRunValidationError{
				field:  "Config",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PipelineRunId

	// no validation rules for TektonName

	if len(errors) > 0 {
		return TaskRunMultiError(errors)
	}

	return nil
}

// TaskRunMultiError is an error wrapping multiple validation errors returned
// by TaskRun.ValidateAll() if the designated constraints aren't met.
type TaskRunMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskRunMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskRunMultiError) AllErrors() []error { return m }

// TaskRunValidationError is the validation error returned by TaskRun.Validate
// if the designated constraints aren't met.
type TaskRunValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskRunValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskRunValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskRunValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskRunValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskRunValidationError) ErrorName() string { return "TaskRunValidationError" }

// Error satisfies the builtin error interface
func (e TaskRunValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskRun.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskRunValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskRunValidationError{}

// Validate checks the field values on GetPRStatusResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetPRStatusResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPRStatusResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPRStatusRespMultiError, or nil if none found.
func (m *GetPRStatusResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPRStatusResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetStartedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPRStatusRespValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPRStatusRespValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPRStatusRespValidationError{
				field:  "StartedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPRStatusRespValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPRStatusRespValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPRStatusRespValidationError{
				field:  "CompletedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetElapsedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPRStatusRespValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPRStatusRespValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetElapsedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPRStatusRespValidationError{
				field:  "ElapsedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetStages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPRStatusRespValidationError{
						field:  fmt.Sprintf("Stages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPRStatusRespValidationError{
						field:  fmt.Sprintf("Stages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPRStatusRespValidationError{
					field:  fmt.Sprintf("Stages[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Status

	// no validation rules for Branch

	// no validation rules for BuildNumber

	// no validation rules for PipelineId

	// no validation rules for ChangeSetId

	// no validation rules for HasTestApprovalTask

	// no validation rules for HasUpgradeApprovalTask

	// no validation rules for HasGrayUpgradeApprovalTask

	if len(errors) > 0 {
		return GetPRStatusRespMultiError(errors)
	}

	return nil
}

// GetPRStatusRespMultiError is an error wrapping multiple validation errors
// returned by GetPRStatusResp.ValidateAll() if the designated constraints
// aren't met.
type GetPRStatusRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPRStatusRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPRStatusRespMultiError) AllErrors() []error { return m }

// GetPRStatusRespValidationError is the validation error returned by
// GetPRStatusResp.Validate if the designated constraints aren't met.
type GetPRStatusRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPRStatusRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPRStatusRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPRStatusRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPRStatusRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPRStatusRespValidationError) ErrorName() string { return "GetPRStatusRespValidationError" }

// Error satisfies the builtin error interface
func (e GetPRStatusRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPRStatusResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPRStatusRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPRStatusRespValidationError{}

// Validate checks the field values on GetPRTaskReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetPRTaskReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPRTaskReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetPRTaskReqMultiError, or
// nil if none found.
func (m *GetPRTaskReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPRTaskReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tid

	if len(errors) > 0 {
		return GetPRTaskReqMultiError(errors)
	}

	return nil
}

// GetPRTaskReqMultiError is an error wrapping multiple validation errors
// returned by GetPRTaskReq.ValidateAll() if the designated constraints aren't met.
type GetPRTaskReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPRTaskReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPRTaskReqMultiError) AllErrors() []error { return m }

// GetPRTaskReqValidationError is the validation error returned by
// GetPRTaskReq.Validate if the designated constraints aren't met.
type GetPRTaskReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPRTaskReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPRTaskReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPRTaskReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPRTaskReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPRTaskReqValidationError) ErrorName() string { return "GetPRTaskReqValidationError" }

// Error satisfies the builtin error interface
func (e GetPRTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPRTaskReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPRTaskReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPRTaskReqValidationError{}

// Validate checks the field values on TaskRunQueryReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TaskRunQueryReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskRunQueryReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TaskRunQueryReqMultiError, or nil if none found.
func (m *TaskRunQueryReq) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskRunQueryReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StageRunId

	// no validation rules for Type

	if len(errors) > 0 {
		return TaskRunQueryReqMultiError(errors)
	}

	return nil
}

// TaskRunQueryReqMultiError is an error wrapping multiple validation errors
// returned by TaskRunQueryReq.ValidateAll() if the designated constraints
// aren't met.
type TaskRunQueryReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskRunQueryReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskRunQueryReqMultiError) AllErrors() []error { return m }

// TaskRunQueryReqValidationError is the validation error returned by
// TaskRunQueryReq.Validate if the designated constraints aren't met.
type TaskRunQueryReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskRunQueryReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskRunQueryReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskRunQueryReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskRunQueryReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskRunQueryReqValidationError) ErrorName() string { return "TaskRunQueryReqValidationError" }

// Error satisfies the builtin error interface
func (e TaskRunQueryReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskRunQueryReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskRunQueryReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskRunQueryReqValidationError{}

// Validate checks the field values on GetPRTaskResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetPRTaskResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPRTaskResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetPRTaskRespMultiError, or
// nil if none found.
func (m *GetPRTaskResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPRTaskResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if all {
		switch v := interface{}(m.GetStartedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPRTaskRespValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPRTaskRespValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPRTaskRespValidationError{
				field:  "StartedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPRTaskRespValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPRTaskRespValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPRTaskRespValidationError{
				field:  "CompletedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetElapsedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPRTaskRespValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPRTaskRespValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetElapsedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPRTaskRespValidationError{
				field:  "ElapsedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TektonName

	// no validation rules for StageRunId

	// no validation rules for TaskId

	// no validation rules for Config

	// no validation rules for PipelineRunId

	// no validation rules for Status

	// no validation rules for PipelineBuildNumber

	// no validation rules for PipelineId

	// no validation rules for Type

	// no validation rules for ChangeSetId

	if len(errors) > 0 {
		return GetPRTaskRespMultiError(errors)
	}

	return nil
}

// GetPRTaskRespMultiError is an error wrapping multiple validation errors
// returned by GetPRTaskResp.ValidateAll() if the designated constraints
// aren't met.
type GetPRTaskRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPRTaskRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPRTaskRespMultiError) AllErrors() []error { return m }

// GetPRTaskRespValidationError is the validation error returned by
// GetPRTaskResp.Validate if the designated constraints aren't met.
type GetPRTaskRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPRTaskRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPRTaskRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPRTaskRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPRTaskRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPRTaskRespValidationError) ErrorName() string { return "GetPRTaskRespValidationError" }

// Error satisfies the builtin error interface
func (e GetPRTaskRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPRTaskResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPRTaskRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPRTaskRespValidationError{}

// Validate checks the field values on GetPRTaskRespList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetPRTaskRespList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPRTaskRespList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPRTaskRespListMultiError, or nil if none found.
func (m *GetPRTaskRespList) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPRTaskRespList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTasks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPRTaskRespListValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPRTaskRespListValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPRTaskRespListValidationError{
					field:  fmt.Sprintf("Tasks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetPRTaskRespListMultiError(errors)
	}

	return nil
}

// GetPRTaskRespListMultiError is an error wrapping multiple validation errors
// returned by GetPRTaskRespList.ValidateAll() if the designated constraints
// aren't met.
type GetPRTaskRespListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPRTaskRespListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPRTaskRespListMultiError) AllErrors() []error { return m }

// GetPRTaskRespListValidationError is the validation error returned by
// GetPRTaskRespList.Validate if the designated constraints aren't met.
type GetPRTaskRespListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPRTaskRespListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPRTaskRespListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPRTaskRespListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPRTaskRespListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPRTaskRespListValidationError) ErrorName() string {
	return "GetPRTaskRespListValidationError"
}

// Error satisfies the builtin error interface
func (e GetPRTaskRespListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPRTaskRespList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPRTaskRespListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPRTaskRespListValidationError{}

// Validate checks the field values on GetPipelineRunResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPipelineRunResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPipelineRunResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPipelineRunRespMultiError, or nil if none found.
func (m *GetPipelineRunResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPipelineRunResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PipelineRunId

	if len(errors) > 0 {
		return GetPipelineRunRespMultiError(errors)
	}

	return nil
}

// GetPipelineRunRespMultiError is an error wrapping multiple validation errors
// returned by GetPipelineRunResp.ValidateAll() if the designated constraints
// aren't met.
type GetPipelineRunRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPipelineRunRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPipelineRunRespMultiError) AllErrors() []error { return m }

// GetPipelineRunRespValidationError is the validation error returned by
// GetPipelineRunResp.Validate if the designated constraints aren't met.
type GetPipelineRunRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPipelineRunRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPipelineRunRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPipelineRunRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPipelineRunRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPipelineRunRespValidationError) ErrorName() string {
	return "GetPipelineRunRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetPipelineRunRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPipelineRunResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPipelineRunRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPipelineRunRespValidationError{}

// Validate checks the field values on PushImageTaskResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PushImageTaskResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PushImageTaskResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PushImageTaskRespMultiError, or nil if none found.
func (m *PushImageTaskResp) ValidateAll() error {
	return m.validate(true)
}

func (m *PushImageTaskResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ArtifactVersion

	if len(errors) > 0 {
		return PushImageTaskRespMultiError(errors)
	}

	return nil
}

// PushImageTaskRespMultiError is an error wrapping multiple validation errors
// returned by PushImageTaskResp.ValidateAll() if the designated constraints
// aren't met.
type PushImageTaskRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PushImageTaskRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PushImageTaskRespMultiError) AllErrors() []error { return m }

// PushImageTaskRespValidationError is the validation error returned by
// PushImageTaskResp.Validate if the designated constraints aren't met.
type PushImageTaskRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PushImageTaskRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PushImageTaskRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PushImageTaskRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PushImageTaskRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PushImageTaskRespValidationError) ErrorName() string {
	return "PushImageTaskRespValidationError"
}

// Error satisfies the builtin error interface
func (e PushImageTaskRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPushImageTaskResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PushImageTaskRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PushImageTaskRespValidationError{}

// Validate checks the field values on TaskRunRecordsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TaskRunRecordsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskRunRecordsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TaskRunRecordsRespMultiError, or nil if none found.
func (m *TaskRunRecordsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskRunRecordsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTaskRuns() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TaskRunRecordsRespValidationError{
						field:  fmt.Sprintf("TaskRuns[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TaskRunRecordsRespValidationError{
						field:  fmt.Sprintf("TaskRuns[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TaskRunRecordsRespValidationError{
					field:  fmt.Sprintf("TaskRuns[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TaskRunRecordsRespMultiError(errors)
	}

	return nil
}

// TaskRunRecordsRespMultiError is an error wrapping multiple validation errors
// returned by TaskRunRecordsResp.ValidateAll() if the designated constraints
// aren't met.
type TaskRunRecordsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskRunRecordsRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskRunRecordsRespMultiError) AllErrors() []error { return m }

// TaskRunRecordsRespValidationError is the validation error returned by
// TaskRunRecordsResp.Validate if the designated constraints aren't met.
type TaskRunRecordsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskRunRecordsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskRunRecordsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskRunRecordsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskRunRecordsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskRunRecordsRespValidationError) ErrorName() string {
	return "TaskRunRecordsRespValidationError"
}

// Error satisfies the builtin error interface
func (e TaskRunRecordsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskRunRecordsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskRunRecordsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskRunRecordsRespValidationError{}

// Validate checks the field values on NewApprovalTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NewApprovalTaskReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NewApprovalTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NewApprovalTaskReqMultiError, or nil if none found.
func (m *NewApprovalTaskReq) ValidateAll() error {
	return m.validate(true)
}

func (m *NewApprovalTaskReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskRunId

	// no validation rules for UserId

	// no validation rules for EmployeeNo

	// no validation rules for ChineseName

	if len(errors) > 0 {
		return NewApprovalTaskReqMultiError(errors)
	}

	return nil
}

// NewApprovalTaskReqMultiError is an error wrapping multiple validation errors
// returned by NewApprovalTaskReq.ValidateAll() if the designated constraints
// aren't met.
type NewApprovalTaskReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NewApprovalTaskReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NewApprovalTaskReqMultiError) AllErrors() []error { return m }

// NewApprovalTaskReqValidationError is the validation error returned by
// NewApprovalTaskReq.Validate if the designated constraints aren't met.
type NewApprovalTaskReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NewApprovalTaskReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NewApprovalTaskReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NewApprovalTaskReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NewApprovalTaskReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NewApprovalTaskReqValidationError) ErrorName() string {
	return "NewApprovalTaskReqValidationError"
}

// Error satisfies the builtin error interface
func (e NewApprovalTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNewApprovalTaskReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NewApprovalTaskReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NewApprovalTaskReqValidationError{}

// Validate checks the field values on NewApprovalTaskResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NewApprovalTaskResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NewApprovalTaskResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NewApprovalTaskRespMultiError, or nil if none found.
func (m *NewApprovalTaskResp) ValidateAll() error {
	return m.validate(true)
}

func (m *NewApprovalTaskResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskRunId

	if len(errors) > 0 {
		return NewApprovalTaskRespMultiError(errors)
	}

	return nil
}

// NewApprovalTaskRespMultiError is an error wrapping multiple validation
// errors returned by NewApprovalTaskResp.ValidateAll() if the designated
// constraints aren't met.
type NewApprovalTaskRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NewApprovalTaskRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NewApprovalTaskRespMultiError) AllErrors() []error { return m }

// NewApprovalTaskRespValidationError is the validation error returned by
// NewApprovalTaskResp.Validate if the designated constraints aren't met.
type NewApprovalTaskRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NewApprovalTaskRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NewApprovalTaskRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NewApprovalTaskRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NewApprovalTaskRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NewApprovalTaskRespValidationError) ErrorName() string {
	return "NewApprovalTaskRespValidationError"
}

// Error satisfies the builtin error interface
func (e NewApprovalTaskRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNewApprovalTaskResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NewApprovalTaskRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NewApprovalTaskRespValidationError{}

// Validate checks the field values on TaskTimeoutReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TaskTimeoutReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskTimeoutReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TaskTimeoutReqMultiError,
// or nil if none found.
func (m *TaskTimeoutReq) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskTimeoutReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PipelineRunId

	// no validation rules for TaskRunId

	// no validation rules for TicketId

	if len(errors) > 0 {
		return TaskTimeoutReqMultiError(errors)
	}

	return nil
}

// TaskTimeoutReqMultiError is an error wrapping multiple validation errors
// returned by TaskTimeoutReq.ValidateAll() if the designated constraints
// aren't met.
type TaskTimeoutReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskTimeoutReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskTimeoutReqMultiError) AllErrors() []error { return m }

// TaskTimeoutReqValidationError is the validation error returned by
// TaskTimeoutReq.Validate if the designated constraints aren't met.
type TaskTimeoutReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskTimeoutReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskTimeoutReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskTimeoutReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskTimeoutReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskTimeoutReqValidationError) ErrorName() string { return "TaskTimeoutReqValidationError" }

// Error satisfies the builtin error interface
func (e TaskTimeoutReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskTimeoutReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskTimeoutReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskTimeoutReqValidationError{}

// Validate checks the field values on TaskTimeoutResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TaskTimeoutResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskTimeoutResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TaskTimeoutRespMultiError, or nil if none found.
func (m *TaskTimeoutResp) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskTimeoutResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CanRetry

	// no validation rules for RetryTimeOut

	// no validation rules for Days

	// no validation rules for ExpireDate

	if len(errors) > 0 {
		return TaskTimeoutRespMultiError(errors)
	}

	return nil
}

// TaskTimeoutRespMultiError is an error wrapping multiple validation errors
// returned by TaskTimeoutResp.ValidateAll() if the designated constraints
// aren't met.
type TaskTimeoutRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskTimeoutRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskTimeoutRespMultiError) AllErrors() []error { return m }

// TaskTimeoutRespValidationError is the validation error returned by
// TaskTimeoutResp.Validate if the designated constraints aren't met.
type TaskTimeoutRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskTimeoutRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskTimeoutRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskTimeoutRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskTimeoutRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskTimeoutRespValidationError) ErrorName() string { return "TaskTimeoutRespValidationError" }

// Error satisfies the builtin error interface
func (e TaskTimeoutRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskTimeoutResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskTimeoutRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskTimeoutRespValidationError{}

// Validate checks the field values on ListPRTaskReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListPRTaskReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPRTaskReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListPRTaskReqMultiError, or
// nil if none found.
func (m *ListPRTaskReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPRTaskReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListPRTaskReqMultiError(errors)
	}

	return nil
}

// ListPRTaskReqMultiError is an error wrapping multiple validation errors
// returned by ListPRTaskReq.ValidateAll() if the designated constraints
// aren't met.
type ListPRTaskReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPRTaskReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPRTaskReqMultiError) AllErrors() []error { return m }

// ListPRTaskReqValidationError is the validation error returned by
// ListPRTaskReq.Validate if the designated constraints aren't met.
type ListPRTaskReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPRTaskReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPRTaskReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPRTaskReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPRTaskReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPRTaskReqValidationError) ErrorName() string { return "ListPRTaskReqValidationError" }

// Error satisfies the builtin error interface
func (e ListPRTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPRTaskReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPRTaskReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPRTaskReqValidationError{}

// Validate checks the field values on ListPRTaskResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListPRTaskResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPRTaskResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListPRTaskRespMultiError,
// or nil if none found.
func (m *ListPRTaskResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPRTaskResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTasks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListPRTaskRespValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListPRTaskRespValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListPRTaskRespValidationError{
					field:  fmt.Sprintf("Tasks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListPRTaskRespMultiError(errors)
	}

	return nil
}

// ListPRTaskRespMultiError is an error wrapping multiple validation errors
// returned by ListPRTaskResp.ValidateAll() if the designated constraints
// aren't met.
type ListPRTaskRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPRTaskRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPRTaskRespMultiError) AllErrors() []error { return m }

// ListPRTaskRespValidationError is the validation error returned by
// ListPRTaskResp.Validate if the designated constraints aren't met.
type ListPRTaskRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPRTaskRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPRTaskRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPRTaskRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPRTaskRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPRTaskRespValidationError) ErrorName() string { return "ListPRTaskRespValidationError" }

// Error satisfies the builtin error interface
func (e ListPRTaskRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPRTaskResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPRTaskRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPRTaskRespValidationError{}

// Validate checks the field values on Env with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Env) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Env with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in EnvMultiError, or nil if none found.
func (m *Env) ValidateAll() error {
	return m.validate(true)
}

func (m *Env) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Cluster

	// no validation rules for Namespace

	// no validation rules for Senv

	// no validation rules for EnvTarget

	// no validation rules for Env

	// no validation rules for ConfigId

	// no validation rules for OfflineTaskRunId

	// no validation rules for OfflineSubtaskRunId

	if len(errors) > 0 {
		return EnvMultiError(errors)
	}

	return nil
}

// EnvMultiError is an error wrapping multiple validation errors returned by
// Env.ValidateAll() if the designated constraints aren't met.
type EnvMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnvMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnvMultiError) AllErrors() []error { return m }

// EnvValidationError is the validation error returned by Env.Validate if the
// designated constraints aren't met.
type EnvValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnvValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnvValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnvValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnvValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnvValidationError) ErrorName() string { return "EnvValidationError" }

// Error satisfies the builtin error interface
func (e EnvValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnv.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnvValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnvValidationError{}

// Validate checks the field values on Operator with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Operator) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Operator with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OperatorMultiError, or nil
// if none found.
func (m *Operator) ValidateAll() error {
	return m.validate(true)
}

func (m *Operator) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ChineseName

	// no validation rules for EmployeeNo

	if len(errors) > 0 {
		return OperatorMultiError(errors)
	}

	return nil
}

// OperatorMultiError is an error wrapping multiple validation errors returned
// by Operator.ValidateAll() if the designated constraints aren't met.
type OperatorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OperatorMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OperatorMultiError) AllErrors() []error { return m }

// OperatorValidationError is the validation error returned by
// Operator.Validate if the designated constraints aren't met.
type OperatorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OperatorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OperatorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OperatorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OperatorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OperatorValidationError) ErrorName() string { return "OperatorValidationError" }

// Error satisfies the builtin error interface
func (e OperatorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOperator.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OperatorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OperatorValidationError{}

// Validate checks the field values on
// LastRetryPipelineRunOfflineCanarySubTasksResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LastRetryPipelineRunOfflineCanarySubTasksResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LastRetryPipelineRunOfflineCanarySubTasksResp with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// LastRetryPipelineRunOfflineCanarySubTasksRespMultiError, or nil if none found.
func (m *LastRetryPipelineRunOfflineCanarySubTasksResp) ValidateAll() error {
	return m.validate(true)
}

func (m *LastRetryPipelineRunOfflineCanarySubTasksResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppId

	// no validation rules for AppName

	// no validation rules for ImageUrl

	// no validation rules for Branch

	for idx, item := range m.GetEnvs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LastRetryPipelineRunOfflineCanarySubTasksRespValidationError{
						field:  fmt.Sprintf("Envs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LastRetryPipelineRunOfflineCanarySubTasksRespValidationError{
						field:  fmt.Sprintf("Envs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LastRetryPipelineRunOfflineCanarySubTasksRespValidationError{
					field:  fmt.Sprintf("Envs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetOperator()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LastRetryPipelineRunOfflineCanarySubTasksRespValidationError{
					field:  "Operator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LastRetryPipelineRunOfflineCanarySubTasksRespValidationError{
					field:  "Operator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOperator()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LastRetryPipelineRunOfflineCanarySubTasksRespValidationError{
				field:  "Operator",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LastRetryPipelineRunOfflineCanarySubTasksRespMultiError(errors)
	}

	return nil
}

// LastRetryPipelineRunOfflineCanarySubTasksRespMultiError is an error wrapping
// multiple validation errors returned by
// LastRetryPipelineRunOfflineCanarySubTasksResp.ValidateAll() if the
// designated constraints aren't met.
type LastRetryPipelineRunOfflineCanarySubTasksRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LastRetryPipelineRunOfflineCanarySubTasksRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LastRetryPipelineRunOfflineCanarySubTasksRespMultiError) AllErrors() []error { return m }

// LastRetryPipelineRunOfflineCanarySubTasksRespValidationError is the
// validation error returned by
// LastRetryPipelineRunOfflineCanarySubTasksResp.Validate if the designated
// constraints aren't met.
type LastRetryPipelineRunOfflineCanarySubTasksRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LastRetryPipelineRunOfflineCanarySubTasksRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LastRetryPipelineRunOfflineCanarySubTasksRespValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LastRetryPipelineRunOfflineCanarySubTasksRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LastRetryPipelineRunOfflineCanarySubTasksRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LastRetryPipelineRunOfflineCanarySubTasksRespValidationError) ErrorName() string {
	return "LastRetryPipelineRunOfflineCanarySubTasksRespValidationError"
}

// Error satisfies the builtin error interface
func (e LastRetryPipelineRunOfflineCanarySubTasksRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLastRetryPipelineRunOfflineCanarySubTasksResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LastRetryPipelineRunOfflineCanarySubTasksRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LastRetryPipelineRunOfflineCanarySubTasksRespValidationError{}

// Validate checks the field values on GetPipelineRunGitRepoTopKRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetPipelineRunGitRepoTopKRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPipelineRunGitRepoTopKRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetPipelineRunGitRepoTopKRequestMultiError, or nil if none found.
func (m *GetPipelineRunGitRepoTopKRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPipelineRunGitRepoTopKRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RepoAddr

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPipelineRunGitRepoTopKRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPipelineRunGitRepoTopKRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPipelineRunGitRepoTopKRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPipelineRunGitRepoTopKRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPipelineRunGitRepoTopKRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPipelineRunGitRepoTopKRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TopK

	if len(errors) > 0 {
		return GetPipelineRunGitRepoTopKRequestMultiError(errors)
	}

	return nil
}

// GetPipelineRunGitRepoTopKRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetPipelineRunGitRepoTopKRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPipelineRunGitRepoTopKRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPipelineRunGitRepoTopKRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPipelineRunGitRepoTopKRequestMultiError) AllErrors() []error { return m }

// GetPipelineRunGitRepoTopKRequestValidationError is the validation error
// returned by GetPipelineRunGitRepoTopKRequest.Validate if the designated
// constraints aren't met.
type GetPipelineRunGitRepoTopKRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPipelineRunGitRepoTopKRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPipelineRunGitRepoTopKRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPipelineRunGitRepoTopKRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPipelineRunGitRepoTopKRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPipelineRunGitRepoTopKRequestValidationError) ErrorName() string {
	return "GetPipelineRunGitRepoTopKRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPipelineRunGitRepoTopKRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPipelineRunGitRepoTopKRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPipelineRunGitRepoTopKRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPipelineRunGitRepoTopKRequestValidationError{}

// Validate checks the field values on GetPipelineRunGitRepoTopKResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetPipelineRunGitRepoTopKResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPipelineRunGitRepoTopKResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPipelineRunGitRepoTopKResponseMultiError, or nil if none found.
func (m *GetPipelineRunGitRepoTopKResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPipelineRunGitRepoTopKResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetBranches() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPipelineRunGitRepoTopKResponseValidationError{
						field:  fmt.Sprintf("Branches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPipelineRunGitRepoTopKResponseValidationError{
						field:  fmt.Sprintf("Branches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPipelineRunGitRepoTopKResponseValidationError{
					field:  fmt.Sprintf("Branches[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetPipelineRunGitRepoTopKResponseMultiError(errors)
	}

	return nil
}

// GetPipelineRunGitRepoTopKResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetPipelineRunGitRepoTopKResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPipelineRunGitRepoTopKResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPipelineRunGitRepoTopKResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPipelineRunGitRepoTopKResponseMultiError) AllErrors() []error { return m }

// GetPipelineRunGitRepoTopKResponseValidationError is the validation error
// returned by GetPipelineRunGitRepoTopKResponse.Validate if the designated
// constraints aren't met.
type GetPipelineRunGitRepoTopKResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPipelineRunGitRepoTopKResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPipelineRunGitRepoTopKResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPipelineRunGitRepoTopKResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPipelineRunGitRepoTopKResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPipelineRunGitRepoTopKResponseValidationError) ErrorName() string {
	return "GetPipelineRunGitRepoTopKResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPipelineRunGitRepoTopKResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPipelineRunGitRepoTopKResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPipelineRunGitRepoTopKResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPipelineRunGitRepoTopKResponseValidationError{}

// Validate checks the field values on GetPRStatusResp_Task with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPRStatusResp_Task) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPRStatusResp_Task with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPRStatusResp_TaskMultiError, or nil if none found.
func (m *GetPRStatusResp_Task) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPRStatusResp_Task) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if all {
		switch v := interface{}(m.GetStartedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPRStatusResp_TaskValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPRStatusResp_TaskValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPRStatusResp_TaskValidationError{
				field:  "StartedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPRStatusResp_TaskValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPRStatusResp_TaskValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPRStatusResp_TaskValidationError{
				field:  "CompletedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetElapsedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPRStatusResp_TaskValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPRStatusResp_TaskValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetElapsedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPRStatusResp_TaskValidationError{
				field:  "ElapsedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for Type

	// no validation rules for Config

	// no validation rules for Result

	if len(errors) > 0 {
		return GetPRStatusResp_TaskMultiError(errors)
	}

	return nil
}

// GetPRStatusResp_TaskMultiError is an error wrapping multiple validation
// errors returned by GetPRStatusResp_Task.ValidateAll() if the designated
// constraints aren't met.
type GetPRStatusResp_TaskMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPRStatusResp_TaskMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPRStatusResp_TaskMultiError) AllErrors() []error { return m }

// GetPRStatusResp_TaskValidationError is the validation error returned by
// GetPRStatusResp_Task.Validate if the designated constraints aren't met.
type GetPRStatusResp_TaskValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPRStatusResp_TaskValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPRStatusResp_TaskValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPRStatusResp_TaskValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPRStatusResp_TaskValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPRStatusResp_TaskValidationError) ErrorName() string {
	return "GetPRStatusResp_TaskValidationError"
}

// Error satisfies the builtin error interface
func (e GetPRStatusResp_TaskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPRStatusResp_Task.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPRStatusResp_TaskValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPRStatusResp_TaskValidationError{}

// Validate checks the field values on GetPRStatusResp_Stage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPRStatusResp_Stage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPRStatusResp_Stage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPRStatusResp_StageMultiError, or nil if none found.
func (m *GetPRStatusResp_Stage) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPRStatusResp_Stage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if all {
		switch v := interface{}(m.GetStartedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPRStatusResp_StageValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPRStatusResp_StageValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPRStatusResp_StageValidationError{
				field:  "StartedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPRStatusResp_StageValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPRStatusResp_StageValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPRStatusResp_StageValidationError{
				field:  "CompletedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetElapsedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPRStatusResp_StageValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPRStatusResp_StageValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetElapsedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPRStatusResp_StageValidationError{
				field:  "ElapsedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTasks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPRStatusResp_StageValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPRStatusResp_StageValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPRStatusResp_StageValidationError{
					field:  fmt.Sprintf("Tasks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Status

	// no validation rules for Type

	if len(errors) > 0 {
		return GetPRStatusResp_StageMultiError(errors)
	}

	return nil
}

// GetPRStatusResp_StageMultiError is an error wrapping multiple validation
// errors returned by GetPRStatusResp_Stage.ValidateAll() if the designated
// constraints aren't met.
type GetPRStatusResp_StageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPRStatusResp_StageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPRStatusResp_StageMultiError) AllErrors() []error { return m }

// GetPRStatusResp_StageValidationError is the validation error returned by
// GetPRStatusResp_Stage.Validate if the designated constraints aren't met.
type GetPRStatusResp_StageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPRStatusResp_StageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPRStatusResp_StageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPRStatusResp_StageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPRStatusResp_StageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPRStatusResp_StageValidationError) ErrorName() string {
	return "GetPRStatusResp_StageValidationError"
}

// Error satisfies the builtin error interface
func (e GetPRStatusResp_StageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPRStatusResp_Stage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPRStatusResp_StageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPRStatusResp_StageValidationError{}

// Validate checks the field values on TaskRunRecordsResp_TaskRunRecord with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *TaskRunRecordsResp_TaskRunRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskRunRecordsResp_TaskRunRecord with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TaskRunRecordsResp_TaskRunRecordMultiError, or nil if none found.
func (m *TaskRunRecordsResp_TaskRunRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskRunRecordsResp_TaskRunRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Config

	if len(errors) > 0 {
		return TaskRunRecordsResp_TaskRunRecordMultiError(errors)
	}

	return nil
}

// TaskRunRecordsResp_TaskRunRecordMultiError is an error wrapping multiple
// validation errors returned by
// TaskRunRecordsResp_TaskRunRecord.ValidateAll() if the designated
// constraints aren't met.
type TaskRunRecordsResp_TaskRunRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskRunRecordsResp_TaskRunRecordMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskRunRecordsResp_TaskRunRecordMultiError) AllErrors() []error { return m }

// TaskRunRecordsResp_TaskRunRecordValidationError is the validation error
// returned by TaskRunRecordsResp_TaskRunRecord.Validate if the designated
// constraints aren't met.
type TaskRunRecordsResp_TaskRunRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskRunRecordsResp_TaskRunRecordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskRunRecordsResp_TaskRunRecordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskRunRecordsResp_TaskRunRecordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskRunRecordsResp_TaskRunRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskRunRecordsResp_TaskRunRecordValidationError) ErrorName() string {
	return "TaskRunRecordsResp_TaskRunRecordValidationError"
}

// Error satisfies the builtin error interface
func (e TaskRunRecordsResp_TaskRunRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskRunRecordsResp_TaskRunRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskRunRecordsResp_TaskRunRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskRunRecordsResp_TaskRunRecordValidationError{}

// Validate checks the field values on
// GetPipelineRunGitRepoTopKResponse_BranchStat with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetPipelineRunGitRepoTopKResponse_BranchStat) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetPipelineRunGitRepoTopKResponse_BranchStat with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetPipelineRunGitRepoTopKResponse_BranchStatMultiError, or nil if none found.
func (m *GetPipelineRunGitRepoTopKResponse_BranchStat) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPipelineRunGitRepoTopKResponse_BranchStat) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Branch

	// no validation rules for Count

	if len(errors) > 0 {
		return GetPipelineRunGitRepoTopKResponse_BranchStatMultiError(errors)
	}

	return nil
}

// GetPipelineRunGitRepoTopKResponse_BranchStatMultiError is an error wrapping
// multiple validation errors returned by
// GetPipelineRunGitRepoTopKResponse_BranchStat.ValidateAll() if the
// designated constraints aren't met.
type GetPipelineRunGitRepoTopKResponse_BranchStatMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPipelineRunGitRepoTopKResponse_BranchStatMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPipelineRunGitRepoTopKResponse_BranchStatMultiError) AllErrors() []error { return m }

// GetPipelineRunGitRepoTopKResponse_BranchStatValidationError is the
// validation error returned by
// GetPipelineRunGitRepoTopKResponse_BranchStat.Validate if the designated
// constraints aren't met.
type GetPipelineRunGitRepoTopKResponse_BranchStatValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPipelineRunGitRepoTopKResponse_BranchStatValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPipelineRunGitRepoTopKResponse_BranchStatValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPipelineRunGitRepoTopKResponse_BranchStatValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPipelineRunGitRepoTopKResponse_BranchStatValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPipelineRunGitRepoTopKResponse_BranchStatValidationError) ErrorName() string {
	return "GetPipelineRunGitRepoTopKResponse_BranchStatValidationError"
}

// Error satisfies the builtin error interface
func (e GetPipelineRunGitRepoTopKResponse_BranchStatValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPipelineRunGitRepoTopKResponse_BranchStat.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPipelineRunGitRepoTopKResponse_BranchStatValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPipelineRunGitRepoTopKResponse_BranchStatValidationError{}
