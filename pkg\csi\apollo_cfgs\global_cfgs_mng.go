package apollo_cfgs

type IGlobalCfgsMng interface {
}

const (
	TESTING    EnvType = "testing"
	DEV        EnvType = "dev"
	PRODUCTION EnvType = "production"
	PREVIEW    EnvType = "preview"
)

var envTypeEnumMapping = map[EnvType]int{
	DEV:        1,
	TESTING:    2,
	PREVIEW:    3,
	PRODUCTION: 4,
}

type EnvType string

type SyncGlobalCfgsArgs struct {
	ProjectID int64  `json:"projectId"` // 项目ID
	UserGroup string `json:"userGroup"` // 用户群组
	CfgFile   string `json:"cfgFile"`   // 配置子目录&文件名
	ApolloNS  string `json:"apolloNS"`  // apollo namespace
}

// 管理节点配置的下载、监听，以及 取消监听
type globalCfgsMng struct {
	*apolloClientMng
}

// 想优化下全局配置，同一Node上所有的POD共享同一份
// 全部配置更需要灰度能力，如果做到Node级别所有Pod共享，灰度能力就无法实现

func InitGlobalCfgsMng() (mng IGlobalCfgsMng, err error) {
	apolloMng, err := InitApolloClientMng()
	if err != nil {

		return
	}
	cfgsMng := &globalCfgsMng{
		apolloClientMng: apolloMng,
	}
	return cfgsMng, nil
}

// func (m *globalCfgsMng) StartGlobalCfgs(args SyncGlobalCfgsArgs) (err error) {
// 	podListeners := make(map[string]*nodeCfgListener, len(args.PodCfgsInfo.DyCfgs))
// 	for _, dyCfg := range args.PodCfgsInfo.DyCfgs {
// 		apolloNS := &config.AppConfig{
// 			AppID:          m.apolloAppIds[args.PodCfgsInfo.Env],
// 			Cluster:        "default",
// 			IP:             m.apolloIP,
// 			NamespaceName:  dyCfg.ApolloNS,
// 			IsBackupConfig: true,
// 			Secret:         m.apolloAppSecrets[args.PodCfgsInfo.Env],
// 			Label:          args.PodCfgsInfo.AppName,
// 		}

// 		listener := &nodeCfgListener{}
// 		err = listener.Init(apolloNS, args.PodInfo.PvNodePath, dyCfg.CfgFile)
// 		if err != nil {
// 			return
// 		}

// 		podListeners[dyCfg.ApolloNS] = listener
// 	}

// 	m.AddPodListeners(args.PodInfo.UID, podListeners)

// 	return
// }

// func (m *globalCfgsMng) StopPodCfgs(podId string) {
// 	listeners := m.GetPodListeners(podId)
// 	if listeners == nil {
// 		return
// 	}

// 	for _, listener := range listeners {
// 		listener.Close()
// 	}

// 	m.DelPodListeners(podId)
// }
