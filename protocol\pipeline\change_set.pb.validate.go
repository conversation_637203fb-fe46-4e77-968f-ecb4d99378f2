// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: pipeline/change_set.proto

package pipeline

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetChgSetResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetChgSetResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChgSetResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetChgSetRespMultiError, or
// nil if none found.
func (m *GetChgSetResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChgSetResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for TemplateId

	if all {
		switch v := interface{}(m.GetDeployTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChgSetRespValidationError{
					field:  "DeployTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChgSetRespValidationError{
					field:  "DeployTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeployTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChgSetRespValidationError{
				field:  "DeployTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDuration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChgSetRespValidationError{
					field:  "Duration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChgSetRespValidationError{
					field:  "Duration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDuration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChgSetRespValidationError{
				field:  "Duration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetStages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetChgSetRespValidationError{
						field:  fmt.Sprintf("Stages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetChgSetRespValidationError{
						field:  fmt.Sprintf("Stages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetChgSetRespValidationError{
					field:  fmt.Sprintf("Stages[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Status

	// no validation rules for ProjectId

	if len(errors) > 0 {
		return GetChgSetRespMultiError(errors)
	}

	return nil
}

// GetChgSetRespMultiError is an error wrapping multiple validation errors
// returned by GetChgSetResp.ValidateAll() if the designated constraints
// aren't met.
type GetChgSetRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChgSetRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChgSetRespMultiError) AllErrors() []error { return m }

// GetChgSetRespValidationError is the validation error returned by
// GetChgSetResp.Validate if the designated constraints aren't met.
type GetChgSetRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChgSetRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChgSetRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChgSetRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChgSetRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChgSetRespValidationError) ErrorName() string { return "GetChgSetRespValidationError" }

// Error satisfies the builtin error interface
func (e GetChgSetRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChgSetResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChgSetRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChgSetRespValidationError{}

// Validate checks the field values on GetChgSetReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetChgSetReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChgSetReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetChgSetReqMultiError, or
// nil if none found.
func (m *GetChgSetReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChgSetReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetChgSetReqMultiError(errors)
	}

	return nil
}

// GetChgSetReqMultiError is an error wrapping multiple validation errors
// returned by GetChgSetReq.ValidateAll() if the designated constraints aren't met.
type GetChgSetReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChgSetReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChgSetReqMultiError) AllErrors() []error { return m }

// GetChgSetReqValidationError is the validation error returned by
// GetChgSetReq.Validate if the designated constraints aren't met.
type GetChgSetReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChgSetReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChgSetReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChgSetReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChgSetReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChgSetReqValidationError) ErrorName() string { return "GetChgSetReqValidationError" }

// Error satisfies the builtin error interface
func (e GetChgSetReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChgSetReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChgSetReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChgSetReqValidationError{}

// Validate checks the field values on GetChgSetTaskReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetChgSetTaskReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChgSetTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetChgSetTaskReqMultiError, or nil if none found.
func (m *GetChgSetTaskReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChgSetTaskReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tid

	// no validation rules for StageId

	// no validation rules for Type

	// no validation rules for ChangeSetId

	if len(errors) > 0 {
		return GetChgSetTaskReqMultiError(errors)
	}

	return nil
}

// GetChgSetTaskReqMultiError is an error wrapping multiple validation errors
// returned by GetChgSetTaskReq.ValidateAll() if the designated constraints
// aren't met.
type GetChgSetTaskReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChgSetTaskReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChgSetTaskReqMultiError) AllErrors() []error { return m }

// GetChgSetTaskReqValidationError is the validation error returned by
// GetChgSetTaskReq.Validate if the designated constraints aren't met.
type GetChgSetTaskReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChgSetTaskReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChgSetTaskReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChgSetTaskReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChgSetTaskReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChgSetTaskReqValidationError) ErrorName() string { return "GetChgSetTaskReqValidationError" }

// Error satisfies the builtin error interface
func (e GetChgSetTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChgSetTaskReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChgSetTaskReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChgSetTaskReqValidationError{}

// Validate checks the field values on PipelineRunResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PipelineRunResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PipelineRunResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PipelineRunRespMultiError, or nil if none found.
func (m *PipelineRunResp) ValidateAll() error {
	return m.validate(true)
}

func (m *PipelineRunResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetStartedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PipelineRunRespValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PipelineRunRespValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PipelineRunRespValidationError{
				field:  "StartedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PipelineRunRespValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PipelineRunRespValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PipelineRunRespValidationError{
				field:  "CompletedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetElapsedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PipelineRunRespValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PipelineRunRespValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetElapsedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PipelineRunRespValidationError{
				field:  "ElapsedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetStages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PipelineRunRespValidationError{
						field:  fmt.Sprintf("Stages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PipelineRunRespValidationError{
						field:  fmt.Sprintf("Stages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PipelineRunRespValidationError{
					field:  fmt.Sprintf("Stages[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Status

	// no validation rules for Branch

	// no validation rules for BuildNumber

	// no validation rules for PipelineId

	// no validation rules for AppId

	// no validation rules for AppName

	// no validation rules for PipelineName

	if len(errors) > 0 {
		return PipelineRunRespMultiError(errors)
	}

	return nil
}

// PipelineRunRespMultiError is an error wrapping multiple validation errors
// returned by PipelineRunResp.ValidateAll() if the designated constraints
// aren't met.
type PipelineRunRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PipelineRunRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PipelineRunRespMultiError) AllErrors() []error { return m }

// PipelineRunRespValidationError is the validation error returned by
// PipelineRunResp.Validate if the designated constraints aren't met.
type PipelineRunRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PipelineRunRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PipelineRunRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PipelineRunRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PipelineRunRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PipelineRunRespValidationError) ErrorName() string { return "PipelineRunRespValidationError" }

// Error satisfies the builtin error interface
func (e PipelineRunRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPipelineRunResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PipelineRunRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PipelineRunRespValidationError{}

// Validate checks the field values on GetChgSetTaskResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetChgSetTaskResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChgSetTaskResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetChgSetTaskRespMultiError, or nil if none found.
func (m *GetChgSetTaskResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChgSetTaskResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for TaskId

	if all {
		switch v := interface{}(m.GetDuration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChgSetTaskRespValidationError{
					field:  "Duration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChgSetTaskRespValidationError{
					field:  "Duration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDuration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChgSetTaskRespValidationError{
				field:  "Duration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for Type

	// no validation rules for Config

	// no validation rules for ChangeSetId

	// no validation rules for ChangeSetStageId

	if len(errors) > 0 {
		return GetChgSetTaskRespMultiError(errors)
	}

	return nil
}

// GetChgSetTaskRespMultiError is an error wrapping multiple validation errors
// returned by GetChgSetTaskResp.ValidateAll() if the designated constraints
// aren't met.
type GetChgSetTaskRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChgSetTaskRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChgSetTaskRespMultiError) AllErrors() []error { return m }

// GetChgSetTaskRespValidationError is the validation error returned by
// GetChgSetTaskResp.Validate if the designated constraints aren't met.
type GetChgSetTaskRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChgSetTaskRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChgSetTaskRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChgSetTaskRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChgSetTaskRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChgSetTaskRespValidationError) ErrorName() string {
	return "GetChgSetTaskRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetChgSetTaskRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChgSetTaskResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChgSetTaskRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChgSetTaskRespValidationError{}

// Validate checks the field values on ChgSetRelatedPipelineRunsResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChgSetRelatedPipelineRunsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChgSetRelatedPipelineRunsResp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ChgSetRelatedPipelineRunsRespMultiError, or nil if none found.
func (m *ChgSetRelatedPipelineRunsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ChgSetRelatedPipelineRunsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPipelineRuns() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ChgSetRelatedPipelineRunsRespValidationError{
						field:  fmt.Sprintf("PipelineRuns[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ChgSetRelatedPipelineRunsRespValidationError{
						field:  fmt.Sprintf("PipelineRuns[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ChgSetRelatedPipelineRunsRespValidationError{
					field:  fmt.Sprintf("PipelineRuns[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ChgSetRelatedPipelineRunsRespMultiError(errors)
	}

	return nil
}

// ChgSetRelatedPipelineRunsRespMultiError is an error wrapping multiple
// validation errors returned by ChgSetRelatedPipelineRunsResp.ValidateAll()
// if the designated constraints aren't met.
type ChgSetRelatedPipelineRunsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChgSetRelatedPipelineRunsRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChgSetRelatedPipelineRunsRespMultiError) AllErrors() []error { return m }

// ChgSetRelatedPipelineRunsRespValidationError is the validation error
// returned by ChgSetRelatedPipelineRunsResp.Validate if the designated
// constraints aren't met.
type ChgSetRelatedPipelineRunsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChgSetRelatedPipelineRunsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChgSetRelatedPipelineRunsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChgSetRelatedPipelineRunsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChgSetRelatedPipelineRunsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChgSetRelatedPipelineRunsRespValidationError) ErrorName() string {
	return "ChgSetRelatedPipelineRunsRespValidationError"
}

// Error satisfies the builtin error interface
func (e ChgSetRelatedPipelineRunsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChgSetRelatedPipelineRunsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChgSetRelatedPipelineRunsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChgSetRelatedPipelineRunsRespValidationError{}

// Validate checks the field values on ChgSetTaskStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ChgSetTaskStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChgSetTaskStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChgSetTaskStatusMultiError, or nil if none found.
func (m *ChgSetTaskStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *ChgSetTaskStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Status

	if len(errors) > 0 {
		return ChgSetTaskStatusMultiError(errors)
	}

	return nil
}

// ChgSetTaskStatusMultiError is an error wrapping multiple validation errors
// returned by ChgSetTaskStatus.ValidateAll() if the designated constraints
// aren't met.
type ChgSetTaskStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChgSetTaskStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChgSetTaskStatusMultiError) AllErrors() []error { return m }

// ChgSetTaskStatusValidationError is the validation error returned by
// ChgSetTaskStatus.Validate if the designated constraints aren't met.
type ChgSetTaskStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChgSetTaskStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChgSetTaskStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChgSetTaskStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChgSetTaskStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChgSetTaskStatusValidationError) ErrorName() string { return "ChgSetTaskStatusValidationError" }

// Error satisfies the builtin error interface
func (e ChgSetTaskStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChgSetTaskStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChgSetTaskStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChgSetTaskStatusValidationError{}

// Validate checks the field values on ChgSetUpdateStatusResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChgSetUpdateStatusResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChgSetUpdateStatusResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChgSetUpdateStatusRespMultiError, or nil if none found.
func (m *ChgSetUpdateStatusResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ChgSetUpdateStatusResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return ChgSetUpdateStatusRespMultiError(errors)
	}

	return nil
}

// ChgSetUpdateStatusRespMultiError is an error wrapping multiple validation
// errors returned by ChgSetUpdateStatusResp.ValidateAll() if the designated
// constraints aren't met.
type ChgSetUpdateStatusRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChgSetUpdateStatusRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChgSetUpdateStatusRespMultiError) AllErrors() []error { return m }

// ChgSetUpdateStatusRespValidationError is the validation error returned by
// ChgSetUpdateStatusResp.Validate if the designated constraints aren't met.
type ChgSetUpdateStatusRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChgSetUpdateStatusRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChgSetUpdateStatusRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChgSetUpdateStatusRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChgSetUpdateStatusRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChgSetUpdateStatusRespValidationError) ErrorName() string {
	return "ChgSetUpdateStatusRespValidationError"
}

// Error satisfies the builtin error interface
func (e ChgSetUpdateStatusRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChgSetUpdateStatusResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChgSetUpdateStatusRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChgSetUpdateStatusRespValidationError{}

// Validate checks the field values on UpdateChgSetTaskConfigReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateChgSetTaskConfigReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateChgSetTaskConfigReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateChgSetTaskConfigReqMultiError, or nil if none found.
func (m *UpdateChgSetTaskConfigReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateChgSetTaskConfigReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for Config

	if len(errors) > 0 {
		return UpdateChgSetTaskConfigReqMultiError(errors)
	}

	return nil
}

// UpdateChgSetTaskConfigReqMultiError is an error wrapping multiple validation
// errors returned by UpdateChgSetTaskConfigReq.ValidateAll() if the
// designated constraints aren't met.
type UpdateChgSetTaskConfigReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateChgSetTaskConfigReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateChgSetTaskConfigReqMultiError) AllErrors() []error { return m }

// UpdateChgSetTaskConfigReqValidationError is the validation error returned by
// UpdateChgSetTaskConfigReq.Validate if the designated constraints aren't met.
type UpdateChgSetTaskConfigReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateChgSetTaskConfigReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateChgSetTaskConfigReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateChgSetTaskConfigReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateChgSetTaskConfigReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateChgSetTaskConfigReqValidationError) ErrorName() string {
	return "UpdateChgSetTaskConfigReqValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateChgSetTaskConfigReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateChgSetTaskConfigReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateChgSetTaskConfigReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateChgSetTaskConfigReqValidationError{}

// Validate checks the field values on GetChgSetResp_Task with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetChgSetResp_Task) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChgSetResp_Task with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetChgSetResp_TaskMultiError, or nil if none found.
func (m *GetChgSetResp_Task) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChgSetResp_Task) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for TaskId

	if all {
		switch v := interface{}(m.GetDuration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChgSetResp_TaskValidationError{
					field:  "Duration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChgSetResp_TaskValidationError{
					field:  "Duration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDuration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChgSetResp_TaskValidationError{
				field:  "Duration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for Type

	// no validation rules for Config

	if len(errors) > 0 {
		return GetChgSetResp_TaskMultiError(errors)
	}

	return nil
}

// GetChgSetResp_TaskMultiError is an error wrapping multiple validation errors
// returned by GetChgSetResp_Task.ValidateAll() if the designated constraints
// aren't met.
type GetChgSetResp_TaskMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChgSetResp_TaskMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChgSetResp_TaskMultiError) AllErrors() []error { return m }

// GetChgSetResp_TaskValidationError is the validation error returned by
// GetChgSetResp_Task.Validate if the designated constraints aren't met.
type GetChgSetResp_TaskValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChgSetResp_TaskValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChgSetResp_TaskValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChgSetResp_TaskValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChgSetResp_TaskValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChgSetResp_TaskValidationError) ErrorName() string {
	return "GetChgSetResp_TaskValidationError"
}

// Error satisfies the builtin error interface
func (e GetChgSetResp_TaskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChgSetResp_Task.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChgSetResp_TaskValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChgSetResp_TaskValidationError{}

// Validate checks the field values on GetChgSetResp_Stage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetChgSetResp_Stage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChgSetResp_Stage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetChgSetResp_StageMultiError, or nil if none found.
func (m *GetChgSetResp_Stage) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChgSetResp_Stage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for StageId

	if all {
		switch v := interface{}(m.GetDuration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChgSetResp_StageValidationError{
					field:  "Duration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChgSetResp_StageValidationError{
					field:  "Duration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDuration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChgSetResp_StageValidationError{
				field:  "Duration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTasks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetChgSetResp_StageValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetChgSetResp_StageValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetChgSetResp_StageValidationError{
					field:  fmt.Sprintf("Tasks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Status

	if len(errors) > 0 {
		return GetChgSetResp_StageMultiError(errors)
	}

	return nil
}

// GetChgSetResp_StageMultiError is an error wrapping multiple validation
// errors returned by GetChgSetResp_Stage.ValidateAll() if the designated
// constraints aren't met.
type GetChgSetResp_StageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChgSetResp_StageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChgSetResp_StageMultiError) AllErrors() []error { return m }

// GetChgSetResp_StageValidationError is the validation error returned by
// GetChgSetResp_Stage.Validate if the designated constraints aren't met.
type GetChgSetResp_StageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChgSetResp_StageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChgSetResp_StageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChgSetResp_StageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChgSetResp_StageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChgSetResp_StageValidationError) ErrorName() string {
	return "GetChgSetResp_StageValidationError"
}

// Error satisfies the builtin error interface
func (e GetChgSetResp_StageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChgSetResp_Stage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChgSetResp_StageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChgSetResp_StageValidationError{}

// Validate checks the field values on PipelineRunResp_Task with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PipelineRunResp_Task) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PipelineRunResp_Task with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PipelineRunResp_TaskMultiError, or nil if none found.
func (m *PipelineRunResp_Task) ValidateAll() error {
	return m.validate(true)
}

func (m *PipelineRunResp_Task) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if all {
		switch v := interface{}(m.GetStartedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PipelineRunResp_TaskValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PipelineRunResp_TaskValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PipelineRunResp_TaskValidationError{
				field:  "StartedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PipelineRunResp_TaskValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PipelineRunResp_TaskValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PipelineRunResp_TaskValidationError{
				field:  "CompletedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetElapsedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PipelineRunResp_TaskValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PipelineRunResp_TaskValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetElapsedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PipelineRunResp_TaskValidationError{
				field:  "ElapsedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for Type

	// no validation rules for Config

	// no validation rules for TaskId

	// no validation rules for Result

	if len(errors) > 0 {
		return PipelineRunResp_TaskMultiError(errors)
	}

	return nil
}

// PipelineRunResp_TaskMultiError is an error wrapping multiple validation
// errors returned by PipelineRunResp_Task.ValidateAll() if the designated
// constraints aren't met.
type PipelineRunResp_TaskMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PipelineRunResp_TaskMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PipelineRunResp_TaskMultiError) AllErrors() []error { return m }

// PipelineRunResp_TaskValidationError is the validation error returned by
// PipelineRunResp_Task.Validate if the designated constraints aren't met.
type PipelineRunResp_TaskValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PipelineRunResp_TaskValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PipelineRunResp_TaskValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PipelineRunResp_TaskValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PipelineRunResp_TaskValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PipelineRunResp_TaskValidationError) ErrorName() string {
	return "PipelineRunResp_TaskValidationError"
}

// Error satisfies the builtin error interface
func (e PipelineRunResp_TaskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPipelineRunResp_Task.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PipelineRunResp_TaskValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PipelineRunResp_TaskValidationError{}

// Validate checks the field values on PipelineRunResp_Stage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PipelineRunResp_Stage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PipelineRunResp_Stage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PipelineRunResp_StageMultiError, or nil if none found.
func (m *PipelineRunResp_Stage) ValidateAll() error {
	return m.validate(true)
}

func (m *PipelineRunResp_Stage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if all {
		switch v := interface{}(m.GetStartedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PipelineRunResp_StageValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PipelineRunResp_StageValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PipelineRunResp_StageValidationError{
				field:  "StartedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PipelineRunResp_StageValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PipelineRunResp_StageValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PipelineRunResp_StageValidationError{
				field:  "CompletedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetElapsedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PipelineRunResp_StageValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PipelineRunResp_StageValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetElapsedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PipelineRunResp_StageValidationError{
				field:  "ElapsedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTasks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PipelineRunResp_StageValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PipelineRunResp_StageValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PipelineRunResp_StageValidationError{
					field:  fmt.Sprintf("Tasks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Status

	// no validation rules for StageId

	if len(errors) > 0 {
		return PipelineRunResp_StageMultiError(errors)
	}

	return nil
}

// PipelineRunResp_StageMultiError is an error wrapping multiple validation
// errors returned by PipelineRunResp_Stage.ValidateAll() if the designated
// constraints aren't met.
type PipelineRunResp_StageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PipelineRunResp_StageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PipelineRunResp_StageMultiError) AllErrors() []error { return m }

// PipelineRunResp_StageValidationError is the validation error returned by
// PipelineRunResp_Stage.Validate if the designated constraints aren't met.
type PipelineRunResp_StageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PipelineRunResp_StageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PipelineRunResp_StageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PipelineRunResp_StageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PipelineRunResp_StageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PipelineRunResp_StageValidationError) ErrorName() string {
	return "PipelineRunResp_StageValidationError"
}

// Error satisfies the builtin error interface
func (e PipelineRunResp_StageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPipelineRunResp_Stage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PipelineRunResp_StageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PipelineRunResp_StageValidationError{}
