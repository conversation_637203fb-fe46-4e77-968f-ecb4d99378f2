package config

import (
	"os"
	"strconv"
	"strings"
)

type Database struct {
	Host     string
	Username string
	Password string
	DBName   string
}

type Kafka struct {
	Name    string
	Brokers []string
	Topics  []string
}

type GRPCTarget struct {
	RepoSyncTarget    string
	PipelineRunTarget string
}

type Config struct {
	MySQL         Database
	PipelineEvent Kafka
	AppEvent      Kafka
	GRPCTarget    *GRPCTarget
	// 当前所属环境
	Env string
	// 只读层根目录
	LowerDirRootPath string
	// 只读层版本
	LowerDirVersion string
	// 代码仓库根目录
	RepoRootPath string
	TopK         int32
}

const (
	// db
	dbNameEnvKey     = "DB_NAME"
	dbHostEnvKey     = "DB_HOST"
	dbUsernameEnvKey = "DB_USERNAME"
	dbPasswordEnvKey = "DB_PASSWORD"

	// kafka
	kafkaBrokersEnvKey = "KAFKA_BROKERS"
	// pipeline
	kafkaPipelineTopicsEnvKey    = "KAFKA_PL_TOPICS"
	kafkaPipelineEventNameEnvKey = "KAFKA_PL_E_NAME"
	// app
	kafkaAppEventNameEnvKey = "KAFKA_APP_E_NAME"
	kafkaAppTopicsEnvKey    = "KAFKA_APP_TOPICS"

	// biz
	lowerDirRootPath      = "LOWER_DIR_ROOT_PATH"
	lowerDirVersionEnvKey = "LOWER_DIR_VERSION"
	repoRootEnvKey        = "REPO_ROOT_PATH"
	topKEnvKey            = "TOP_K"

	envEnvKey = "ENV"

	reposyncGRPCTargetEnvKey    = "REPOSYNC_GRPC_TARGET"
	pipelineRunGRPCTargetEnvKey = "PIPELINE_RUN_GRPC_TARGET"
)

func newConfig() *Config {
	brokers := strings.Split(os.Getenv(kafkaBrokersEnvKey), ",")
	cfg := &Config{
		MySQL: Database{
			Host:     os.Getenv(dbHostEnvKey),
			Username: os.Getenv(dbUsernameEnvKey),
			Password: os.Getenv(dbPasswordEnvKey),
			DBName:   os.Getenv(dbNameEnvKey),
		},
		PipelineEvent: Kafka{
			Name:    os.Getenv(kafkaPipelineEventNameEnvKey),
			Brokers: brokers,
			Topics:  strings.Split(os.Getenv(kafkaPipelineTopicsEnvKey), ","),
		},
		AppEvent: Kafka{
			Name:    os.Getenv(kafkaAppEventNameEnvKey),
			Brokers: brokers,
			Topics:  strings.Split(os.Getenv(kafkaAppTopicsEnvKey), ","),
		},
		GRPCTarget: &GRPCTarget{
			RepoSyncTarget:    os.Getenv(reposyncGRPCTargetEnvKey),
			PipelineRunTarget: os.Getenv(pipelineRunGRPCTargetEnvKey),
		},
		Env:              os.Getenv(envEnvKey),
		LowerDirRootPath: os.Getenv(lowerDirRootPath),
		LowerDirVersion:  os.Getenv(lowerDirVersionEnvKey),
		RepoRootPath:     os.Getenv(repoRootEnvKey),
		TopK:             int32(20), // default
	}

	topKStr := os.Getenv(topKEnvKey)
	if topKStr != "" {
		if topK, err := strconv.ParseInt(topKStr, 10, 32); err == nil { // parse success
			cfg.TopK = int32(topK)
		}
	}
	return cfg
}
