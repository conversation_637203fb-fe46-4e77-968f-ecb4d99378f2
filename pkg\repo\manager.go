package repo

import (
	"context"
	"sync"
)

func NewManager(rootPath string) *Manager {
	return &Manager{
		rootPath: rootPath,
	}
}

type Manager struct {
	rootPath    string   // e.g. /data/v2/2024-01
	repoMapping sync.Map // url: targetDir
}

func (m *Manager) run(ctx context.Context, url, branch string) {
	repository := New(url, m.rootPath)
	repository.SetBranch(branch)

	val, ok := m.repoMapping.LoadOrStore(repository.URL(), repository.TargetDir())
	if ok {
		repository.SetCloneFromLocalPath(val.(string))
	}

	repository.Clone(ctx)
}

func (m *Manager) Clone(ctx context.Context, url, branch string) {
	m.run(ctx, url, branch)
}
