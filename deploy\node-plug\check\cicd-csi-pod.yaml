apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  name: a-config-check
spec:
  replicas: 1
  selector:
    matchLabels:
      app: a-config-check
  template:
    metadata:
      annotations:
        com.ttyuyin.cicd.csi/pod.cfg.info: IHsKICAgICJhcHBOYW1lIjogImNoZW55LXNlciIsIAogICAgImVudiI6IDIsIAogICAgImVudlRhZyI6IDEsIAogICAgInN1YkVudiI6ICJjYW5uYXkiLCAKICAgICJkeUNmZ3MiOiBbCiAgICAgICAgewogICAgICAgICAgICAiYXBvbGxvTlMiOiAiY2lhLnR4dCIsIAogICAgICAgICAgICAiY2ZnRmlsZSI6ICIvY2ljZC9jaWEueWFtbCIKICAgICAgICB9CiAgICBdCn0=
      labels:
        app: a-config-check
    spec:
      nodeSelector:
        kubernetes.io/hostname: ************
      volumes:
      - name: pkgcache
        persistentVolumeClaim:
          claimName: tt-cicd-dy-config-pvc-t4322
      # - hostPath:
      #     path: /data/tt-cicd-csi/
      #     type: DirectoryOrCreate
      #   name: mnt
      containers:
      - image: cr.ttyuyin.com/devops/ubuntu:22.04
        name: a-config-check
        command: [ "/bin/bash", "-c", "--" ]
        args: [ "sleep infinity" ]
        volumeMounts:
          - name: pkgcache
            mountPath: /data/cicd-dy-conf/
          # - name: mnt
          #   mountPath: /data/tt-cicd-csi/
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        securityContext:
          privileged: true
        resources:
          limits:
            cpu: 1
            memory: 1024Mi
          requests:
            cpu: "0.2"
            memory: 128Mi