// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        (unknown)
// source: app/app.proto

package app

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type APP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name   string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Owners []int64 `protobuf:"varint,3,rep,packed,name=owners,proto3" json:"owners,omitempty"`
	// @gotags: json:"buildPath,omitempty"
	BuildPath string `protobuf:"bytes,4,opt,name=build_path,json=buildPath,proto3" json:"build_path,omitempty"`
	// @gotags: json:"repoAddr,omitempty"
	RepoAddr string `protobuf:"bytes,5,opt,name=repo_addr,json=repoAddr,proto3" json:"repo_addr,omitempty"`
	// @gotags: json:"langName,omitempty"
	LangName string `protobuf:"bytes,6,opt,name=lang_name,json=langName,proto3" json:"lang_name,omitempty"`
	// @gotags: json:"langVersion,omitempty"
	LangVersion string `protobuf:"bytes,7,opt,name=lang_version,json=langVersion,proto3" json:"lang_version,omitempty"`
	// @gotags: json:"projectID,omitempty"
	ProjectId   int64  `protobuf:"varint,8,opt,name=project_id,json=projectID,proto3" json:"project_id,omitempty"`
	Description string `protobuf:"bytes,9,opt,name=description,proto3" json:"description,omitempty"`
	// @gotags: json:"cmdbId,omitempty"
	CmdbId string `protobuf:"bytes,10,opt,name=cmdb_id,json=cmdbId,proto3" json:"cmdb_id,omitempty"`
}

func (x *APP) Reset() {
	*x = APP{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_app_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *APP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*APP) ProtoMessage() {}

func (x *APP) ProtoReflect() protoreflect.Message {
	mi := &file_app_app_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use APP.ProtoReflect.Descriptor instead.
func (*APP) Descriptor() ([]byte, []int) {
	return file_app_app_proto_rawDescGZIP(), []int{0}
}

func (x *APP) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *APP) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *APP) GetOwners() []int64 {
	if x != nil {
		return x.Owners
	}
	return nil
}

func (x *APP) GetBuildPath() string {
	if x != nil {
		return x.BuildPath
	}
	return ""
}

func (x *APP) GetRepoAddr() string {
	if x != nil {
		return x.RepoAddr
	}
	return ""
}

func (x *APP) GetLangName() string {
	if x != nil {
		return x.LangName
	}
	return ""
}

func (x *APP) GetLangVersion() string {
	if x != nil {
		return x.LangVersion
	}
	return ""
}

func (x *APP) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *APP) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *APP) GetCmdbId() string {
	if x != nil {
		return x.CmdbId
	}
	return ""
}

type AppParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// @gotags: json:"projectID,omitempty"
	BranchSearch string `protobuf:"bytes,2,opt,name=branch_search,json=branchSearch,proto3" json:"branch_search,omitempty"`
	Regex        string `protobuf:"bytes,3,opt,name=regex,proto3" json:"regex,omitempty"`
}

func (x *AppParam) Reset() {
	*x = AppParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_app_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppParam) ProtoMessage() {}

func (x *AppParam) ProtoReflect() protoreflect.Message {
	mi := &file_app_app_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppParam.ProtoReflect.Descriptor instead.
func (*AppParam) Descriptor() ([]byte, []int) {
	return file_app_app_proto_rawDescGZIP(), []int{1}
}

func (x *AppParam) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AppParam) GetBranchSearch() string {
	if x != nil {
		return x.BranchSearch
	}
	return ""
}

func (x *AppParam) GetRegex() string {
	if x != nil {
		return x.Regex
	}
	return ""
}

type AppBranchList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"branchList,omitempty"
	BranchList []string `protobuf:"bytes,1,rep,name=branch_list,json=branchList,proto3" json:"branch_list,omitempty"`
}

func (x *AppBranchList) Reset() {
	*x = AppBranchList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_app_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppBranchList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppBranchList) ProtoMessage() {}

func (x *AppBranchList) ProtoReflect() protoreflect.Message {
	mi := &file_app_app_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppBranchList.ProtoReflect.Descriptor instead.
func (*AppBranchList) Descriptor() ([]byte, []int) {
	return file_app_app_proto_rawDescGZIP(), []int{2}
}

func (x *AppBranchList) GetBranchList() []string {
	if x != nil {
		return x.BranchList
	}
	return nil
}

type AppsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        []int64 `protobuf:"varint,1,rep,packed,name=id,proto3" json:"id,omitempty"`
	ProjectId int64   `protobuf:"varint,2,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
}

func (x *AppsReq) Reset() {
	*x = AppsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_app_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppsReq) ProtoMessage() {}

func (x *AppsReq) ProtoReflect() protoreflect.Message {
	mi := &file_app_app_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppsReq.ProtoReflect.Descriptor instead.
func (*AppsReq) Descriptor() ([]byte, []int) {
	return file_app_app_proto_rawDescGZIP(), []int{3}
}

func (x *AppsReq) GetId() []int64 {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *AppsReq) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

type AppList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Apps []*APP `protobuf:"bytes,1,rep,name=apps,proto3" json:"apps,omitempty"`
}

func (x *AppList) Reset() {
	*x = AppList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_app_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppList) ProtoMessage() {}

func (x *AppList) ProtoReflect() protoreflect.Message {
	mi := &file_app_app_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppList.ProtoReflect.Descriptor instead.
func (*AppList) Descriptor() ([]byte, []int) {
	return file_app_app_proto_rawDescGZIP(), []int{4}
}

func (x *AppList) GetApps() []*APP {
	if x != nil {
		return x.Apps
	}
	return nil
}

type GetDeployMsgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetDeployMsgReq) Reset() {
	*x = GetDeployMsgReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_app_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeployMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeployMsgReq) ProtoMessage() {}

func (x *GetDeployMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_app_app_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeployMsgReq.ProtoReflect.Descriptor instead.
func (*GetDeployMsgReq) Descriptor() ([]byte, []int) {
	return file_app_app_proto_rawDescGZIP(), []int{5}
}

func (x *GetDeployMsgReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetDeployMsgResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level string `protobuf:"bytes,1,opt,name=level,proto3" json:"level,omitempty"`
	// @gotags: json:"matchLabels,omitempty"
	MatchLabels map[string]string `protobuf:"bytes,2,rep,name=match_labels,json=matchLabels,proto3" json:"match_labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// @gotags: json:"serviceLabels,omitempty"
	ServiceLabels map[string]string `protobuf:"bytes,3,rep,name=service_labels,json=serviceLabels,proto3" json:"service_labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// @gotags: json:"projectId"
	ProjectId int64 `protobuf:"varint,4,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// cmdb id
	CmdbId string `protobuf:"bytes,5,opt,name=cmdb_id,json=cmdbId,proto3" json:"cmdb_id,omitempty"`
	// 开发语言
	LangName string `protobuf:"bytes,6,opt,name=lang_name,json=langName,proto3" json:"lang_name,omitempty"`
	// 应用名称
	AppName string `protobuf:"bytes,7,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
}

func (x *GetDeployMsgResp) Reset() {
	*x = GetDeployMsgResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_app_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeployMsgResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeployMsgResp) ProtoMessage() {}

func (x *GetDeployMsgResp) ProtoReflect() protoreflect.Message {
	mi := &file_app_app_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeployMsgResp.ProtoReflect.Descriptor instead.
func (*GetDeployMsgResp) Descriptor() ([]byte, []int) {
	return file_app_app_proto_rawDescGZIP(), []int{6}
}

func (x *GetDeployMsgResp) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *GetDeployMsgResp) GetMatchLabels() map[string]string {
	if x != nil {
		return x.MatchLabels
	}
	return nil
}

func (x *GetDeployMsgResp) GetServiceLabels() map[string]string {
	if x != nil {
		return x.ServiceLabels
	}
	return nil
}

func (x *GetDeployMsgResp) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *GetDeployMsgResp) GetCmdbId() string {
	if x != nil {
		return x.CmdbId
	}
	return ""
}

func (x *GetDeployMsgResp) GetLangName() string {
	if x != nil {
		return x.LangName
	}
	return ""
}

func (x *GetDeployMsgResp) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

type GetAppInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId int64 `protobuf:"varint,1,opt,name=app_id,json=appID,proto3" json:"app_id,omitempty"`
}

func (x *GetAppInfoReq) Reset() {
	*x = GetAppInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_app_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppInfoReq) ProtoMessage() {}

func (x *GetAppInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_app_app_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppInfoReq.ProtoReflect.Descriptor instead.
func (*GetAppInfoReq) Descriptor() ([]byte, []int) {
	return file_app_app_proto_rawDescGZIP(), []int{7}
}

func (x *GetAppInfoReq) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

type GetAppInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	App     *APP                        `protobuf:"bytes,1,opt,name=app,proto3" json:"app,omitempty"`
	Project *GetAppInfoResp_ProjectInfo `protobuf:"bytes,2,opt,name=project,proto3" json:"project,omitempty"`
}

func (x *GetAppInfoResp) Reset() {
	*x = GetAppInfoResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_app_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppInfoResp) ProtoMessage() {}

func (x *GetAppInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_app_app_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppInfoResp.ProtoReflect.Descriptor instead.
func (*GetAppInfoResp) Descriptor() ([]byte, []int) {
	return file_app_app_proto_rawDescGZIP(), []int{8}
}

func (x *GetAppInfoResp) GetApp() *APP {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *GetAppInfoResp) GetProject() *GetAppInfoResp_ProjectInfo {
	if x != nil {
		return x.Project
	}
	return nil
}

type GetAppInfoResp_ProjectInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type        string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Identity    string `protobuf:"bytes,5,opt,name=identity,proto3" json:"identity,omitempty"`
}

func (x *GetAppInfoResp_ProjectInfo) Reset() {
	*x = GetAppInfoResp_ProjectInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_app_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppInfoResp_ProjectInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppInfoResp_ProjectInfo) ProtoMessage() {}

func (x *GetAppInfoResp_ProjectInfo) ProtoReflect() protoreflect.Message {
	mi := &file_app_app_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppInfoResp_ProjectInfo.ProtoReflect.Descriptor instead.
func (*GetAppInfoResp_ProjectInfo) Descriptor() ([]byte, []int) {
	return file_app_app_proto_rawDescGZIP(), []int{8, 0}
}

func (x *GetAppInfoResp_ProjectInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetAppInfoResp_ProjectInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetAppInfoResp_ProjectInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetAppInfoResp_ProjectInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GetAppInfoResp_ProjectInfo) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

var File_app_app_proto protoreflect.FileDescriptor

var file_app_app_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x61, 0x70, 0x70, 0x2f, 0x61, 0x70, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x03, 0x61, 0x70, 0x70, 0x22, 0x97, 0x02, 0x0a, 0x03, 0x41, 0x50, 0x50, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x75, 0x69, 0x6c,
	0x64, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x75,
	0x69, 0x6c, 0x64, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f,
	0x41, 0x64, 0x64, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6d, 0x64, 0x62, 0x5f, 0x69, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6d, 0x64, 0x62, 0x49, 0x64, 0x22, 0x55,
	0x0a, 0x08, 0x41, 0x70, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x72,
	0x61, 0x6e, 0x63, 0x68, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12,
	0x14, 0x0a, 0x05, 0x72, 0x65, 0x67, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x72, 0x65, 0x67, 0x65, 0x78, 0x22, 0x30, 0x0a, 0x0d, 0x41, 0x70, 0x70, 0x42, 0x72, 0x61, 0x6e,
	0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x72, 0x61,
	0x6e, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x38, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x22, 0x27, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x04,
	0x61, 0x70, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x61, 0x70, 0x70,
	0x2e, 0x41, 0x50, 0x50, 0x52, 0x04, 0x61, 0x70, 0x70, 0x73, 0x22, 0x21, 0x0a, 0x0f, 0x47, 0x65,
	0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0xb6, 0x03,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4d, 0x73, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x49, 0x0a, 0x0c, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4d, 0x73,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x12, 0x4f, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70,
	0x70, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4d, 0x73, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6d, 0x64, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6d, 0x64, 0x62, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x6c, 0x61, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x3e, 0x0a, 0x10, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x26, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x44, 0x22, 0xed,
	0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x1a, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x08,
	0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41, 0x50, 0x50, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x39, 0x0a,
	0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x1a, 0x83, 0x01, 0x0a, 0x0b, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x32, 0x8c,
	0x02, 0x0a, 0x0a, 0x41, 0x70, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x21, 0x0a,
	0x06, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x12, 0x0d, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41, 0x70,
	0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x1a, 0x08, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41, 0x50, 0x50,
	0x12, 0x35, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12,
	0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x1a, 0x13, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x35, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x41, 0x70,
	0x70, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x0d, 0x2e, 0x61, 0x70,
	0x70, 0x2e, 0x41, 0x70, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x1a, 0x12, 0x2e, 0x61, 0x70, 0x70,
	0x2e, 0x41, 0x70, 0x70, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2d,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x49, 0x64,
	0x73, 0x12, 0x0c, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41, 0x70, 0x70, 0x73, 0x52, 0x65, 0x71, 0x1a,
	0x0c, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3e, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4d, 0x73, 0x67,
	0x12, 0x14, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x65, 0x74,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73, 0x70, 0x42, 0x2d, 0x5a,
	0x2b, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x61, 0x72,
	0x6d, 0x6f, 0x6e, 0x79, 0x2f, 0x61, 0x70, 0x70, 0x3b, 0x61, 0x70, 0x70, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_app_app_proto_rawDescOnce sync.Once
	file_app_app_proto_rawDescData = file_app_app_proto_rawDesc
)

func file_app_app_proto_rawDescGZIP() []byte {
	file_app_app_proto_rawDescOnce.Do(func() {
		file_app_app_proto_rawDescData = protoimpl.X.CompressGZIP(file_app_app_proto_rawDescData)
	})
	return file_app_app_proto_rawDescData
}

var file_app_app_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_app_app_proto_goTypes = []interface{}{
	(*APP)(nil),                        // 0: app.APP
	(*AppParam)(nil),                   // 1: app.AppParam
	(*AppBranchList)(nil),              // 2: app.AppBranchList
	(*AppsReq)(nil),                    // 3: app.AppsReq
	(*AppList)(nil),                    // 4: app.AppList
	(*GetDeployMsgReq)(nil),            // 5: app.GetDeployMsgReq
	(*GetDeployMsgResp)(nil),           // 6: app.GetDeployMsgResp
	(*GetAppInfoReq)(nil),              // 7: app.GetAppInfoReq
	(*GetAppInfoResp)(nil),             // 8: app.GetAppInfoResp
	nil,                                // 9: app.GetDeployMsgResp.MatchLabelsEntry
	nil,                                // 10: app.GetDeployMsgResp.ServiceLabelsEntry
	(*GetAppInfoResp_ProjectInfo)(nil), // 11: app.GetAppInfoResp.ProjectInfo
}
var file_app_app_proto_depIdxs = []int32{
	0,  // 0: app.AppList.apps:type_name -> app.APP
	9,  // 1: app.GetDeployMsgResp.match_labels:type_name -> app.GetDeployMsgResp.MatchLabelsEntry
	10, // 2: app.GetDeployMsgResp.service_labels:type_name -> app.GetDeployMsgResp.ServiceLabelsEntry
	0,  // 3: app.GetAppInfoResp.app:type_name -> app.APP
	11, // 4: app.GetAppInfoResp.project:type_name -> app.GetAppInfoResp.ProjectInfo
	1,  // 5: app.AppService.GetApp:input_type -> app.AppParam
	7,  // 6: app.AppService.GetAppInfo:input_type -> app.GetAppInfoReq
	1,  // 7: app.AppService.GetAppBranchList:input_type -> app.AppParam
	3,  // 8: app.AppService.GetAppListByIds:input_type -> app.AppsReq
	5,  // 9: app.AppService.GetAppDeployMsg:input_type -> app.GetDeployMsgReq
	0,  // 10: app.AppService.GetApp:output_type -> app.APP
	8,  // 11: app.AppService.GetAppInfo:output_type -> app.GetAppInfoResp
	2,  // 12: app.AppService.GetAppBranchList:output_type -> app.AppBranchList
	4,  // 13: app.AppService.GetAppListByIds:output_type -> app.AppList
	6,  // 14: app.AppService.GetAppDeployMsg:output_type -> app.GetDeployMsgResp
	10, // [10:15] is the sub-list for method output_type
	5,  // [5:10] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_app_app_proto_init() }
func file_app_app_proto_init() {
	if File_app_app_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_app_app_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*APP); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_app_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_app_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppBranchList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_app_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_app_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_app_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeployMsgReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_app_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeployMsgResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_app_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_app_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppInfoResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_app_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppInfoResp_ProjectInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_app_app_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_app_app_proto_goTypes,
		DependencyIndexes: file_app_app_proto_depIdxs,
		MessageInfos:      file_app_app_proto_msgTypes,
	}.Build()
	File_app_app_proto = out.File
	file_app_app_proto_rawDesc = nil
	file_app_app_proto_goTypes = nil
	file_app_app_proto_depIdxs = nil
}
