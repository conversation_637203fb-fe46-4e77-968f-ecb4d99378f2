
apiVersion: batch/v1
kind: CronJob
metadata:
  name: cis-cleaner-qa
spec:
  schedule: "5 1 */1 * *"
  successfulJobsHistoryLimit: 
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          serviceAccount: cleaner
          nodeSelector:
            kubernetes.io/hostname: ************
          schedulerName: default-scheduler
          serviceAccountName: csi-driver
          terminationGracePeriodSeconds: 30
          volumes:
          - hostPath:
              path: /data/gitlab-sync/
              type: DirectoryOrCreate
            name: test-volume
          containers:
            - name: cis-cleaner-qa
              image: cr.ttyuyin.com/devops/cicd-csi-cleaner:v0.10
              command:
                - python
                - cleaner.py
              env:
                - name: NFS_VERSION
                  value: "3"
                - name: CSI_ENV
                  value: "qa"
                - name: MYSQL_HOST
                  value: "************"
                - name: MYSQL_PASSWORD
                  value: "vRcfj3W#2nGdBeu@"
                - name: MYSQL_USERNAME
                  value: "rd_dev"
                - name: MYSQL_DBNAME
                  value: "pipeline_test"
              securityContext:
                privileged: true
              terminationMessagePath: /dev/termination-log
              terminationMessagePolicy: File
              volumeMounts:
              - mountPath: /gitlab-repo
                name: test-volume
              - mountPath: /data/csi
                name: test-volume
          
