// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: pipeline/pipeline.proto

package pipeline

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Pipeline with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Pipeline) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Pipeline with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PipelineMultiError, or nil
// if none found.
func (m *Pipeline) ValidateAll() error {
	return m.validate(true)
}

func (m *Pipeline) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for AppId

	if len(errors) > 0 {
		return PipelineMultiError(errors)
	}

	return nil
}

// PipelineMultiError is an error wrapping multiple validation errors returned
// by Pipeline.ValidateAll() if the designated constraints aren't met.
type PipelineMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PipelineMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PipelineMultiError) AllErrors() []error { return m }

// PipelineValidationError is the validation error returned by
// Pipeline.Validate if the designated constraints aren't met.
type PipelineValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PipelineValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PipelineValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PipelineValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PipelineValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PipelineValidationError) ErrorName() string { return "PipelineValidationError" }

// Error satisfies the builtin error interface
func (e PipelineValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPipeline.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PipelineValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PipelineValidationError{}

// Validate checks the field values on Task with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Task) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Task with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TaskMultiError, or nil if none found.
func (m *Task) ValidateAll() error {
	return m.validate(true)
}

func (m *Task) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Config

	if len(errors) > 0 {
		return TaskMultiError(errors)
	}

	return nil
}

// TaskMultiError is an error wrapping multiple validation errors returned by
// Task.ValidateAll() if the designated constraints aren't met.
type TaskMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskMultiError) AllErrors() []error { return m }

// TaskValidationError is the validation error returned by Task.Validate if the
// designated constraints aren't met.
type TaskValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskValidationError) ErrorName() string { return "TaskValidationError" }

// Error satisfies the builtin error interface
func (e TaskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTask.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskValidationError{}

// Validate checks the field values on PipelineArray with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PipelineArray) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PipelineArray with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PipelineArrayMultiError, or
// nil if none found.
func (m *PipelineArray) ValidateAll() error {
	return m.validate(true)
}

func (m *PipelineArray) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPipelines() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PipelineArrayValidationError{
						field:  fmt.Sprintf("Pipelines[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PipelineArrayValidationError{
						field:  fmt.Sprintf("Pipelines[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PipelineArrayValidationError{
					field:  fmt.Sprintf("Pipelines[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PipelineArrayMultiError(errors)
	}

	return nil
}

// PipelineArrayMultiError is an error wrapping multiple validation errors
// returned by PipelineArray.ValidateAll() if the designated constraints
// aren't met.
type PipelineArrayMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PipelineArrayMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PipelineArrayMultiError) AllErrors() []error { return m }

// PipelineArrayValidationError is the validation error returned by
// PipelineArray.Validate if the designated constraints aren't met.
type PipelineArrayValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PipelineArrayValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PipelineArrayValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PipelineArrayValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PipelineArrayValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PipelineArrayValidationError) ErrorName() string { return "PipelineArrayValidationError" }

// Error satisfies the builtin error interface
func (e PipelineArrayValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPipelineArray.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PipelineArrayValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PipelineArrayValidationError{}

// Validate checks the field values on PipelineResult with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PipelineResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PipelineResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PipelineResultMultiError,
// or nil if none found.
func (m *PipelineResult) ValidateAll() error {
	return m.validate(true)
}

func (m *PipelineResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for TemplateId

	// no validation rules for Name

	// no validation rules for Type

	if len(errors) > 0 {
		return PipelineResultMultiError(errors)
	}

	return nil
}

// PipelineResultMultiError is an error wrapping multiple validation errors
// returned by PipelineResult.ValidateAll() if the designated constraints
// aren't met.
type PipelineResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PipelineResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PipelineResultMultiError) AllErrors() []error { return m }

// PipelineResultValidationError is the validation error returned by
// PipelineResult.Validate if the designated constraints aren't met.
type PipelineResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PipelineResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PipelineResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PipelineResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PipelineResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PipelineResultValidationError) ErrorName() string { return "PipelineResultValidationError" }

// Error satisfies the builtin error interface
func (e PipelineResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPipelineResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PipelineResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PipelineResultValidationError{}

// Validate checks the field values on PipelineCountReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PipelineCountReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PipelineCountReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PipelineCountReqMultiError, or nil if none found.
func (m *PipelineCountReq) ValidateAll() error {
	return m.validate(true)
}

func (m *PipelineCountReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return PipelineCountReqMultiError(errors)
	}

	return nil
}

// PipelineCountReqMultiError is an error wrapping multiple validation errors
// returned by PipelineCountReq.ValidateAll() if the designated constraints
// aren't met.
type PipelineCountReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PipelineCountReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PipelineCountReqMultiError) AllErrors() []error { return m }

// PipelineCountReqValidationError is the validation error returned by
// PipelineCountReq.Validate if the designated constraints aren't met.
type PipelineCountReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PipelineCountReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PipelineCountReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PipelineCountReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PipelineCountReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PipelineCountReqValidationError) ErrorName() string { return "PipelineCountReqValidationError" }

// Error satisfies the builtin error interface
func (e PipelineCountReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPipelineCountReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PipelineCountReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PipelineCountReqValidationError{}

// Validate checks the field values on PipelineAppReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PipelineAppReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PipelineAppReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PipelineAppReqMultiError,
// or nil if none found.
func (m *PipelineAppReq) ValidateAll() error {
	return m.validate(true)
}

func (m *PipelineAppReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppId

	// no validation rules for AppName

	// no validation rules for BuildPath

	// no validation rules for RepoAddr

	// no validation rules for Language

	// no validation rules for LanguageVersion

	if len(errors) > 0 {
		return PipelineAppReqMultiError(errors)
	}

	return nil
}

// PipelineAppReqMultiError is an error wrapping multiple validation errors
// returned by PipelineAppReq.ValidateAll() if the designated constraints
// aren't met.
type PipelineAppReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PipelineAppReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PipelineAppReqMultiError) AllErrors() []error { return m }

// PipelineAppReqValidationError is the validation error returned by
// PipelineAppReq.Validate if the designated constraints aren't met.
type PipelineAppReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PipelineAppReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PipelineAppReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PipelineAppReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PipelineAppReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PipelineAppReqValidationError) ErrorName() string { return "PipelineAppReqValidationError" }

// Error satisfies the builtin error interface
func (e PipelineAppReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPipelineAppReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PipelineAppReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PipelineAppReqValidationError{}

// Validate checks the field values on PipelineCountResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PipelineCountResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PipelineCountResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PipelineCountRespMultiError, or nil if none found.
func (m *PipelineCountResp) ValidateAll() error {
	return m.validate(true)
}

func (m *PipelineCountResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CountMap

	if len(errors) > 0 {
		return PipelineCountRespMultiError(errors)
	}

	return nil
}

// PipelineCountRespMultiError is an error wrapping multiple validation errors
// returned by PipelineCountResp.ValidateAll() if the designated constraints
// aren't met.
type PipelineCountRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PipelineCountRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PipelineCountRespMultiError) AllErrors() []error { return m }

// PipelineCountRespValidationError is the validation error returned by
// PipelineCountResp.Validate if the designated constraints aren't met.
type PipelineCountRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PipelineCountRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PipelineCountRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PipelineCountRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PipelineCountRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PipelineCountRespValidationError) ErrorName() string {
	return "PipelineCountRespValidationError"
}

// Error satisfies the builtin error interface
func (e PipelineCountRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPipelineCountResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PipelineCountRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PipelineCountRespValidationError{}

// Validate checks the field values on PipelineAppResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PipelineAppResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PipelineAppResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PipelineAppRespMultiError, or nil if none found.
func (m *PipelineAppResp) ValidateAll() error {
	return m.validate(true)
}

func (m *PipelineAppResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return PipelineAppRespMultiError(errors)
	}

	return nil
}

// PipelineAppRespMultiError is an error wrapping multiple validation errors
// returned by PipelineAppResp.ValidateAll() if the designated constraints
// aren't met.
type PipelineAppRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PipelineAppRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PipelineAppRespMultiError) AllErrors() []error { return m }

// PipelineAppRespValidationError is the validation error returned by
// PipelineAppResp.Validate if the designated constraints aren't met.
type PipelineAppRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PipelineAppRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PipelineAppRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PipelineAppRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PipelineAppRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PipelineAppRespValidationError) ErrorName() string { return "PipelineAppRespValidationError" }

// Error satisfies the builtin error interface
func (e PipelineAppRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPipelineAppResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PipelineAppRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PipelineAppRespValidationError{}

// Validate checks the field values on GetPipelineConfigReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPipelineConfigReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPipelineConfigReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPipelineConfigReqMultiError, or nil if none found.
func (m *GetPipelineConfigReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPipelineConfigReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetPipelineConfigReqMultiError(errors)
	}

	return nil
}

// GetPipelineConfigReqMultiError is an error wrapping multiple validation
// errors returned by GetPipelineConfigReq.ValidateAll() if the designated
// constraints aren't met.
type GetPipelineConfigReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPipelineConfigReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPipelineConfigReqMultiError) AllErrors() []error { return m }

// GetPipelineConfigReqValidationError is the validation error returned by
// GetPipelineConfigReq.Validate if the designated constraints aren't met.
type GetPipelineConfigReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPipelineConfigReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPipelineConfigReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPipelineConfigReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPipelineConfigReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPipelineConfigReqValidationError) ErrorName() string {
	return "GetPipelineConfigReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetPipelineConfigReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPipelineConfigReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPipelineConfigReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPipelineConfigReqValidationError{}

// Validate checks the field values on GetPipelineConfigResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPipelineConfigResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPipelineConfigResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPipelineConfigRespMultiError, or nil if none found.
func (m *GetPipelineConfigResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPipelineConfigResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	for idx, item := range m.GetStages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPipelineConfigRespValidationError{
						field:  fmt.Sprintf("Stages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPipelineConfigRespValidationError{
						field:  fmt.Sprintf("Stages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPipelineConfigRespValidationError{
					field:  fmt.Sprintf("Stages[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetPipelineConfigRespMultiError(errors)
	}

	return nil
}

// GetPipelineConfigRespMultiError is an error wrapping multiple validation
// errors returned by GetPipelineConfigResp.ValidateAll() if the designated
// constraints aren't met.
type GetPipelineConfigRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPipelineConfigRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPipelineConfigRespMultiError) AllErrors() []error { return m }

// GetPipelineConfigRespValidationError is the validation error returned by
// GetPipelineConfigResp.Validate if the designated constraints aren't met.
type GetPipelineConfigRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPipelineConfigRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPipelineConfigRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPipelineConfigRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPipelineConfigRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPipelineConfigRespValidationError) ErrorName() string {
	return "GetPipelineConfigRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetPipelineConfigRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPipelineConfigResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPipelineConfigRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPipelineConfigRespValidationError{}

// Validate checks the field values on UpdatePipelineTaskConfigReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdatePipelineTaskConfigReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePipelineTaskConfigReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdatePipelineTaskConfigReqMultiError, or nil if none found.
func (m *UpdatePipelineTaskConfigReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePipelineTaskConfigReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PipelineRunId

	// no validation rules for TaskId

	// no validation rules for TaskRunId

	// no validation rules for UpdatedPipelineConfig

	// no validation rules for UpdatedTaskRunConfig

	if len(errors) > 0 {
		return UpdatePipelineTaskConfigReqMultiError(errors)
	}

	return nil
}

// UpdatePipelineTaskConfigReqMultiError is an error wrapping multiple
// validation errors returned by UpdatePipelineTaskConfigReq.ValidateAll() if
// the designated constraints aren't met.
type UpdatePipelineTaskConfigReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePipelineTaskConfigReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePipelineTaskConfigReqMultiError) AllErrors() []error { return m }

// UpdatePipelineTaskConfigReqValidationError is the validation error returned
// by UpdatePipelineTaskConfigReq.Validate if the designated constraints
// aren't met.
type UpdatePipelineTaskConfigReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePipelineTaskConfigReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePipelineTaskConfigReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePipelineTaskConfigReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePipelineTaskConfigReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePipelineTaskConfigReqValidationError) ErrorName() string {
	return "UpdatePipelineTaskConfigReqValidationError"
}

// Error satisfies the builtin error interface
func (e UpdatePipelineTaskConfigReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePipelineTaskConfigReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePipelineTaskConfigReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePipelineTaskConfigReqValidationError{}

// Validate checks the field values on UpdatePipelineTaskMultiCloudConfigReq
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdatePipelineTaskMultiCloudConfigReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePipelineTaskMultiCloudConfigReq
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdatePipelineTaskMultiCloudConfigReqMultiError, or nil if none found.
func (m *UpdatePipelineTaskMultiCloudConfigReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePipelineTaskMultiCloudConfigReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PipelineRunId

	// no validation rules for StageRunId

	// no validation rules for UpdatedTaskRunMultiCloudConfig

	if len(errors) > 0 {
		return UpdatePipelineTaskMultiCloudConfigReqMultiError(errors)
	}

	return nil
}

// UpdatePipelineTaskMultiCloudConfigReqMultiError is an error wrapping
// multiple validation errors returned by
// UpdatePipelineTaskMultiCloudConfigReq.ValidateAll() if the designated
// constraints aren't met.
type UpdatePipelineTaskMultiCloudConfigReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePipelineTaskMultiCloudConfigReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePipelineTaskMultiCloudConfigReqMultiError) AllErrors() []error { return m }

// UpdatePipelineTaskMultiCloudConfigReqValidationError is the validation error
// returned by UpdatePipelineTaskMultiCloudConfigReq.Validate if the
// designated constraints aren't met.
type UpdatePipelineTaskMultiCloudConfigReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePipelineTaskMultiCloudConfigReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePipelineTaskMultiCloudConfigReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePipelineTaskMultiCloudConfigReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePipelineTaskMultiCloudConfigReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePipelineTaskMultiCloudConfigReqValidationError) ErrorName() string {
	return "UpdatePipelineTaskMultiCloudConfigReqValidationError"
}

// Error satisfies the builtin error interface
func (e UpdatePipelineTaskMultiCloudConfigReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePipelineTaskMultiCloudConfigReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePipelineTaskMultiCloudConfigReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePipelineTaskMultiCloudConfigReqValidationError{}

// Validate checks the field values on GetPipelineConfigResp_Stage with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPipelineConfigResp_Stage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPipelineConfigResp_Stage with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPipelineConfigResp_StageMultiError, or nil if none found.
func (m *GetPipelineConfigResp_Stage) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPipelineConfigResp_Stage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	for idx, item := range m.GetTasks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPipelineConfigResp_StageValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPipelineConfigResp_StageValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPipelineConfigResp_StageValidationError{
					field:  fmt.Sprintf("Tasks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Type

	if len(errors) > 0 {
		return GetPipelineConfigResp_StageMultiError(errors)
	}

	return nil
}

// GetPipelineConfigResp_StageMultiError is an error wrapping multiple
// validation errors returned by GetPipelineConfigResp_Stage.ValidateAll() if
// the designated constraints aren't met.
type GetPipelineConfigResp_StageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPipelineConfigResp_StageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPipelineConfigResp_StageMultiError) AllErrors() []error { return m }

// GetPipelineConfigResp_StageValidationError is the validation error returned
// by GetPipelineConfigResp_Stage.Validate if the designated constraints
// aren't met.
type GetPipelineConfigResp_StageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPipelineConfigResp_StageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPipelineConfigResp_StageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPipelineConfigResp_StageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPipelineConfigResp_StageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPipelineConfigResp_StageValidationError) ErrorName() string {
	return "GetPipelineConfigResp_StageValidationError"
}

// Error satisfies the builtin error interface
func (e GetPipelineConfigResp_StageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPipelineConfigResp_Stage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPipelineConfigResp_StageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPipelineConfigResp_StageValidationError{}

// Validate checks the field values on GetPipelineConfigResp_Task with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPipelineConfigResp_Task) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPipelineConfigResp_Task with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPipelineConfigResp_TaskMultiError, or nil if none found.
func (m *GetPipelineConfigResp_Task) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPipelineConfigResp_Task) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if all {
		switch v := interface{}(m.GetConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPipelineConfigResp_TaskValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPipelineConfigResp_TaskValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPipelineConfigResp_TaskValidationError{
				field:  "Config",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	if len(errors) > 0 {
		return GetPipelineConfigResp_TaskMultiError(errors)
	}

	return nil
}

// GetPipelineConfigResp_TaskMultiError is an error wrapping multiple
// validation errors returned by GetPipelineConfigResp_Task.ValidateAll() if
// the designated constraints aren't met.
type GetPipelineConfigResp_TaskMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPipelineConfigResp_TaskMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPipelineConfigResp_TaskMultiError) AllErrors() []error { return m }

// GetPipelineConfigResp_TaskValidationError is the validation error returned
// by GetPipelineConfigResp_Task.Validate if the designated constraints aren't met.
type GetPipelineConfigResp_TaskValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPipelineConfigResp_TaskValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPipelineConfigResp_TaskValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPipelineConfigResp_TaskValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPipelineConfigResp_TaskValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPipelineConfigResp_TaskValidationError) ErrorName() string {
	return "GetPipelineConfigResp_TaskValidationError"
}

// Error satisfies the builtin error interface
func (e GetPipelineConfigResp_TaskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPipelineConfigResp_Task.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPipelineConfigResp_TaskValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPipelineConfigResp_TaskValidationError{}
