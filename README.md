# Overview

此项目旨在开发一个k8s csi-driver, 用于向CICD平台提供流水线构建临时数据的存储空间

这个程序主要解决了如下问题：

- 基于大仓的代码拉取速度慢
- 基于大仓的构建数据持久化存储量大(要满足流水线重试的需要)
- 基于tekton worksapce,提供统一的pvc声明与实现

## 开发说明

- `reposync` 提供了rpc接口给 `csi-driver` 调用，用于预拉取仓库到本地。每月定时全量clone Git仓库作为只读层，供创建PV时的mount使用。
- `csi-driver` 监听 k8s，通过nfs提供PV给Tekton流水线共享数据使用。
- `csi-cleaner` Python代码，每天定时  全量清理30天之前运行流水线挂载的PV所占用的空间；30天内运行成功、取消的流水线挂载的PV空间。clean_expire_gitlab_repo.py每月清理只读层，频次太低，现在手动清理的。

由于进行 pb 定义，在使用前如果更改了协议，可以在项目根目录下使用 `make gen-proto` 重新生成 pb 文件

## 部署说明

开发环境以**容器**方式部署

1.镜像制作可通过Sentinel平台
 csi-driver 流水线：<https://cicd.ttyuyin.com/#/pipeline/single/detail/238430?projectId=1>
 reposync 流水线：<https://cicd.ttyuyin.com/#/pipeline/single/detail/236654?projectId=1>

 由于缺失 nodeselect 以及 privileged: true 功能，仅仅用来生成镜像。然后结合`./deploy/csi-driver_dev.yaml` `./deploy/csi-repo-sync_dev.yaml` 进行手动部署。
2. csi-cleaner 手动`docker build`+`./deploy/csi-cleaner/csi-driver_xx.yaml`直接部署。

生产环境以**虚拟机**方式部署。

1.宿主机监控和基本信息：<https://tt-telemetry.ttyuyin.com/#/host-detail?solo_id=649a8eddea2fdc5b4fea021c&cmdb_id=649a8ead64a21ef3da6117bb>
2.csi-driver 使用 supervisor 管理进程
3.reposync 使用 supervisor 管理进程
4.以 Linux cronjob 方式启用定时任务,脚本路径：/root/will/tool-scripts/v3/release_expired_pv.py

VM 系统重启，会导致 mount 数据丢失，故需要重启时执行再次 Mount 和 Export 命令。
工具代码位于`deploy/main.go`，工具放在 VM`/data/csi/dev/csi_remount/app`, 会读取`csi-overlay.ini`文件，执行命令。
重启之前通过 `mount | grep "overlay"  >csi-overlay.ini`  保存原来的 mount 数据。

## 功能说明

### `reposync`

### `csi-driver`

### `csi-cleaner`

## 设计初衷

TT的研发实践是基于大仓的服务管理，由于项目年限长，代码管控不规范，导致出现了数个3-6GB左右的git项目。

而这种现状又带来了上述的问题。

想像一下这样的场景: 有一个大仓(容量为5GB)下面有10个服务，这10个服务都接入了新CICD流水线，然后按开发节奏，每天会触发2次流水线构建，这样的使用场景下， 每天会消耗多少存储使用量，而流水线速度又会被拖慢多少呢?

简单算一下:

- 存储使用量: (5GB+ 500MB) *10* 2= 110GB/天 (按500MB的构建临时数据来算)
- 克隆耗时: (5120 / 20)  = 4.2min/条. (目前从cicd集群克隆代码的速度在20MB以下，平均在10MB/s, 同时多条并行克隆会更慢)

即使我们可以使用浅克隆, 本地克隆等方法去加速代码拉取的速度，但依然解决不了存储使用量过大和并行拉取速度慢的问题

### 如何解决

让我们先来分析一下流水线构建时，各类存储的使用场景:

- 代码仓库:  在流水线生命周期场景中，只需要下载代码仓库，而不需要修改相关代码并提交，因此代码仓库在属性上是`只读`的
- 构建数据: 在流水线生命周期中，会使用构建工具对源代码进行编译构建，这一块数据仅对此次流水线构建有效，但在重试场景中，需要持久化存储，在属性上是需要同时`读取,写入`，在容量上看，增长非常迅速，需要控制相关数据的生成量。
- 制品仓库: 存放目标程序的仓库，通常是二进制文件，或者一个oci镜像，在属性上是只需要`写入`的，在容量上看，增长非常迅速，但通过oci镜像分层技术，可以有效的进行镜像数据复用。
- 缓存数据: 缓存数据在设计上是跨流水线共享的，可以加速某个服务的构建过程，在属性上是需要同时`读取,写入`的， 在容量上来看，是增长缓慢的

通过上述分析能够知道，代码仓库做为只读的一种存储，在跨流水线构建场景中，实际上不会发生变化(或者这个变化是很小，递增的，可复现的)，那么我们能不能提前将代码仓库拉取到本地，在构建时仅进行修改呢？

答案是可以的,那就是写时复制机制

#### 写时复制(CoW)

提前将代码仓库利用定时任务克隆到本地，然后使用overlayfs的特性，每次构建时，都将代码仓库做为lowerdir的只读层，并将构建数据做为可写层，挂载成overlayfs,并导出成nfs，供流水线构建使用，流水线构建时的临时数据全部回写到upperdir中，进行持久存储，在后续重试中，进行组合挂载即可恢复。

在流水线看来，就像挂载了一个代码仓库到对应目录上，仅存在增量pull的数据量，拉取速度非常快

优点:

- 增量pull, 能实现传统的代码仓库持久化的加速效果，加速性能最好
- 代码拉取任务无需定制

缺点:

- 需要开发基于k8s的csi-driver,来将对应overlayfs的挂载细节屏蔽掉，提供统一的nfs挂载给流水线pod挂载使用
- overlayfs的nfs export实现，从实践上来讲并没有经过广泛验证，缺乏相关技术资料与benchmark数据

经过综合评估与考虑，写时复制方案虽然需要付出更多的开发成本，但在实现上可以最大化加速拉取性能，减少磁盘数据占用

相关实现效果图如下:

![image-20230625154003684](./pics/image-20230625154003684.png)

![image-20230630201740403](./pics/image-20230630201740403.png)

#### Pitfalls

有一些为了稳定性和迫于时间问题，而没有解决，或者没有得到验证的一些问题，以下是需要注意的:

- 生产为了稳定性考虑，使用了一台独立的Vm去部署csi应用，而不是部署在k8s中， 由此带来了额外的管理与维护成本.(生产的应用使用supervisor进行了统一管理，尽量减少相关操作负担)
- overlayfs+nfs的解决方案，在业内都很少存在相关实践，因此暂不清楚会不会碰上性能问题，但目前压测场景下，相关性能表现良好（50个pipeline同时挂载，20个大仓同时构建）
