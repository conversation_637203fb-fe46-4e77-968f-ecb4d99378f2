// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: app/app.proto

package app

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on APP with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *APP) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on APP with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in APPMultiError, or nil if none found.
func (m *APP) ValidateAll() error {
	return m.validate(true)
}

func (m *APP) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for BuildPath

	// no validation rules for RepoAddr

	// no validation rules for LangName

	// no validation rules for LangVersion

	// no validation rules for ProjectId

	// no validation rules for Description

	// no validation rules for CmdbId

	if len(errors) > 0 {
		return APPMultiError(errors)
	}

	return nil
}

// APPMultiError is an error wrapping multiple validation errors returned by
// APP.ValidateAll() if the designated constraints aren't met.
type APPMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m APPMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m APPMultiError) AllErrors() []error { return m }

// APPValidationError is the validation error returned by APP.Validate if the
// designated constraints aren't met.
type APPValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e APPValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e APPValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e APPValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e APPValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e APPValidationError) ErrorName() string { return "APPValidationError" }

// Error satisfies the builtin error interface
func (e APPValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAPP.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = APPValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = APPValidationError{}

// Validate checks the field values on AppParam with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AppParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AppParam with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AppParamMultiError, or nil
// if none found.
func (m *AppParam) ValidateAll() error {
	return m.validate(true)
}

func (m *AppParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for BranchSearch

	// no validation rules for Regex

	if len(errors) > 0 {
		return AppParamMultiError(errors)
	}

	return nil
}

// AppParamMultiError is an error wrapping multiple validation errors returned
// by AppParam.ValidateAll() if the designated constraints aren't met.
type AppParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppParamMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppParamMultiError) AllErrors() []error { return m }

// AppParamValidationError is the validation error returned by
// AppParam.Validate if the designated constraints aren't met.
type AppParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppParamValidationError) ErrorName() string { return "AppParamValidationError" }

// Error satisfies the builtin error interface
func (e AppParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppParamValidationError{}

// Validate checks the field values on AppBranchList with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AppBranchList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AppBranchList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AppBranchListMultiError, or
// nil if none found.
func (m *AppBranchList) ValidateAll() error {
	return m.validate(true)
}

func (m *AppBranchList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AppBranchListMultiError(errors)
	}

	return nil
}

// AppBranchListMultiError is an error wrapping multiple validation errors
// returned by AppBranchList.ValidateAll() if the designated constraints
// aren't met.
type AppBranchListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppBranchListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppBranchListMultiError) AllErrors() []error { return m }

// AppBranchListValidationError is the validation error returned by
// AppBranchList.Validate if the designated constraints aren't met.
type AppBranchListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppBranchListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppBranchListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppBranchListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppBranchListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppBranchListValidationError) ErrorName() string { return "AppBranchListValidationError" }

// Error satisfies the builtin error interface
func (e AppBranchListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppBranchList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppBranchListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppBranchListValidationError{}

// Validate checks the field values on AppsReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AppsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AppsReq with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AppsReqMultiError, or nil if none found.
func (m *AppsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AppsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	if len(errors) > 0 {
		return AppsReqMultiError(errors)
	}

	return nil
}

// AppsReqMultiError is an error wrapping multiple validation errors returned
// by AppsReq.ValidateAll() if the designated constraints aren't met.
type AppsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppsReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppsReqMultiError) AllErrors() []error { return m }

// AppsReqValidationError is the validation error returned by AppsReq.Validate
// if the designated constraints aren't met.
type AppsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppsReqValidationError) ErrorName() string { return "AppsReqValidationError" }

// Error satisfies the builtin error interface
func (e AppsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppsReqValidationError{}

// Validate checks the field values on AppList with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AppList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AppList with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AppListMultiError, or nil if none found.
func (m *AppList) ValidateAll() error {
	return m.validate(true)
}

func (m *AppList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetApps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AppListValidationError{
						field:  fmt.Sprintf("Apps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AppListValidationError{
						field:  fmt.Sprintf("Apps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AppListValidationError{
					field:  fmt.Sprintf("Apps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AppListMultiError(errors)
	}

	return nil
}

// AppListMultiError is an error wrapping multiple validation errors returned
// by AppList.ValidateAll() if the designated constraints aren't met.
type AppListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppListMultiError) AllErrors() []error { return m }

// AppListValidationError is the validation error returned by AppList.Validate
// if the designated constraints aren't met.
type AppListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppListValidationError) ErrorName() string { return "AppListValidationError" }

// Error satisfies the builtin error interface
func (e AppListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppListValidationError{}

// Validate checks the field values on GetDeployMsgReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetDeployMsgReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeployMsgReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDeployMsgReqMultiError, or nil if none found.
func (m *GetDeployMsgReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeployMsgReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetDeployMsgReqMultiError(errors)
	}

	return nil
}

// GetDeployMsgReqMultiError is an error wrapping multiple validation errors
// returned by GetDeployMsgReq.ValidateAll() if the designated constraints
// aren't met.
type GetDeployMsgReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeployMsgReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeployMsgReqMultiError) AllErrors() []error { return m }

// GetDeployMsgReqValidationError is the validation error returned by
// GetDeployMsgReq.Validate if the designated constraints aren't met.
type GetDeployMsgReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeployMsgReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeployMsgReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeployMsgReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeployMsgReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeployMsgReqValidationError) ErrorName() string { return "GetDeployMsgReqValidationError" }

// Error satisfies the builtin error interface
func (e GetDeployMsgReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeployMsgReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeployMsgReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeployMsgReqValidationError{}

// Validate checks the field values on GetDeployMsgResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetDeployMsgResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeployMsgResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDeployMsgRespMultiError, or nil if none found.
func (m *GetDeployMsgResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeployMsgResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Level

	// no validation rules for MatchLabels

	// no validation rules for ServiceLabels

	// no validation rules for ProjectId

	// no validation rules for CmdbId

	// no validation rules for LangName

	// no validation rules for AppName

	if len(errors) > 0 {
		return GetDeployMsgRespMultiError(errors)
	}

	return nil
}

// GetDeployMsgRespMultiError is an error wrapping multiple validation errors
// returned by GetDeployMsgResp.ValidateAll() if the designated constraints
// aren't met.
type GetDeployMsgRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeployMsgRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeployMsgRespMultiError) AllErrors() []error { return m }

// GetDeployMsgRespValidationError is the validation error returned by
// GetDeployMsgResp.Validate if the designated constraints aren't met.
type GetDeployMsgRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeployMsgRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeployMsgRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeployMsgRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeployMsgRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeployMsgRespValidationError) ErrorName() string { return "GetDeployMsgRespValidationError" }

// Error satisfies the builtin error interface
func (e GetDeployMsgRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeployMsgResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeployMsgRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeployMsgRespValidationError{}

// Validate checks the field values on GetAppInfoReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetAppInfoReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAppInfoReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetAppInfoReqMultiError, or
// nil if none found.
func (m *GetAppInfoReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAppInfoReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppId

	if len(errors) > 0 {
		return GetAppInfoReqMultiError(errors)
	}

	return nil
}

// GetAppInfoReqMultiError is an error wrapping multiple validation errors
// returned by GetAppInfoReq.ValidateAll() if the designated constraints
// aren't met.
type GetAppInfoReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAppInfoReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAppInfoReqMultiError) AllErrors() []error { return m }

// GetAppInfoReqValidationError is the validation error returned by
// GetAppInfoReq.Validate if the designated constraints aren't met.
type GetAppInfoReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAppInfoReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAppInfoReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAppInfoReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAppInfoReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAppInfoReqValidationError) ErrorName() string { return "GetAppInfoReqValidationError" }

// Error satisfies the builtin error interface
func (e GetAppInfoReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAppInfoReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAppInfoReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAppInfoReqValidationError{}

// Validate checks the field values on GetAppInfoResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetAppInfoResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAppInfoResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetAppInfoRespMultiError,
// or nil if none found.
func (m *GetAppInfoResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAppInfoResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetApp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAppInfoRespValidationError{
					field:  "App",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAppInfoRespValidationError{
					field:  "App",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAppInfoRespValidationError{
				field:  "App",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAppInfoRespValidationError{
					field:  "Project",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAppInfoRespValidationError{
					field:  "Project",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAppInfoRespValidationError{
				field:  "Project",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAppInfoRespMultiError(errors)
	}

	return nil
}

// GetAppInfoRespMultiError is an error wrapping multiple validation errors
// returned by GetAppInfoResp.ValidateAll() if the designated constraints
// aren't met.
type GetAppInfoRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAppInfoRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAppInfoRespMultiError) AllErrors() []error { return m }

// GetAppInfoRespValidationError is the validation error returned by
// GetAppInfoResp.Validate if the designated constraints aren't met.
type GetAppInfoRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAppInfoRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAppInfoRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAppInfoRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAppInfoRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAppInfoRespValidationError) ErrorName() string { return "GetAppInfoRespValidationError" }

// Error satisfies the builtin error interface
func (e GetAppInfoRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAppInfoResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAppInfoRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAppInfoRespValidationError{}

// Validate checks the field values on GetAppInfoResp_ProjectInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAppInfoResp_ProjectInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAppInfoResp_ProjectInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAppInfoResp_ProjectInfoMultiError, or nil if none found.
func (m *GetAppInfoResp_ProjectInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAppInfoResp_ProjectInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for Description

	// no validation rules for Identity

	if len(errors) > 0 {
		return GetAppInfoResp_ProjectInfoMultiError(errors)
	}

	return nil
}

// GetAppInfoResp_ProjectInfoMultiError is an error wrapping multiple
// validation errors returned by GetAppInfoResp_ProjectInfo.ValidateAll() if
// the designated constraints aren't met.
type GetAppInfoResp_ProjectInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAppInfoResp_ProjectInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAppInfoResp_ProjectInfoMultiError) AllErrors() []error { return m }

// GetAppInfoResp_ProjectInfoValidationError is the validation error returned
// by GetAppInfoResp_ProjectInfo.Validate if the designated constraints aren't met.
type GetAppInfoResp_ProjectInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAppInfoResp_ProjectInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAppInfoResp_ProjectInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAppInfoResp_ProjectInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAppInfoResp_ProjectInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAppInfoResp_ProjectInfoValidationError) ErrorName() string {
	return "GetAppInfoResp_ProjectInfoValidationError"
}

// Error satisfies the builtin error interface
func (e GetAppInfoResp_ProjectInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAppInfoResp_ProjectInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAppInfoResp_ProjectInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAppInfoResp_ProjectInfoValidationError{}
