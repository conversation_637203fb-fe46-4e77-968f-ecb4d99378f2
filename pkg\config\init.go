package config

import (
	"os"

	"github.com/joho/godotenv"

	"golang.ttyuyin.com/harmony/pkg/log"
)

var localConfig *Config

// Init 目前环境变量，在 reposync 和 csi-driver 中都会用到，因此作为一个公共的包
// 因为使用环境变量方式，因此需要在启动时需要加载读取环境变量，再使用 Current 获取已加载的环境变量
func Init(fp string) {
	err := godotenv.Load(fp)
	if err != nil {
		log.Errorf("loading .env file err: %v", err)
		os.Exit(1)
	}
	localConfig = newConfig()
}

func Current() *Config {
	return localConfig
}
