// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: rdp/reposync/v1/repo_sync.proto

package reposync

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateLowerDirRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLowerDirRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLowerDirRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLowerDirRequestMultiError, or nil if none found.
func (m *CreateLowerDirRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLowerDirRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FullPath

	// no validation rules for Version

	// no validation rules for RepoAddr

	// no validation rules for Branch

	if len(errors) > 0 {
		return CreateLowerDirRequestMultiError(errors)
	}

	return nil
}

// CreateLowerDirRequestMultiError is an error wrapping multiple validation
// errors returned by CreateLowerDirRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateLowerDirRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLowerDirRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLowerDirRequestMultiError) AllErrors() []error { return m }

// CreateLowerDirRequestValidationError is the validation error returned by
// CreateLowerDirRequest.Validate if the designated constraints aren't met.
type CreateLowerDirRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLowerDirRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLowerDirRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLowerDirRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLowerDirRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLowerDirRequestValidationError) ErrorName() string {
	return "CreateLowerDirRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLowerDirRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLowerDirRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLowerDirRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLowerDirRequestValidationError{}

// Validate checks the field values on CreateLowerDirResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLowerDirResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLowerDirResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLowerDirResponseMultiError, or nil if none found.
func (m *CreateLowerDirResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLowerDirResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CreateLowerDirResponseMultiError(errors)
	}

	return nil
}

// CreateLowerDirResponseMultiError is an error wrapping multiple validation
// errors returned by CreateLowerDirResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateLowerDirResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLowerDirResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLowerDirResponseMultiError) AllErrors() []error { return m }

// CreateLowerDirResponseValidationError is the validation error returned by
// CreateLowerDirResponse.Validate if the designated constraints aren't met.
type CreateLowerDirResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLowerDirResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLowerDirResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLowerDirResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLowerDirResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLowerDirResponseValidationError) ErrorName() string {
	return "CreateLowerDirResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLowerDirResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLowerDirResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLowerDirResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLowerDirResponseValidationError{}
