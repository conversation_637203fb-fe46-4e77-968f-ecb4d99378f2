# 该模板列举该服务用到的环境变量
# 使用时，需要将该文件拷贝到二进制目录下，并重命名为.env
#
# 模式，debug 是为了调试跳过部分逻辑和打印更详细的日志信息，生产环境下请设置为 prod
MODE="debug"
# ENV 是指 dev、qa、prod 等环境
ENV="qa"
# 只读层根目录
LOWER_DIR_ROOT_PATH="/gitlab-sync"
# 只读层当前版本
LOWER_DIR_VERSION="v2"
# 废弃
REPO_ROOT_PATH="/data/gitlab-sync"

# 数据库配置
DB_HOST="************"
DB_PORT="3306"
DB_USERNAME="rd_dev"
DB_PASSWORD=""
DB_NAME="app"

# gitlab http 账号配置
GIT_CREDENTIALS=""

# kafka 配置
KAFKA_BROKERS="************:9092"
# pipeline
KAFKA_PL_E_NAME="reposync-receive-pipeline"
KAFKA_PL_TOPICS="cicd-pipeline-event-local"
# app
KAFKA_APP_E_NAME="reposync-receive-app"
KAFKA_APP_TOPICS="cicd-app-event-local"

# 对外暴露的 grpc 地址
REPOSYNC_GRPC_TARGET=":9050"
# 连接到 pipeline 的 grpc 地址
PIPELINE_RUN_GRPC_TARGET=":9005"

# 暂未使用
LARK_APP_ID=""
LARK_APP_SECRET=""