// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: pipeline/change_set.proto

package pipeline

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ChangeSetService_GetChgSet_FullMethodName                          = "/pipeline.ChangeSetService/GetChgSet"
	ChangeSetService_GetChgSetByTaskId_FullMethodName                  = "/pipeline.ChangeSetService/GetChgSetByTaskId"
	ChangeSetService_GetChgSetRelatedPipelineRuns_FullMethodName       = "/pipeline.ChangeSetService/GetChgSetRelatedPipelineRuns"
	ChangeSetService_GetChgSetTaskBy_FullMethodName                    = "/pipeline.ChangeSetService/GetChgSetTaskBy"
	ChangeSetService_UpdateChgSetRelatedPipelinesStatus_FullMethodName = "/pipeline.ChangeSetService/UpdateChgSetRelatedPipelinesStatus"
	ChangeSetService_UpdateChgSetTaskConfig_FullMethodName             = "/pipeline.ChangeSetService/UpdateChgSetTaskConfig"
)

// ChangeSetServiceClient is the client API for ChangeSetService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ChangeSetServiceClient interface {
	GetChgSet(ctx context.Context, in *GetChgSetReq, opts ...grpc.CallOption) (*GetChgSetResp, error)
	GetChgSetByTaskId(ctx context.Context, in *GetChgSetTaskReq, opts ...grpc.CallOption) (*GetChgSetResp, error)
	GetChgSetRelatedPipelineRuns(ctx context.Context, in *GetChgSetReq, opts ...grpc.CallOption) (*ChgSetRelatedPipelineRunsResp, error)
	GetChgSetTaskBy(ctx context.Context, in *GetChgSetTaskReq, opts ...grpc.CallOption) (*GetChgSetTaskResp, error)
	UpdateChgSetRelatedPipelinesStatus(ctx context.Context, in *ChgSetTaskStatus, opts ...grpc.CallOption) (*ChgSetUpdateStatusResp, error)
	UpdateChgSetTaskConfig(ctx context.Context, in *UpdateChgSetTaskConfigReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type changeSetServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewChangeSetServiceClient(cc grpc.ClientConnInterface) ChangeSetServiceClient {
	return &changeSetServiceClient{cc}
}

func (c *changeSetServiceClient) GetChgSet(ctx context.Context, in *GetChgSetReq, opts ...grpc.CallOption) (*GetChgSetResp, error) {
	out := new(GetChgSetResp)
	err := c.cc.Invoke(ctx, ChangeSetService_GetChgSet_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *changeSetServiceClient) GetChgSetByTaskId(ctx context.Context, in *GetChgSetTaskReq, opts ...grpc.CallOption) (*GetChgSetResp, error) {
	out := new(GetChgSetResp)
	err := c.cc.Invoke(ctx, ChangeSetService_GetChgSetByTaskId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *changeSetServiceClient) GetChgSetRelatedPipelineRuns(ctx context.Context, in *GetChgSetReq, opts ...grpc.CallOption) (*ChgSetRelatedPipelineRunsResp, error) {
	out := new(ChgSetRelatedPipelineRunsResp)
	err := c.cc.Invoke(ctx, ChangeSetService_GetChgSetRelatedPipelineRuns_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *changeSetServiceClient) GetChgSetTaskBy(ctx context.Context, in *GetChgSetTaskReq, opts ...grpc.CallOption) (*GetChgSetTaskResp, error) {
	out := new(GetChgSetTaskResp)
	err := c.cc.Invoke(ctx, ChangeSetService_GetChgSetTaskBy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *changeSetServiceClient) UpdateChgSetRelatedPipelinesStatus(ctx context.Context, in *ChgSetTaskStatus, opts ...grpc.CallOption) (*ChgSetUpdateStatusResp, error) {
	out := new(ChgSetUpdateStatusResp)
	err := c.cc.Invoke(ctx, ChangeSetService_UpdateChgSetRelatedPipelinesStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *changeSetServiceClient) UpdateChgSetTaskConfig(ctx context.Context, in *UpdateChgSetTaskConfigReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ChangeSetService_UpdateChgSetTaskConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChangeSetServiceServer is the server API for ChangeSetService service.
// All implementations must embed UnimplementedChangeSetServiceServer
// for forward compatibility
type ChangeSetServiceServer interface {
	GetChgSet(context.Context, *GetChgSetReq) (*GetChgSetResp, error)
	GetChgSetByTaskId(context.Context, *GetChgSetTaskReq) (*GetChgSetResp, error)
	GetChgSetRelatedPipelineRuns(context.Context, *GetChgSetReq) (*ChgSetRelatedPipelineRunsResp, error)
	GetChgSetTaskBy(context.Context, *GetChgSetTaskReq) (*GetChgSetTaskResp, error)
	UpdateChgSetRelatedPipelinesStatus(context.Context, *ChgSetTaskStatus) (*ChgSetUpdateStatusResp, error)
	UpdateChgSetTaskConfig(context.Context, *UpdateChgSetTaskConfigReq) (*emptypb.Empty, error)
	mustEmbedUnimplementedChangeSetServiceServer()
}

// UnimplementedChangeSetServiceServer must be embedded to have forward compatible implementations.
type UnimplementedChangeSetServiceServer struct {
}

func (UnimplementedChangeSetServiceServer) GetChgSet(context.Context, *GetChgSetReq) (*GetChgSetResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChgSet not implemented")
}
func (UnimplementedChangeSetServiceServer) GetChgSetByTaskId(context.Context, *GetChgSetTaskReq) (*GetChgSetResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChgSetByTaskId not implemented")
}
func (UnimplementedChangeSetServiceServer) GetChgSetRelatedPipelineRuns(context.Context, *GetChgSetReq) (*ChgSetRelatedPipelineRunsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChgSetRelatedPipelineRuns not implemented")
}
func (UnimplementedChangeSetServiceServer) GetChgSetTaskBy(context.Context, *GetChgSetTaskReq) (*GetChgSetTaskResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChgSetTaskBy not implemented")
}
func (UnimplementedChangeSetServiceServer) UpdateChgSetRelatedPipelinesStatus(context.Context, *ChgSetTaskStatus) (*ChgSetUpdateStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateChgSetRelatedPipelinesStatus not implemented")
}
func (UnimplementedChangeSetServiceServer) UpdateChgSetTaskConfig(context.Context, *UpdateChgSetTaskConfigReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateChgSetTaskConfig not implemented")
}
func (UnimplementedChangeSetServiceServer) mustEmbedUnimplementedChangeSetServiceServer() {}

// UnsafeChangeSetServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChangeSetServiceServer will
// result in compilation errors.
type UnsafeChangeSetServiceServer interface {
	mustEmbedUnimplementedChangeSetServiceServer()
}

func RegisterChangeSetServiceServer(s grpc.ServiceRegistrar, srv ChangeSetServiceServer) {
	s.RegisterService(&ChangeSetService_ServiceDesc, srv)
}

func _ChangeSetService_GetChgSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChgSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChangeSetServiceServer).GetChgSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChangeSetService_GetChgSet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChangeSetServiceServer).GetChgSet(ctx, req.(*GetChgSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChangeSetService_GetChgSetByTaskId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChgSetTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChangeSetServiceServer).GetChgSetByTaskId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChangeSetService_GetChgSetByTaskId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChangeSetServiceServer).GetChgSetByTaskId(ctx, req.(*GetChgSetTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChangeSetService_GetChgSetRelatedPipelineRuns_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChgSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChangeSetServiceServer).GetChgSetRelatedPipelineRuns(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChangeSetService_GetChgSetRelatedPipelineRuns_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChangeSetServiceServer).GetChgSetRelatedPipelineRuns(ctx, req.(*GetChgSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChangeSetService_GetChgSetTaskBy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChgSetTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChangeSetServiceServer).GetChgSetTaskBy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChangeSetService_GetChgSetTaskBy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChangeSetServiceServer).GetChgSetTaskBy(ctx, req.(*GetChgSetTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChangeSetService_UpdateChgSetRelatedPipelinesStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChgSetTaskStatus)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChangeSetServiceServer).UpdateChgSetRelatedPipelinesStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChangeSetService_UpdateChgSetRelatedPipelinesStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChangeSetServiceServer).UpdateChgSetRelatedPipelinesStatus(ctx, req.(*ChgSetTaskStatus))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChangeSetService_UpdateChgSetTaskConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChgSetTaskConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChangeSetServiceServer).UpdateChgSetTaskConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChangeSetService_UpdateChgSetTaskConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChangeSetServiceServer).UpdateChgSetTaskConfig(ctx, req.(*UpdateChgSetTaskConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ChangeSetService_ServiceDesc is the grpc.ServiceDesc for ChangeSetService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ChangeSetService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pipeline.ChangeSetService",
	HandlerType: (*ChangeSetServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChgSet",
			Handler:    _ChangeSetService_GetChgSet_Handler,
		},
		{
			MethodName: "GetChgSetByTaskId",
			Handler:    _ChangeSetService_GetChgSetByTaskId_Handler,
		},
		{
			MethodName: "GetChgSetRelatedPipelineRuns",
			Handler:    _ChangeSetService_GetChgSetRelatedPipelineRuns_Handler,
		},
		{
			MethodName: "GetChgSetTaskBy",
			Handler:    _ChangeSetService_GetChgSetTaskBy_Handler,
		},
		{
			MethodName: "UpdateChgSetRelatedPipelinesStatus",
			Handler:    _ChangeSetService_UpdateChgSetRelatedPipelinesStatus_Handler,
		},
		{
			MethodName: "UpdateChgSetTaskConfig",
			Handler:    _ChangeSetService_UpdateChgSetTaskConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pipeline/change_set.proto",
}
