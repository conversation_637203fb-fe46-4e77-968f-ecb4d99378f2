package repo

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/require"
)

func Test_extractRepoDir(t *testing.T) {
	tcs := []struct {
		name     string
		repoAddr string
		repoDir  string
	}{
		{
			name:     "个人仓库",
			repoAddr: "https://gitlab.ttyuyin.com/will.zhang/repo-sync.git",
			repoDir:  "will.zhang/repo-sync",
		},
		{
			name:     "组仓库",
			repoAddr: "https://gitlab.ttyuyin.com/harmony/csi-driver.git",
			repoDir:  "harmony/csi-driver",
		},
		{
			name:     "子组仓库",
			repoAddr: "https://gitlab.ttyuyin.com/enigma/web/admin.git",
			repoDir:  "enigma/web/admin",
		},
		{
			name:     "子组仓库",
			repoAddr: "https://gitlab.ttyuyin.com/support-backend/oversea/se-asian-sdk/sdk-visual/code-generator.git",
			repoDir:  "support-backend/oversea/se-asian-sdk/sdk-visual/code-generator",
		},
		{
			name:     "仓库名带版本号",
			repoAddr: "https://gitlab.ttyuyin.com/data_dev/teh/spark-3.5.0.git",
			repoDir:  "data_dev/teh/spark-3.5.0",
		},
	}

	for _, tc := range tcs {
		t.Run(tc.name, func(t *testing.T) {
			repoDir := extractRepoDir(tc.repoAddr)

			require.Equal(t, tc.repoDir, repoDir)
		})
	}
}

func TestRepository_TargetDir(t *testing.T) {
	tcs := []struct {
		name      string
		repoURL   string
		rootPath  string
		branch    string
		targetDir string
	}{
		{
			name:      "加载默认分支到默认目录",
			repoURL:   "https://gitlab.ttyuyin.com/will.zhang/repo-sync.git",
			rootPath:  "",
			branch:    "",
			targetDir: "/data/will.zhang/repo-sync/master",
		},
		{
			name:      "加载 test 分支到默认目录",
			repoURL:   "https://gitlab.ttyuyin.com/will.zhang/repo-sync.git",
			rootPath:  "",
			branch:    "test",
			targetDir: "/data/will.zhang/repo-sync/test",
		},
		{
			name:      "加载默认分支到当月目录",
			repoURL:   "https://gitlab.ttyuyin.com/will.zhang/repo-sync.git",
			rootPath:  "/data/v2/2024-01",
			branch:    "",
			targetDir: "/data/v2/2024-01/will.zhang/repo-sync/master",
		},
		{
			name:      "加载 test 分支到当月目录",
			repoURL:   "https://gitlab.ttyuyin.com/will.zhang/repo-sync.git",
			rootPath:  "/data/v2/2024-01",
			branch:    "test",
			targetDir: "/data/v2/2024-01/will.zhang/repo-sync/test",
		},
	}

	for _, tc := range tcs {
		t.Run(tc.name, func(t *testing.T) {
			repo := New(tc.repoURL, tc.rootPath)
			if tc.branch != "" {
				repo.SetBranch(tc.branch)
			}

			require.Equal(t, tc.targetDir, repo.TargetDir())
		})
	}
}

func TestRepository_Clone(t *testing.T) {
	t.Skip()
	_ = os.Setenv("MODE", "debug")
	repo := New("https://gitlab.ttyuyin.com/harmony/csi-driver.git", "/tmp/v2")
	repo.Clone(context.Background())
}
