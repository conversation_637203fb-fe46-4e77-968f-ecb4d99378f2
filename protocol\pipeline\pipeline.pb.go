// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        (unknown)
// source: pipeline/pipeline.proto

package pipeline

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Pipeline struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AppId int64 `protobuf:"varint,2,opt,name=appId,proto3" json:"appId,omitempty"`
}

func (x *Pipeline) Reset() {
	*x = Pipeline{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pipeline) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pipeline) ProtoMessage() {}

func (x *Pipeline) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pipeline.ProtoReflect.Descriptor instead.
func (*Pipeline) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_proto_rawDescGZIP(), []int{0}
}

func (x *Pipeline) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Pipeline) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

type Task struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Config []byte `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *Task) Reset() {
	*x = Task{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Task) ProtoMessage() {}

func (x *Task) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Task.ProtoReflect.Descriptor instead.
func (*Task) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_proto_rawDescGZIP(), []int{1}
}

func (x *Task) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Task) GetConfig() []byte {
	if x != nil {
		return x.Config
	}
	return nil
}

type PipelineArray struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pipelines []*PipelineResult `protobuf:"bytes,1,rep,name=pipelines,proto3" json:"pipelines,omitempty"`
}

func (x *PipelineArray) Reset() {
	*x = PipelineArray{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineArray) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineArray) ProtoMessage() {}

func (x *PipelineArray) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineArray.ProtoReflect.Descriptor instead.
func (*PipelineArray) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_proto_rawDescGZIP(), []int{2}
}

func (x *PipelineArray) GetPipelines() []*PipelineResult {
	if x != nil {
		return x.Pipelines
	}
	return nil
}

type PipelineResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TemplateId int64  `protobuf:"varint,2,opt,name=templateId,proto3" json:"templateId,omitempty"`
	Name       string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Type       string `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *PipelineResult) Reset() {
	*x = PipelineResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineResult) ProtoMessage() {}

func (x *PipelineResult) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineResult.ProtoReflect.Descriptor instead.
func (*PipelineResult) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_proto_rawDescGZIP(), []int{3}
}

func (x *PipelineResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PipelineResult) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *PipelineResult) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PipelineResult) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type PipelineCountReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppIds []int64 `protobuf:"varint,1,rep,packed,name=appIds,proto3" json:"appIds,omitempty"`
}

func (x *PipelineCountReq) Reset() {
	*x = PipelineCountReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineCountReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineCountReq) ProtoMessage() {}

func (x *PipelineCountReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineCountReq.ProtoReflect.Descriptor instead.
func (*PipelineCountReq) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_proto_rawDescGZIP(), []int{4}
}

func (x *PipelineCountReq) GetAppIds() []int64 {
	if x != nil {
		return x.AppIds
	}
	return nil
}

type PipelineAppReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId           int64  `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppName         string `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	BuildPath       string `protobuf:"bytes,3,opt,name=build_path,json=buildPath,proto3" json:"build_path,omitempty"`
	RepoAddr        string `protobuf:"bytes,4,opt,name=repo_addr,json=repoAddr,proto3" json:"repo_addr,omitempty"`
	Language        string `protobuf:"bytes,5,opt,name=language,proto3" json:"language,omitempty"`
	LanguageVersion string `protobuf:"bytes,6,opt,name=language_version,json=languageVersion,proto3" json:"language_version,omitempty"`
}

func (x *PipelineAppReq) Reset() {
	*x = PipelineAppReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineAppReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineAppReq) ProtoMessage() {}

func (x *PipelineAppReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineAppReq.ProtoReflect.Descriptor instead.
func (*PipelineAppReq) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_proto_rawDescGZIP(), []int{5}
}

func (x *PipelineAppReq) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *PipelineAppReq) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *PipelineAppReq) GetBuildPath() string {
	if x != nil {
		return x.BuildPath
	}
	return ""
}

func (x *PipelineAppReq) GetRepoAddr() string {
	if x != nil {
		return x.RepoAddr
	}
	return ""
}

func (x *PipelineAppReq) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *PipelineAppReq) GetLanguageVersion() string {
	if x != nil {
		return x.LanguageVersion
	}
	return ""
}

type PipelineCountResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CountMap map[int64]int64 `protobuf:"bytes,1,rep,name=count_map,json=countMap,proto3" json:"count_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *PipelineCountResp) Reset() {
	*x = PipelineCountResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineCountResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineCountResp) ProtoMessage() {}

func (x *PipelineCountResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineCountResp.ProtoReflect.Descriptor instead.
func (*PipelineCountResp) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_proto_rawDescGZIP(), []int{6}
}

func (x *PipelineCountResp) GetCountMap() map[int64]int64 {
	if x != nil {
		return x.CountMap
	}
	return nil
}

type PipelineAppResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *PipelineAppResp) Reset() {
	*x = PipelineAppResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineAppResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineAppResp) ProtoMessage() {}

func (x *PipelineAppResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineAppResp.ProtoReflect.Descriptor instead.
func (*PipelineAppResp) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_proto_rawDescGZIP(), []int{7}
}

func (x *PipelineAppResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PipelineAppResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type GetPipelineConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: uri:"id"
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetPipelineConfigReq) Reset() {
	*x = GetPipelineConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPipelineConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPipelineConfigReq) ProtoMessage() {}

func (x *GetPipelineConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPipelineConfigReq.ProtoReflect.Descriptor instead.
func (*GetPipelineConfigReq) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_proto_rawDescGZIP(), []int{8}
}

func (x *GetPipelineConfigReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetPipelineConfigResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64                          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Stages []*GetPipelineConfigResp_Stage `protobuf:"bytes,2,rep,name=stages,proto3" json:"stages,omitempty"`
}

func (x *GetPipelineConfigResp) Reset() {
	*x = GetPipelineConfigResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPipelineConfigResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPipelineConfigResp) ProtoMessage() {}

func (x *GetPipelineConfigResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPipelineConfigResp.ProtoReflect.Descriptor instead.
func (*GetPipelineConfigResp) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_proto_rawDescGZIP(), []int{9}
}

func (x *GetPipelineConfigResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPipelineConfigResp) GetStages() []*GetPipelineConfigResp_Stage {
	if x != nil {
		return x.Stages
	}
	return nil
}

type UpdatePipelineTaskConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"pipelineRunId"
	PipelineRunId int64 `protobuf:"varint,1,opt,name=pipelineRunId,proto3" json:"pipelineRunId,omitempty"`
	// @gotags: json:"taskId"
	TaskId int64 `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// @gotags: json:"taskRunId"
	TaskRunId int64 `protobuf:"varint,3,opt,name=task_run_id,json=taskRunId,proto3" json:"task_run_id,omitempty"`
	// @gotags: json:"updatedPipelineConfig"
	UpdatedPipelineConfig []byte `protobuf:"bytes,4,opt,name=updated_pipeline_config,json=updatedPipelineConfig,proto3" json:"updated_pipeline_config,omitempty"`
	// @gotags: json:"updatedTaskRunConfig"
	UpdatedTaskRunConfig []byte `protobuf:"bytes,5,opt,name=updated_task_run_config,json=updatedTaskRunConfig,proto3" json:"updated_task_run_config,omitempty"`
}

func (x *UpdatePipelineTaskConfigReq) Reset() {
	*x = UpdatePipelineTaskConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePipelineTaskConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePipelineTaskConfigReq) ProtoMessage() {}

func (x *UpdatePipelineTaskConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePipelineTaskConfigReq.ProtoReflect.Descriptor instead.
func (*UpdatePipelineTaskConfigReq) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_proto_rawDescGZIP(), []int{10}
}

func (x *UpdatePipelineTaskConfigReq) GetPipelineRunId() int64 {
	if x != nil {
		return x.PipelineRunId
	}
	return 0
}

func (x *UpdatePipelineTaskConfigReq) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *UpdatePipelineTaskConfigReq) GetTaskRunId() int64 {
	if x != nil {
		return x.TaskRunId
	}
	return 0
}

func (x *UpdatePipelineTaskConfigReq) GetUpdatedPipelineConfig() []byte {
	if x != nil {
		return x.UpdatedPipelineConfig
	}
	return nil
}

func (x *UpdatePipelineTaskConfigReq) GetUpdatedTaskRunConfig() []byte {
	if x != nil {
		return x.UpdatedTaskRunConfig
	}
	return nil
}

type UpdatePipelineTaskMultiCloudConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"pipelineRunId"
	PipelineRunId int64 `protobuf:"varint,1,opt,name=pipelineRunId,proto3" json:"pipelineRunId,omitempty"`
	// @gotags: json:"stageRunId"
	StageRunId int64 `protobuf:"varint,3,opt,name=stage_run_id,json=stageRunId,proto3" json:"stage_run_id,omitempty"`
	// @gotags: json:"updatedTaskRunMultiCloudConfig"
	UpdatedTaskRunMultiCloudConfig []byte `protobuf:"bytes,5,opt,name=updated_task_run_multi_cloud_config,json=updatedTaskRunMultiCloudConfig,proto3" json:"updated_task_run_multi_cloud_config,omitempty"`
}

func (x *UpdatePipelineTaskMultiCloudConfigReq) Reset() {
	*x = UpdatePipelineTaskMultiCloudConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePipelineTaskMultiCloudConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePipelineTaskMultiCloudConfigReq) ProtoMessage() {}

func (x *UpdatePipelineTaskMultiCloudConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePipelineTaskMultiCloudConfigReq.ProtoReflect.Descriptor instead.
func (*UpdatePipelineTaskMultiCloudConfigReq) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_proto_rawDescGZIP(), []int{11}
}

func (x *UpdatePipelineTaskMultiCloudConfigReq) GetPipelineRunId() int64 {
	if x != nil {
		return x.PipelineRunId
	}
	return 0
}

func (x *UpdatePipelineTaskMultiCloudConfigReq) GetStageRunId() int64 {
	if x != nil {
		return x.StageRunId
	}
	return 0
}

func (x *UpdatePipelineTaskMultiCloudConfigReq) GetUpdatedTaskRunMultiCloudConfig() []byte {
	if x != nil {
		return x.UpdatedTaskRunMultiCloudConfig
	}
	return nil
}

type GetPipelineConfigResp_Stage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int64                         `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name  string                        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Tasks []*GetPipelineConfigResp_Task `protobuf:"bytes,3,rep,name=tasks,proto3" json:"tasks,omitempty"`
	Type  string                        `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *GetPipelineConfigResp_Stage) Reset() {
	*x = GetPipelineConfigResp_Stage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPipelineConfigResp_Stage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPipelineConfigResp_Stage) ProtoMessage() {}

func (x *GetPipelineConfigResp_Stage) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPipelineConfigResp_Stage.ProtoReflect.Descriptor instead.
func (*GetPipelineConfigResp_Stage) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_proto_rawDescGZIP(), []int{9, 0}
}

func (x *GetPipelineConfigResp_Stage) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPipelineConfigResp_Stage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetPipelineConfigResp_Stage) GetTasks() []*GetPipelineConfigResp_Task {
	if x != nil {
		return x.Tasks
	}
	return nil
}

func (x *GetPipelineConfigResp_Stage) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type GetPipelineConfigResp_Task struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name   string           `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Config *structpb.Struct `protobuf:"bytes,3,opt,name=config,proto3" json:"config,omitempty"`
	Type   string           `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *GetPipelineConfigResp_Task) Reset() {
	*x = GetPipelineConfigResp_Task{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_pipeline_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPipelineConfigResp_Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPipelineConfigResp_Task) ProtoMessage() {}

func (x *GetPipelineConfigResp_Task) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_pipeline_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPipelineConfigResp_Task.ProtoReflect.Descriptor instead.
func (*GetPipelineConfigResp_Task) Descriptor() ([]byte, []int) {
	return file_pipeline_pipeline_proto_rawDescGZIP(), []int{9, 1}
}

func (x *GetPipelineConfigResp_Task) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPipelineConfigResp_Task) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetPipelineConfigResp_Task) GetConfig() *structpb.Struct {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *GetPipelineConfigResp_Task) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

var File_pipeline_pipeline_proto protoreflect.FileDescriptor

var file_pipeline_pipeline_proto_rawDesc = []byte{
	0x0a, 0x17, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x30,
	0x0a, 0x08, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x22, 0x2e, 0x0a, 0x04, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x22, 0x47, 0x0a, 0x0d, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x72, 0x72, 0x61,
	0x79, 0x12, 0x36, 0x0a, 0x09, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x09,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x22, 0x68, 0x0a, 0x0e, 0x50, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x2a, 0x0a, 0x10, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x06, 0x61, 0x70, 0x70, 0x49, 0x64, 0x73, 0x22,
	0xc5, 0x01, 0x0a, 0x0e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x70, 0x70, 0x52,
	0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x41, 0x64, 0x64, 0x72,
	0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x29, 0x0a, 0x10,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x98, 0x01, 0x0a, 0x11, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x46, 0x0a,
	0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4d, 0x61, 0x70, 0x1a, 0x3b, 0x0a, 0x0d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61,
	0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x37, 0x0a, 0x0f, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x70,
	0x70, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x26, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x22, 0xd4, 0x02, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3d, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x67, 0x65, 0x73, 0x1a, 0x7b, 0x0a, 0x05,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x05, 0x74, 0x61, 0x73,
	0x6b, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x05,
	0x74, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x1a, 0x6f, 0x0a, 0x04, 0x54, 0x61, 0x73,
	0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x06,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0xeb, 0x01, 0x0a, 0x1b, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x74, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x17, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x15, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x35, 0x0a, 0x17, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x14, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x75, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xbc, 0x01, 0x0a, 0x25, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x4d,
	0x75, 0x6c, 0x74, 0x69, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x65, 0x71, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75,
	0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x23, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x5f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x1e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x6c, 0x6f, 0x75,
	0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x32, 0x82, 0x05, 0x0a, 0x0f, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x4e,
	0x65, 0x77, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x12, 0x2e, 0x70, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x1a, 0x18,
	0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x54, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1e, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12, 0x41,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x79, 0x41,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x1a, 0x17, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x72, 0x72, 0x61,
	0x79, 0x12, 0x2d, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x42, 0x79, 0x49, 0x64,
	0x12, 0x0e, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x1a, 0x0e, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x12, 0x4b, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x41, 0x70, 0x70, 0x4d, 0x73, 0x67, 0x12, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x70, 0x70, 0x52,
	0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x12, 0x59, 0x0a,
	0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x25, 0x2e, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x6d, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x75,
	0x6c, 0x74, 0x69, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2f,
	0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x75, 0x6c, 0x74,
	0x69, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x53, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x41, 0x70, 0x70,
	0x49, 0x64, 0x73, 0x12, 0x1a, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x1b, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x42, 0x37, 0x5a, 0x35,
	0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x61, 0x72, 0x6d,
	0x6f, 0x6e, 0x79, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x3b, 0x70, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pipeline_pipeline_proto_rawDescOnce sync.Once
	file_pipeline_pipeline_proto_rawDescData = file_pipeline_pipeline_proto_rawDesc
)

func file_pipeline_pipeline_proto_rawDescGZIP() []byte {
	file_pipeline_pipeline_proto_rawDescOnce.Do(func() {
		file_pipeline_pipeline_proto_rawDescData = protoimpl.X.CompressGZIP(file_pipeline_pipeline_proto_rawDescData)
	})
	return file_pipeline_pipeline_proto_rawDescData
}

var file_pipeline_pipeline_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_pipeline_pipeline_proto_goTypes = []interface{}{
	(*Pipeline)(nil),                              // 0: pipeline.Pipeline
	(*Task)(nil),                                  // 1: pipeline.Task
	(*PipelineArray)(nil),                         // 2: pipeline.PipelineArray
	(*PipelineResult)(nil),                        // 3: pipeline.PipelineResult
	(*PipelineCountReq)(nil),                      // 4: pipeline.PipelineCountReq
	(*PipelineAppReq)(nil),                        // 5: pipeline.PipelineAppReq
	(*PipelineCountResp)(nil),                     // 6: pipeline.PipelineCountResp
	(*PipelineAppResp)(nil),                       // 7: pipeline.PipelineAppResp
	(*GetPipelineConfigReq)(nil),                  // 8: pipeline.GetPipelineConfigReq
	(*GetPipelineConfigResp)(nil),                 // 9: pipeline.GetPipelineConfigResp
	(*UpdatePipelineTaskConfigReq)(nil),           // 10: pipeline.UpdatePipelineTaskConfigReq
	(*UpdatePipelineTaskMultiCloudConfigReq)(nil), // 11: pipeline.UpdatePipelineTaskMultiCloudConfigReq
	nil,                                 // 12: pipeline.PipelineCountResp.CountMapEntry
	(*GetPipelineConfigResp_Stage)(nil), // 13: pipeline.GetPipelineConfigResp.Stage
	(*GetPipelineConfigResp_Task)(nil),  // 14: pipeline.GetPipelineConfigResp.Task
	(*structpb.Struct)(nil),             // 15: google.protobuf.Struct
	(*emptypb.Empty)(nil),               // 16: google.protobuf.Empty
}
var file_pipeline_pipeline_proto_depIdxs = []int32{
	3,  // 0: pipeline.PipelineArray.pipelines:type_name -> pipeline.PipelineResult
	12, // 1: pipeline.PipelineCountResp.count_map:type_name -> pipeline.PipelineCountResp.CountMapEntry
	13, // 2: pipeline.GetPipelineConfigResp.stages:type_name -> pipeline.GetPipelineConfigResp.Stage
	14, // 3: pipeline.GetPipelineConfigResp.Stage.tasks:type_name -> pipeline.GetPipelineConfigResp.Task
	15, // 4: pipeline.GetPipelineConfigResp.Task.config:type_name -> google.protobuf.Struct
	0,  // 5: pipeline.PipelineService.NewPipeline:input_type -> pipeline.Pipeline
	8,  // 6: pipeline.PipelineService.GetPipelineConfig:input_type -> pipeline.GetPipelineConfigReq
	0,  // 7: pipeline.PipelineService.GetPipelineByAppId:input_type -> pipeline.Pipeline
	1,  // 8: pipeline.PipelineService.GetTaskById:input_type -> pipeline.Task
	5,  // 9: pipeline.PipelineService.UpdatePipelineAppMsg:input_type -> pipeline.PipelineAppReq
	10, // 10: pipeline.PipelineService.UpdatePipelineTaskConfig:input_type -> pipeline.UpdatePipelineTaskConfigReq
	11, // 11: pipeline.PipelineService.UpdatePipelineTaskMultiCloudConfig:input_type -> pipeline.UpdatePipelineTaskMultiCloudConfigReq
	4,  // 12: pipeline.PipelineService.GetPipelineCountByAppIds:input_type -> pipeline.PipelineCountReq
	3,  // 13: pipeline.PipelineService.NewPipeline:output_type -> pipeline.PipelineResult
	9,  // 14: pipeline.PipelineService.GetPipelineConfig:output_type -> pipeline.GetPipelineConfigResp
	2,  // 15: pipeline.PipelineService.GetPipelineByAppId:output_type -> pipeline.PipelineArray
	1,  // 16: pipeline.PipelineService.GetTaskById:output_type -> pipeline.Task
	7,  // 17: pipeline.PipelineService.UpdatePipelineAppMsg:output_type -> pipeline.PipelineAppResp
	16, // 18: pipeline.PipelineService.UpdatePipelineTaskConfig:output_type -> google.protobuf.Empty
	16, // 19: pipeline.PipelineService.UpdatePipelineTaskMultiCloudConfig:output_type -> google.protobuf.Empty
	6,  // 20: pipeline.PipelineService.GetPipelineCountByAppIds:output_type -> pipeline.PipelineCountResp
	13, // [13:21] is the sub-list for method output_type
	5,  // [5:13] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_pipeline_pipeline_proto_init() }
func file_pipeline_pipeline_proto_init() {
	if File_pipeline_pipeline_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pipeline_pipeline_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pipeline); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Task); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineArray); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineCountReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineAppReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineCountResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineAppResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPipelineConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPipelineConfigResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePipelineTaskConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePipelineTaskMultiCloudConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPipelineConfigResp_Stage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_pipeline_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPipelineConfigResp_Task); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pipeline_pipeline_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pipeline_pipeline_proto_goTypes,
		DependencyIndexes: file_pipeline_pipeline_proto_depIdxs,
		MessageInfos:      file_pipeline_pipeline_proto_msgTypes,
	}.Build()
	File_pipeline_pipeline_proto = out.File
	file_pipeline_pipeline_proto_rawDesc = nil
	file_pipeline_pipeline_proto_goTypes = nil
	file_pipeline_pipeline_proto_depIdxs = nil
}
