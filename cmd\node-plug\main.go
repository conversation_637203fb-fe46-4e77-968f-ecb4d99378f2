package main

import (
	"flag"

	"golang.ttyuyin.com/harmony/csi-driver/pkg/csi"
	"k8s.io/klog/v2"
)

var (
	endpoint = flag.String("endpoint", "unix://tmp/csi.sock", "CSI Endpoint")
	nodeID   = flag.String("nodeid", "", "Node ID")
	serType  = flag.String("sertype", "", "CSI Service Type")
)

func main() {
	flag.Parse()
	flag.Set("logtostderr", "true")

	if *nodeID == "" {
		klog.Fatalln("nodeID must be provided")
	}

	drv := csi.NewDriver(*endpoint, *nodeID, *serType)
	if err := drv.Run(); err != nil {
		klog.Fatalln(err)
	}
}
