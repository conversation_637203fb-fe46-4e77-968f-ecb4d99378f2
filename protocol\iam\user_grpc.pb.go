// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: iam/user.proto

package iam

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UserService_GetUserPreferenceBy_FullMethodName     = "/iam.UserService/GetUserPreferenceBy"
	UserService_UpdateUserPreferenceBy_FullMethodName  = "/iam.UserService/UpdateUserPreferenceBy"
	UserService_GetUserOfProject_FullMethodName        = "/iam.UserService/GetUserOfProject"
	UserService_PageUserOfProject_FullMethodName       = "/iam.UserService/PageUserOfProject"
	UserService_GetUserListByIds_FullMethodName        = "/iam.UserService/GetUserListByIds"
	UserService_GetUserById_FullMethodName             = "/iam.UserService/GetUserById"
	UserService_GetUserByGitlabId_FullMethodName       = "/iam.UserService/GetUserByGitlabId"
	UserService_GetUserByEmail_FullMethodName          = "/iam.UserService/GetUserByEmail"
	UserService_SearchUser_FullMethodName              = "/iam.UserService/SearchUser"
	UserService_GetUserByLarkUnionId_FullMethodName    = "/iam.UserService/GetUserByLarkUnionId"
	UserService_GetAllUsersByRole_FullMethodName       = "/iam.UserService/GetAllUsersByRole"
	UserService_FindUserByUniqCondition_FullMethodName = "/iam.UserService/FindUserByUniqCondition"
)

// UserServiceClient is the client API for UserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserServiceClient interface {
	// 查询用户的偏好信息
	GetUserPreferenceBy(ctx context.Context, in *UserParam, opts ...grpc.CallOption) (*UserPreferenceResponse, error)
	UpdateUserPreferenceBy(ctx context.Context, in *UserPreference, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 查询项目下的成员(全量不分页)
	GetUserOfProject(ctx context.Context, in *UserSearch, opts ...grpc.CallOption) (*UserQueryResponse, error)
	// 查询项目下成员列表(分页)
	PageUserOfProject(ctx context.Context, in *UserSearch, opts ...grpc.CallOption) (*UserQueryResponse, error)
	// 根据用户Id查询用户信息，以列表形式返回
	GetUserListByIds(ctx context.Context, in *UserIdsQuery, opts ...grpc.CallOption) (*UserQueryResponse, error)
	// 查询用户是否存在
	GetUserById(ctx context.Context, in *UserParam, opts ...grpc.CallOption) (*UserResponse, error)
	// find user by gitlab id
	GetUserByGitlabId(ctx context.Context, in *GitlabQuery, opts ...grpc.CallOption) (*User, error)
	// find user by email
	GetUserByEmail(ctx context.Context, in *UserSearch, opts ...grpc.CallOption) (*User, error)
	// search user
	SearchUser(ctx context.Context, in *UserSearchReq, opts ...grpc.CallOption) (*UserQueryResponse, error)
	GetUserByLarkUnionId(ctx context.Context, in *UserLarkUnionId, opts ...grpc.CallOption) (*UserResponse, error)
	GetAllUsersByRole(ctx context.Context, in *Role, opts ...grpc.CallOption) (*UserQueryResponse, error)
	// 根据用户唯一条件查询用户信息
	FindUserByUniqCondition(ctx context.Context, in *FindUserByUniqConditionReq, opts ...grpc.CallOption) (*UserResponse, error)
}

type userServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserServiceClient(cc grpc.ClientConnInterface) UserServiceClient {
	return &userServiceClient{cc}
}

func (c *userServiceClient) GetUserPreferenceBy(ctx context.Context, in *UserParam, opts ...grpc.CallOption) (*UserPreferenceResponse, error) {
	out := new(UserPreferenceResponse)
	err := c.cc.Invoke(ctx, UserService_GetUserPreferenceBy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdateUserPreferenceBy(ctx context.Context, in *UserPreference, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, UserService_UpdateUserPreferenceBy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetUserOfProject(ctx context.Context, in *UserSearch, opts ...grpc.CallOption) (*UserQueryResponse, error) {
	out := new(UserQueryResponse)
	err := c.cc.Invoke(ctx, UserService_GetUserOfProject_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) PageUserOfProject(ctx context.Context, in *UserSearch, opts ...grpc.CallOption) (*UserQueryResponse, error) {
	out := new(UserQueryResponse)
	err := c.cc.Invoke(ctx, UserService_PageUserOfProject_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetUserListByIds(ctx context.Context, in *UserIdsQuery, opts ...grpc.CallOption) (*UserQueryResponse, error) {
	out := new(UserQueryResponse)
	err := c.cc.Invoke(ctx, UserService_GetUserListByIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetUserById(ctx context.Context, in *UserParam, opts ...grpc.CallOption) (*UserResponse, error) {
	out := new(UserResponse)
	err := c.cc.Invoke(ctx, UserService_GetUserById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetUserByGitlabId(ctx context.Context, in *GitlabQuery, opts ...grpc.CallOption) (*User, error) {
	out := new(User)
	err := c.cc.Invoke(ctx, UserService_GetUserByGitlabId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetUserByEmail(ctx context.Context, in *UserSearch, opts ...grpc.CallOption) (*User, error) {
	out := new(User)
	err := c.cc.Invoke(ctx, UserService_GetUserByEmail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) SearchUser(ctx context.Context, in *UserSearchReq, opts ...grpc.CallOption) (*UserQueryResponse, error) {
	out := new(UserQueryResponse)
	err := c.cc.Invoke(ctx, UserService_SearchUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetUserByLarkUnionId(ctx context.Context, in *UserLarkUnionId, opts ...grpc.CallOption) (*UserResponse, error) {
	out := new(UserResponse)
	err := c.cc.Invoke(ctx, UserService_GetUserByLarkUnionId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetAllUsersByRole(ctx context.Context, in *Role, opts ...grpc.CallOption) (*UserQueryResponse, error) {
	out := new(UserQueryResponse)
	err := c.cc.Invoke(ctx, UserService_GetAllUsersByRole_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) FindUserByUniqCondition(ctx context.Context, in *FindUserByUniqConditionReq, opts ...grpc.CallOption) (*UserResponse, error) {
	out := new(UserResponse)
	err := c.cc.Invoke(ctx, UserService_FindUserByUniqCondition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserServiceServer is the server API for UserService service.
// All implementations must embed UnimplementedUserServiceServer
// for forward compatibility
type UserServiceServer interface {
	// 查询用户的偏好信息
	GetUserPreferenceBy(context.Context, *UserParam) (*UserPreferenceResponse, error)
	UpdateUserPreferenceBy(context.Context, *UserPreference) (*emptypb.Empty, error)
	// 查询项目下的成员(全量不分页)
	GetUserOfProject(context.Context, *UserSearch) (*UserQueryResponse, error)
	// 查询项目下成员列表(分页)
	PageUserOfProject(context.Context, *UserSearch) (*UserQueryResponse, error)
	// 根据用户Id查询用户信息，以列表形式返回
	GetUserListByIds(context.Context, *UserIdsQuery) (*UserQueryResponse, error)
	// 查询用户是否存在
	GetUserById(context.Context, *UserParam) (*UserResponse, error)
	// find user by gitlab id
	GetUserByGitlabId(context.Context, *GitlabQuery) (*User, error)
	// find user by email
	GetUserByEmail(context.Context, *UserSearch) (*User, error)
	// search user
	SearchUser(context.Context, *UserSearchReq) (*UserQueryResponse, error)
	GetUserByLarkUnionId(context.Context, *UserLarkUnionId) (*UserResponse, error)
	GetAllUsersByRole(context.Context, *Role) (*UserQueryResponse, error)
	// 根据用户唯一条件查询用户信息
	FindUserByUniqCondition(context.Context, *FindUserByUniqConditionReq) (*UserResponse, error)
	mustEmbedUnimplementedUserServiceServer()
}

// UnimplementedUserServiceServer must be embedded to have forward compatible implementations.
type UnimplementedUserServiceServer struct {
}

func (UnimplementedUserServiceServer) GetUserPreferenceBy(context.Context, *UserParam) (*UserPreferenceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserPreferenceBy not implemented")
}
func (UnimplementedUserServiceServer) UpdateUserPreferenceBy(context.Context, *UserPreference) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserPreferenceBy not implemented")
}
func (UnimplementedUserServiceServer) GetUserOfProject(context.Context, *UserSearch) (*UserQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserOfProject not implemented")
}
func (UnimplementedUserServiceServer) PageUserOfProject(context.Context, *UserSearch) (*UserQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PageUserOfProject not implemented")
}
func (UnimplementedUserServiceServer) GetUserListByIds(context.Context, *UserIdsQuery) (*UserQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserListByIds not implemented")
}
func (UnimplementedUserServiceServer) GetUserById(context.Context, *UserParam) (*UserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserById not implemented")
}
func (UnimplementedUserServiceServer) GetUserByGitlabId(context.Context, *GitlabQuery) (*User, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserByGitlabId not implemented")
}
func (UnimplementedUserServiceServer) GetUserByEmail(context.Context, *UserSearch) (*User, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserByEmail not implemented")
}
func (UnimplementedUserServiceServer) SearchUser(context.Context, *UserSearchReq) (*UserQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchUser not implemented")
}
func (UnimplementedUserServiceServer) GetUserByLarkUnionId(context.Context, *UserLarkUnionId) (*UserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserByLarkUnionId not implemented")
}
func (UnimplementedUserServiceServer) GetAllUsersByRole(context.Context, *Role) (*UserQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllUsersByRole not implemented")
}
func (UnimplementedUserServiceServer) FindUserByUniqCondition(context.Context, *FindUserByUniqConditionReq) (*UserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindUserByUniqCondition not implemented")
}
func (UnimplementedUserServiceServer) mustEmbedUnimplementedUserServiceServer() {}

// UnsafeUserServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserServiceServer will
// result in compilation errors.
type UnsafeUserServiceServer interface {
	mustEmbedUnimplementedUserServiceServer()
}

func RegisterUserServiceServer(s grpc.ServiceRegistrar, srv UserServiceServer) {
	s.RegisterService(&UserService_ServiceDesc, srv)
}

func _UserService_GetUserPreferenceBy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetUserPreferenceBy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetUserPreferenceBy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetUserPreferenceBy(ctx, req.(*UserParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdateUserPreferenceBy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserPreference)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdateUserPreferenceBy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UpdateUserPreferenceBy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdateUserPreferenceBy(ctx, req.(*UserPreference))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetUserOfProject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserSearch)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetUserOfProject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetUserOfProject_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetUserOfProject(ctx, req.(*UserSearch))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_PageUserOfProject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserSearch)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).PageUserOfProject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_PageUserOfProject_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).PageUserOfProject(ctx, req.(*UserSearch))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetUserListByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdsQuery)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetUserListByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetUserListByIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetUserListByIds(ctx, req.(*UserIdsQuery))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetUserById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetUserById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetUserById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetUserById(ctx, req.(*UserParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetUserByGitlabId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GitlabQuery)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetUserByGitlabId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetUserByGitlabId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetUserByGitlabId(ctx, req.(*GitlabQuery))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetUserByEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserSearch)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetUserByEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetUserByEmail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetUserByEmail(ctx, req.(*UserSearch))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_SearchUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserSearchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).SearchUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_SearchUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).SearchUser(ctx, req.(*UserSearchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetUserByLarkUnionId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserLarkUnionId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetUserByLarkUnionId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetUserByLarkUnionId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetUserByLarkUnionId(ctx, req.(*UserLarkUnionId))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetAllUsersByRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Role)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetAllUsersByRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetAllUsersByRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetAllUsersByRole(ctx, req.(*Role))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_FindUserByUniqCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindUserByUniqConditionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).FindUserByUniqCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_FindUserByUniqCondition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).FindUserByUniqCondition(ctx, req.(*FindUserByUniqConditionReq))
	}
	return interceptor(ctx, in, info, handler)
}

// UserService_ServiceDesc is the grpc.ServiceDesc for UserService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "iam.UserService",
	HandlerType: (*UserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserPreferenceBy",
			Handler:    _UserService_GetUserPreferenceBy_Handler,
		},
		{
			MethodName: "UpdateUserPreferenceBy",
			Handler:    _UserService_UpdateUserPreferenceBy_Handler,
		},
		{
			MethodName: "GetUserOfProject",
			Handler:    _UserService_GetUserOfProject_Handler,
		},
		{
			MethodName: "PageUserOfProject",
			Handler:    _UserService_PageUserOfProject_Handler,
		},
		{
			MethodName: "GetUserListByIds",
			Handler:    _UserService_GetUserListByIds_Handler,
		},
		{
			MethodName: "GetUserById",
			Handler:    _UserService_GetUserById_Handler,
		},
		{
			MethodName: "GetUserByGitlabId",
			Handler:    _UserService_GetUserByGitlabId_Handler,
		},
		{
			MethodName: "GetUserByEmail",
			Handler:    _UserService_GetUserByEmail_Handler,
		},
		{
			MethodName: "SearchUser",
			Handler:    _UserService_SearchUser_Handler,
		},
		{
			MethodName: "GetUserByLarkUnionId",
			Handler:    _UserService_GetUserByLarkUnionId_Handler,
		},
		{
			MethodName: "GetAllUsersByRole",
			Handler:    _UserService_GetAllUsersByRole_Handler,
		},
		{
			MethodName: "FindUserByUniqCondition",
			Handler:    _UserService_FindUserByUniqCondition_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "iam/user.proto",
}
