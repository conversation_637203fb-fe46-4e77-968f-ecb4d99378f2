package main

import (
	"context"
	"path"

	"golang.ttyuyin.com/harmony/csi-driver/pkg/task"
	"golang.ttyuyin.com/harmony/pkg/log"
)

func debug(cm *task.CronManager) {
	h := &debugHandler{}

	_, _ = cm.RegisterCronSpec(
		"*/15 * * * * *",
		h,
	)
	_, _ = cm.RegisterCronSpec(
		"0 */15 * * * *",
		h,
	)
}

type debugHandler struct{}

func (h *debugHandler) RootPath() string {
	return path.Join(cfg.LowerDirRootPath, cfg.LowerDirVersion, task.GetCurrentDateString())
}

func (h *debugHandler) Handle(ctx context.Context, rootPath, url, branch string) {
	log.Infof("mock task, rootPath: %s, url: %s, branch: %s", rootPath, url, branch)
}

func (h *debugHandler) Repos() []task.Params {
	return []task.Params{{RepoURL: "https://gitlab.ttyuyin.com/harmony/pkg.git", Branch: "master"}}
}
