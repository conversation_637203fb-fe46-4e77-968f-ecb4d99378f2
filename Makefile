#.EXPORT_ALL_VARIABLES:
#GOFLAGS="-tags=linux,darwin"

.PHONY: gen-proto

gen-proto:
	@buf generate https://gitlab.ttyuyin.com/harmony/protocols.git

.PHONY: test
test:
	./ci/coverage/coverage.sh


.PHONY: build-csi-driver
build-csi-driver:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o bin/controller cmd/csi-driver/main.go

.PHONY: build-repo-sync
build-repo-sync:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o bin/reposync cmd/reposync/*.go


.PHONY: compile-test-csi-driver
compile-test-csi-driver: gen-proto test build-csi-driver


.PHONY: compile-test-repo-sync
compile-test-repo-sync: gen-proto test build-repo-sync