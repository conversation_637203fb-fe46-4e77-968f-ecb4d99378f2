// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        (unknown)
// source: pipeline/change_set.proto

package pipeline

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetChgSetResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name       string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	TemplateId int64  `protobuf:"varint,3,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// @gotags: json:"deployTime"
	DeployTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=deploy_time,json=deployTime,proto3" json:"deploy_time,omitempty"`
	Duration   *durationpb.Duration   `protobuf:"bytes,5,opt,name=duration,proto3" json:"duration,omitempty"`
	Stages     []*GetChgSetResp_Stage `protobuf:"bytes,6,rep,name=stages,proto3" json:"stages,omitempty"`
	Status     string                 `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	// @gotags: json:"projectId"
	ProjectId int64 `protobuf:"varint,8,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
}

func (x *GetChgSetResp) Reset() {
	*x = GetChgSetResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_change_set_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChgSetResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChgSetResp) ProtoMessage() {}

func (x *GetChgSetResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_change_set_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChgSetResp.ProtoReflect.Descriptor instead.
func (*GetChgSetResp) Descriptor() ([]byte, []int) {
	return file_pipeline_change_set_proto_rawDescGZIP(), []int{0}
}

func (x *GetChgSetResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetChgSetResp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetChgSetResp) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *GetChgSetResp) GetDeployTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeployTime
	}
	return nil
}

func (x *GetChgSetResp) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *GetChgSetResp) GetStages() []*GetChgSetResp_Stage {
	if x != nil {
		return x.Stages
	}
	return nil
}

func (x *GetChgSetResp) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetChgSetResp) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

type GetChgSetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: uri:"id" binding:"required"
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetChgSetReq) Reset() {
	*x = GetChgSetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_change_set_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChgSetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChgSetReq) ProtoMessage() {}

func (x *GetChgSetReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_change_set_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChgSetReq.ProtoReflect.Descriptor instead.
func (*GetChgSetReq) Descriptor() ([]byte, []int) {
	return file_pipeline_change_set_proto_rawDescGZIP(), []int{1}
}

func (x *GetChgSetReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetChgSetTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: uri:"id" binding:"required" json:"taskId"
	Tid int64 `protobuf:"varint,1,opt,name=tid,proto3" json:"tid,omitempty"`
	// @gotags: json:"stageId"
	StageId int64  `protobuf:"varint,2,opt,name=stage_id,json=stageId,proto3" json:"stage_id,omitempty"`
	Type    string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	// @gotags: json:"changeSetId"
	ChangeSetId int64 `protobuf:"varint,4,opt,name=change_set_id,json=changeSetId,proto3" json:"change_set_id,omitempty"`
}

func (x *GetChgSetTaskReq) Reset() {
	*x = GetChgSetTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_change_set_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChgSetTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChgSetTaskReq) ProtoMessage() {}

func (x *GetChgSetTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_change_set_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChgSetTaskReq.ProtoReflect.Descriptor instead.
func (*GetChgSetTaskReq) Descriptor() ([]byte, []int) {
	return file_pipeline_change_set_proto_rawDescGZIP(), []int{2}
}

func (x *GetChgSetTaskReq) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *GetChgSetTaskReq) GetStageId() int64 {
	if x != nil {
		return x.StageId
	}
	return 0
}

func (x *GetChgSetTaskReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetChgSetTaskReq) GetChangeSetId() int64 {
	if x != nil {
		return x.ChangeSetId
	}
	return 0
}

type PipelineRunResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// @gotags: json:"startedTime"
	StartedTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=started_time,json=startedTime,proto3" json:"started_time,omitempty"`
	// @gotags: json:"completedTime"
	CompletedTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=completed_time,json=completedTime,proto3" json:"completed_time,omitempty"`
	// @gotags: json:"elapsedTime"
	ElapsedTime *durationpb.Duration     `protobuf:"bytes,5,opt,name=elapsed_time,json=elapsedTime,proto3" json:"elapsed_time,omitempty"`
	Stages      []*PipelineRunResp_Stage `protobuf:"bytes,6,rep,name=stages,proto3" json:"stages,omitempty"`
	Status      string                   `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	Branch      string                   `protobuf:"bytes,8,opt,name=branch,proto3" json:"branch,omitempty"`
	// @gotags: json:"buildNumber"
	BuildNumber int64 `protobuf:"varint,9,opt,name=build_number,json=buildNumber,proto3" json:"build_number,omitempty"`
	// @gotags: json:"pipelineId"
	PipelineId int64 `protobuf:"varint,10,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id,omitempty"`
	// @gotags: json:"appId"
	AppId int64 `protobuf:"varint,11,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// @gotags: json:"appName"
	AppName string `protobuf:"bytes,12,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	// @gotags: json:"pipelineName"
	PipelineName string `protobuf:"bytes,13,opt,name=pipeline_name,json=pipelineName,proto3" json:"pipeline_name,omitempty"`
}

func (x *PipelineRunResp) Reset() {
	*x = PipelineRunResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_change_set_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineRunResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineRunResp) ProtoMessage() {}

func (x *PipelineRunResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_change_set_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineRunResp.ProtoReflect.Descriptor instead.
func (*PipelineRunResp) Descriptor() ([]byte, []int) {
	return file_pipeline_change_set_proto_rawDescGZIP(), []int{3}
}

func (x *PipelineRunResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PipelineRunResp) GetStartedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedTime
	}
	return nil
}

func (x *PipelineRunResp) GetCompletedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedTime
	}
	return nil
}

func (x *PipelineRunResp) GetElapsedTime() *durationpb.Duration {
	if x != nil {
		return x.ElapsedTime
	}
	return nil
}

func (x *PipelineRunResp) GetStages() []*PipelineRunResp_Stage {
	if x != nil {
		return x.Stages
	}
	return nil
}

func (x *PipelineRunResp) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *PipelineRunResp) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *PipelineRunResp) GetBuildNumber() int64 {
	if x != nil {
		return x.BuildNumber
	}
	return 0
}

func (x *PipelineRunResp) GetPipelineId() int64 {
	if x != nil {
		return x.PipelineId
	}
	return 0
}

func (x *PipelineRunResp) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *PipelineRunResp) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *PipelineRunResp) GetPipelineName() string {
	if x != nil {
		return x.PipelineName
	}
	return ""
}

type GetChgSetTaskResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// @gotags: json:"taskId"
	TaskId   int64                `protobuf:"varint,3,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Duration *durationpb.Duration `protobuf:"bytes,4,opt,name=duration,proto3" json:"duration,omitempty"`
	Status   string               `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	Type     string               `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	Config   []byte               `protobuf:"bytes,7,opt,name=config,proto3" json:"config,omitempty"`
	// @gotags: json:"changeSetId"
	ChangeSetId int64 `protobuf:"varint,8,opt,name=change_set_id,json=changeSetId,proto3" json:"change_set_id,omitempty"`
	// @gotags: json:"changeSetStageId"
	ChangeSetStageId int64 `protobuf:"varint,9,opt,name=change_set_stage_id,json=changeSetStageId,proto3" json:"change_set_stage_id,omitempty"`
}

func (x *GetChgSetTaskResp) Reset() {
	*x = GetChgSetTaskResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_change_set_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChgSetTaskResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChgSetTaskResp) ProtoMessage() {}

func (x *GetChgSetTaskResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_change_set_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChgSetTaskResp.ProtoReflect.Descriptor instead.
func (*GetChgSetTaskResp) Descriptor() ([]byte, []int) {
	return file_pipeline_change_set_proto_rawDescGZIP(), []int{4}
}

func (x *GetChgSetTaskResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetChgSetTaskResp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetChgSetTaskResp) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *GetChgSetTaskResp) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *GetChgSetTaskResp) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetChgSetTaskResp) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetChgSetTaskResp) GetConfig() []byte {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *GetChgSetTaskResp) GetChangeSetId() int64 {
	if x != nil {
		return x.ChangeSetId
	}
	return 0
}

func (x *GetChgSetTaskResp) GetChangeSetStageId() int64 {
	if x != nil {
		return x.ChangeSetStageId
	}
	return 0
}

type ChgSetRelatedPipelineRunsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"pipelineRuns"
	PipelineRuns []*PipelineRunResp `protobuf:"bytes,1,rep,name=pipeline_runs,json=pipelineRuns,proto3" json:"pipeline_runs,omitempty"`
}

func (x *ChgSetRelatedPipelineRunsResp) Reset() {
	*x = ChgSetRelatedPipelineRunsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_change_set_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChgSetRelatedPipelineRunsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChgSetRelatedPipelineRunsResp) ProtoMessage() {}

func (x *ChgSetRelatedPipelineRunsResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_change_set_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChgSetRelatedPipelineRunsResp.ProtoReflect.Descriptor instead.
func (*ChgSetRelatedPipelineRunsResp) Descriptor() ([]byte, []int) {
	return file_pipeline_change_set_proto_rawDescGZIP(), []int{5}
}

func (x *ChgSetRelatedPipelineRunsResp) GetPipelineRuns() []*PipelineRunResp {
	if x != nil {
		return x.PipelineRuns
	}
	return nil
}

type ChgSetTaskStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ChgSetTaskStatus) Reset() {
	*x = ChgSetTaskStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_change_set_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChgSetTaskStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChgSetTaskStatus) ProtoMessage() {}

func (x *ChgSetTaskStatus) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_change_set_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChgSetTaskStatus.ProtoReflect.Descriptor instead.
func (*ChgSetTaskStatus) Descriptor() ([]byte, []int) {
	return file_pipeline_change_set_proto_rawDescGZIP(), []int{6}
}

func (x *ChgSetTaskStatus) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChgSetTaskStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type ChgSetUpdateStatusResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *ChgSetUpdateStatusResp) Reset() {
	*x = ChgSetUpdateStatusResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_change_set_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChgSetUpdateStatusResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChgSetUpdateStatusResp) ProtoMessage() {}

func (x *ChgSetUpdateStatusResp) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_change_set_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChgSetUpdateStatusResp.ProtoReflect.Descriptor instead.
func (*ChgSetUpdateStatusResp) Descriptor() ([]byte, []int) {
	return file_pipeline_change_set_proto_rawDescGZIP(), []int{7}
}

func (x *ChgSetUpdateStatusResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ChgSetUpdateStatusResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type UpdateChgSetTaskConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"taskId"
	TaskId int64  `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Config []byte `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *UpdateChgSetTaskConfigReq) Reset() {
	*x = UpdateChgSetTaskConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_change_set_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateChgSetTaskConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateChgSetTaskConfigReq) ProtoMessage() {}

func (x *UpdateChgSetTaskConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_change_set_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateChgSetTaskConfigReq.ProtoReflect.Descriptor instead.
func (*UpdateChgSetTaskConfigReq) Descriptor() ([]byte, []int) {
	return file_pipeline_change_set_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateChgSetTaskConfigReq) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *UpdateChgSetTaskConfigReq) GetConfig() []byte {
	if x != nil {
		return x.Config
	}
	return nil
}

type GetChgSetResp_Task struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// @gotags: json:"taskId"
	TaskId   int64                `protobuf:"varint,3,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Duration *durationpb.Duration `protobuf:"bytes,4,opt,name=duration,proto3" json:"duration,omitempty"`
	Status   string               `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	Type     string               `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	Config   []byte               `protobuf:"bytes,7,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *GetChgSetResp_Task) Reset() {
	*x = GetChgSetResp_Task{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_change_set_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChgSetResp_Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChgSetResp_Task) ProtoMessage() {}

func (x *GetChgSetResp_Task) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_change_set_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChgSetResp_Task.ProtoReflect.Descriptor instead.
func (*GetChgSetResp_Task) Descriptor() ([]byte, []int) {
	return file_pipeline_change_set_proto_rawDescGZIP(), []int{0, 0}
}

func (x *GetChgSetResp_Task) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetChgSetResp_Task) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetChgSetResp_Task) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *GetChgSetResp_Task) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *GetChgSetResp_Task) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetChgSetResp_Task) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetChgSetResp_Task) GetConfig() []byte {
	if x != nil {
		return x.Config
	}
	return nil
}

type GetChgSetResp_Stage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// @gotags: json:"stageId"
	StageId  int64                 `protobuf:"varint,3,opt,name=stage_id,json=stageId,proto3" json:"stage_id,omitempty"`
	Duration *durationpb.Duration  `protobuf:"bytes,4,opt,name=duration,proto3" json:"duration,omitempty"`
	Tasks    []*GetChgSetResp_Task `protobuf:"bytes,5,rep,name=tasks,proto3" json:"tasks,omitempty"`
	Status   string                `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *GetChgSetResp_Stage) Reset() {
	*x = GetChgSetResp_Stage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_change_set_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChgSetResp_Stage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChgSetResp_Stage) ProtoMessage() {}

func (x *GetChgSetResp_Stage) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_change_set_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChgSetResp_Stage.ProtoReflect.Descriptor instead.
func (*GetChgSetResp_Stage) Descriptor() ([]byte, []int) {
	return file_pipeline_change_set_proto_rawDescGZIP(), []int{0, 1}
}

func (x *GetChgSetResp_Stage) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetChgSetResp_Stage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetChgSetResp_Stage) GetStageId() int64 {
	if x != nil {
		return x.StageId
	}
	return 0
}

func (x *GetChgSetResp_Stage) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *GetChgSetResp_Stage) GetTasks() []*GetChgSetResp_Task {
	if x != nil {
		return x.Tasks
	}
	return nil
}

func (x *GetChgSetResp_Stage) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type PipelineRunResp_Task struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// @gotags: json:"startedTime"
	StartedTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=started_time,json=startedTime,proto3" json:"started_time,omitempty"`
	// @gotags: json:"completedTime"
	CompletedTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=completed_time,json=completedTime,proto3" json:"completed_time,omitempty"`
	// @gotags: json:"elapsedTime"
	ElapsedTime *durationpb.Duration `protobuf:"bytes,5,opt,name=elapsed_time,json=elapsedTime,proto3" json:"elapsed_time,omitempty"`
	Status      string               `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
	Type        string               `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
	Config      []byte               `protobuf:"bytes,8,opt,name=config,proto3" json:"config,omitempty"`
	// @gotags: json:"taskId"
	TaskId int64  `protobuf:"varint,9,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Result []byte `protobuf:"bytes,10,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *PipelineRunResp_Task) Reset() {
	*x = PipelineRunResp_Task{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_change_set_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineRunResp_Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineRunResp_Task) ProtoMessage() {}

func (x *PipelineRunResp_Task) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_change_set_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineRunResp_Task.ProtoReflect.Descriptor instead.
func (*PipelineRunResp_Task) Descriptor() ([]byte, []int) {
	return file_pipeline_change_set_proto_rawDescGZIP(), []int{3, 0}
}

func (x *PipelineRunResp_Task) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PipelineRunResp_Task) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PipelineRunResp_Task) GetStartedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedTime
	}
	return nil
}

func (x *PipelineRunResp_Task) GetCompletedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedTime
	}
	return nil
}

func (x *PipelineRunResp_Task) GetElapsedTime() *durationpb.Duration {
	if x != nil {
		return x.ElapsedTime
	}
	return nil
}

func (x *PipelineRunResp_Task) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *PipelineRunResp_Task) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *PipelineRunResp_Task) GetConfig() []byte {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *PipelineRunResp_Task) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *PipelineRunResp_Task) GetResult() []byte {
	if x != nil {
		return x.Result
	}
	return nil
}

type PipelineRunResp_Stage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// @gotags: json:"startedTime"
	StartedTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=started_time,json=startedTime,proto3" json:"started_time,omitempty"`
	// @gotags: json:"completedTime"
	CompletedTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=completed_time,json=completedTime,proto3" json:"completed_time,omitempty"`
	// @gotags: json:"elapsedTime"
	ElapsedTime *durationpb.Duration    `protobuf:"bytes,5,opt,name=elapsed_time,json=elapsedTime,proto3" json:"elapsed_time,omitempty"`
	Tasks       []*PipelineRunResp_Task `protobuf:"bytes,6,rep,name=tasks,proto3" json:"tasks,omitempty"`
	Status      string                  `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	// @gotags: json:"stageId"
	StageId int64 `protobuf:"varint,8,opt,name=stage_id,json=stageId,proto3" json:"stage_id,omitempty"`
}

func (x *PipelineRunResp_Stage) Reset() {
	*x = PipelineRunResp_Stage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pipeline_change_set_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineRunResp_Stage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineRunResp_Stage) ProtoMessage() {}

func (x *PipelineRunResp_Stage) ProtoReflect() protoreflect.Message {
	mi := &file_pipeline_change_set_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineRunResp_Stage.ProtoReflect.Descriptor instead.
func (*PipelineRunResp_Stage) Descriptor() ([]byte, []int) {
	return file_pipeline_change_set_proto_rawDescGZIP(), []int{3, 1}
}

func (x *PipelineRunResp_Stage) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PipelineRunResp_Stage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PipelineRunResp_Stage) GetStartedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedTime
	}
	return nil
}

func (x *PipelineRunResp_Stage) GetCompletedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedTime
	}
	return nil
}

func (x *PipelineRunResp_Stage) GetElapsedTime() *durationpb.Duration {
	if x != nil {
		return x.ElapsedTime
	}
	return nil
}

func (x *PipelineRunResp_Stage) GetTasks() []*PipelineRunResp_Task {
	if x != nil {
		return x.Tasks
	}
	return nil
}

func (x *PipelineRunResp_Stage) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *PipelineRunResp_Stage) GetStageId() int64 {
	if x != nil {
		return x.StageId
	}
	return 0
}

var File_pipeline_change_set_proto protoreflect.FileDescriptor

var file_pipeline_change_set_proto_rawDesc = []byte{
	0x0a, 0x19, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x5f, 0x73, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xc3, 0x05, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x43, 0x68, 0x67, 0x53, 0x65,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x64, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x64, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x67, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x67,
	0x53, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x1a, 0xbe, 0x01, 0x0a,
	0x04, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x12, 0x35, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0xc9, 0x01,
	0x0a, 0x05, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a,
	0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x67, 0x53, 0x65,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b,
	0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x1e, 0x0a, 0x0c, 0x47, 0x65, 0x74,
	0x43, 0x68, 0x67, 0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x77, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x43, 0x68, 0x67, 0x53, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a,
	0x03, 0x74, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x74, 0x69, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x22,
	0x0a, 0x0d, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x65, 0x74,
	0x49, 0x64, 0x22, 0x9e, 0x09, 0x0a, 0x0f, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52,
	0x75, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x65, 0x6c, 0x61, 0x70,
	0x73, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x6c, 0x61, 0x70, 0x73,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x67, 0x65, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x67, 0x65, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63,
	0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12,
	0x21, 0x0a, 0x0c, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0xdf, 0x02, 0x0a, 0x04, 0x54,
	0x61, 0x73, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x65, 0x6c, 0x61,
	0x70, 0x73, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x6c, 0x61, 0x70,
	0x73, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61,
	0x73, 0x6b, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x1a, 0xd4, 0x02, 0x0a,
	0x05, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x0c,
	0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65,
	0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x05, 0x74, 0x61,
	0x73, 0x6b, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x49, 0x64, 0x22, 0x9e, 0x02, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x68, 0x67, 0x53, 0x65,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x53, 0x65, 0x74, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x13, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f,
	0x73, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x49, 0x64, 0x22, 0x5f, 0x0a, 0x1d, 0x43, 0x68, 0x67, 0x53, 0x65, 0x74, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x65, 0x64, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3e, 0x0a, 0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x72, 0x75, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x52, 0x75, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x52, 0x0c, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x52, 0x75, 0x6e, 0x73, 0x22, 0x3a, 0x0a, 0x10, 0x43, 0x68, 0x67, 0x53, 0x65, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x3e, 0x0a, 0x16, 0x43, 0x68, 0x67, 0x53, 0x65, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x22, 0x4c, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x67, 0x53, 0x65,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x12, 0x17,
	0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x32,
	0x82, 0x04, 0x0a, 0x10, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x65, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x3c, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x43, 0x68, 0x67, 0x53, 0x65,
	0x74, 0x12, 0x16, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x68, 0x67, 0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x67, 0x53, 0x65, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x48, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x68, 0x67, 0x53, 0x65, 0x74, 0x42,
	0x79, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1a, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x67, 0x53, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x68, 0x67, 0x53, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x5f, 0x0a, 0x1c,
	0x47, 0x65, 0x74, 0x43, 0x68, 0x67, 0x53, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x73, 0x12, 0x16, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x67, 0x53, 0x65,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x43, 0x68, 0x67, 0x53, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x50, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4a, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x43, 0x68, 0x67, 0x53, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x42, 0x79,
	0x12, 0x1a, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x68, 0x67, 0x53, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x67, 0x53, 0x65,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x12, 0x62, 0x0a, 0x22, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x68, 0x67, 0x53, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1a, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x68, 0x67, 0x53, 0x65,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0x20, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x43, 0x68, 0x67, 0x53, 0x65, 0x74, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x55, 0x0a,
	0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x67, 0x53, 0x65, 0x74, 0x54, 0x61, 0x73,
	0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x23, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x67, 0x53, 0x65, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x42, 0x37, 0x5a, 0x35, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x74,
	0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x61, 0x72, 0x6d, 0x6f, 0x6e, 0x79, 0x2f, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x3b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pipeline_change_set_proto_rawDescOnce sync.Once
	file_pipeline_change_set_proto_rawDescData = file_pipeline_change_set_proto_rawDesc
)

func file_pipeline_change_set_proto_rawDescGZIP() []byte {
	file_pipeline_change_set_proto_rawDescOnce.Do(func() {
		file_pipeline_change_set_proto_rawDescData = protoimpl.X.CompressGZIP(file_pipeline_change_set_proto_rawDescData)
	})
	return file_pipeline_change_set_proto_rawDescData
}

var file_pipeline_change_set_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_pipeline_change_set_proto_goTypes = []interface{}{
	(*GetChgSetResp)(nil),                 // 0: pipeline.GetChgSetResp
	(*GetChgSetReq)(nil),                  // 1: pipeline.GetChgSetReq
	(*GetChgSetTaskReq)(nil),              // 2: pipeline.GetChgSetTaskReq
	(*PipelineRunResp)(nil),               // 3: pipeline.PipelineRunResp
	(*GetChgSetTaskResp)(nil),             // 4: pipeline.GetChgSetTaskResp
	(*ChgSetRelatedPipelineRunsResp)(nil), // 5: pipeline.ChgSetRelatedPipelineRunsResp
	(*ChgSetTaskStatus)(nil),              // 6: pipeline.ChgSetTaskStatus
	(*ChgSetUpdateStatusResp)(nil),        // 7: pipeline.ChgSetUpdateStatusResp
	(*UpdateChgSetTaskConfigReq)(nil),     // 8: pipeline.UpdateChgSetTaskConfigReq
	(*GetChgSetResp_Task)(nil),            // 9: pipeline.GetChgSetResp.Task
	(*GetChgSetResp_Stage)(nil),           // 10: pipeline.GetChgSetResp.Stage
	(*PipelineRunResp_Task)(nil),          // 11: pipeline.PipelineRunResp.Task
	(*PipelineRunResp_Stage)(nil),         // 12: pipeline.PipelineRunResp.Stage
	(*timestamppb.Timestamp)(nil),         // 13: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),           // 14: google.protobuf.Duration
	(*emptypb.Empty)(nil),                 // 15: google.protobuf.Empty
}
var file_pipeline_change_set_proto_depIdxs = []int32{
	13, // 0: pipeline.GetChgSetResp.deploy_time:type_name -> google.protobuf.Timestamp
	14, // 1: pipeline.GetChgSetResp.duration:type_name -> google.protobuf.Duration
	10, // 2: pipeline.GetChgSetResp.stages:type_name -> pipeline.GetChgSetResp.Stage
	13, // 3: pipeline.PipelineRunResp.started_time:type_name -> google.protobuf.Timestamp
	13, // 4: pipeline.PipelineRunResp.completed_time:type_name -> google.protobuf.Timestamp
	14, // 5: pipeline.PipelineRunResp.elapsed_time:type_name -> google.protobuf.Duration
	12, // 6: pipeline.PipelineRunResp.stages:type_name -> pipeline.PipelineRunResp.Stage
	14, // 7: pipeline.GetChgSetTaskResp.duration:type_name -> google.protobuf.Duration
	3,  // 8: pipeline.ChgSetRelatedPipelineRunsResp.pipeline_runs:type_name -> pipeline.PipelineRunResp
	14, // 9: pipeline.GetChgSetResp.Task.duration:type_name -> google.protobuf.Duration
	14, // 10: pipeline.GetChgSetResp.Stage.duration:type_name -> google.protobuf.Duration
	9,  // 11: pipeline.GetChgSetResp.Stage.tasks:type_name -> pipeline.GetChgSetResp.Task
	13, // 12: pipeline.PipelineRunResp.Task.started_time:type_name -> google.protobuf.Timestamp
	13, // 13: pipeline.PipelineRunResp.Task.completed_time:type_name -> google.protobuf.Timestamp
	14, // 14: pipeline.PipelineRunResp.Task.elapsed_time:type_name -> google.protobuf.Duration
	13, // 15: pipeline.PipelineRunResp.Stage.started_time:type_name -> google.protobuf.Timestamp
	13, // 16: pipeline.PipelineRunResp.Stage.completed_time:type_name -> google.protobuf.Timestamp
	14, // 17: pipeline.PipelineRunResp.Stage.elapsed_time:type_name -> google.protobuf.Duration
	11, // 18: pipeline.PipelineRunResp.Stage.tasks:type_name -> pipeline.PipelineRunResp.Task
	1,  // 19: pipeline.ChangeSetService.GetChgSet:input_type -> pipeline.GetChgSetReq
	2,  // 20: pipeline.ChangeSetService.GetChgSetByTaskId:input_type -> pipeline.GetChgSetTaskReq
	1,  // 21: pipeline.ChangeSetService.GetChgSetRelatedPipelineRuns:input_type -> pipeline.GetChgSetReq
	2,  // 22: pipeline.ChangeSetService.GetChgSetTaskBy:input_type -> pipeline.GetChgSetTaskReq
	6,  // 23: pipeline.ChangeSetService.UpdateChgSetRelatedPipelinesStatus:input_type -> pipeline.ChgSetTaskStatus
	8,  // 24: pipeline.ChangeSetService.UpdateChgSetTaskConfig:input_type -> pipeline.UpdateChgSetTaskConfigReq
	0,  // 25: pipeline.ChangeSetService.GetChgSet:output_type -> pipeline.GetChgSetResp
	0,  // 26: pipeline.ChangeSetService.GetChgSetByTaskId:output_type -> pipeline.GetChgSetResp
	5,  // 27: pipeline.ChangeSetService.GetChgSetRelatedPipelineRuns:output_type -> pipeline.ChgSetRelatedPipelineRunsResp
	4,  // 28: pipeline.ChangeSetService.GetChgSetTaskBy:output_type -> pipeline.GetChgSetTaskResp
	7,  // 29: pipeline.ChangeSetService.UpdateChgSetRelatedPipelinesStatus:output_type -> pipeline.ChgSetUpdateStatusResp
	15, // 30: pipeline.ChangeSetService.UpdateChgSetTaskConfig:output_type -> google.protobuf.Empty
	25, // [25:31] is the sub-list for method output_type
	19, // [19:25] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_pipeline_change_set_proto_init() }
func file_pipeline_change_set_proto_init() {
	if File_pipeline_change_set_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pipeline_change_set_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChgSetResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_change_set_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChgSetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_change_set_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChgSetTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_change_set_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineRunResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_change_set_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChgSetTaskResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_change_set_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChgSetRelatedPipelineRunsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_change_set_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChgSetTaskStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_change_set_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChgSetUpdateStatusResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_change_set_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateChgSetTaskConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_change_set_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChgSetResp_Task); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_change_set_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChgSetResp_Stage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_change_set_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineRunResp_Task); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pipeline_change_set_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineRunResp_Stage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pipeline_change_set_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pipeline_change_set_proto_goTypes,
		DependencyIndexes: file_pipeline_change_set_proto_depIdxs,
		MessageInfos:      file_pipeline_change_set_proto_msgTypes,
	}.Build()
	File_pipeline_change_set_proto = out.File
	file_pipeline_change_set_proto_rawDesc = nil
	file_pipeline_change_set_proto_goTypes = nil
	file_pipeline_change_set_proto_depIdxs = nil
}
