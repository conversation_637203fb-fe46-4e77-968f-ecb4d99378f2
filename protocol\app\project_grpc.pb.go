// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: app/project.proto

package app

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ProjectService_NewProject_FullMethodName       = "/app.ProjectService/NewProject"
	ProjectService_DeleteProject_FullMethodName    = "/app.ProjectService/DeleteProject"
	ProjectService_GetProjectList_FullMethodName   = "/app.ProjectService/GetProjectList"
	ProjectService_GetProjectById_FullMethodName   = "/app.ProjectService/GetProjectById"
	ProjectService_GetProjectByName_FullMethodName = "/app.ProjectService/GetProjectByName"
)

// ProjectServiceClient is the client API for ProjectService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ProjectServiceClient interface {
	NewProject(ctx context.Context, in *Project, opts ...grpc.CallOption) (*ProjectResult, error)
	DeleteProject(ctx context.Context, in *Project, opts ...grpc.CallOption) (*ProjectResult, error)
	GetProjectList(ctx context.Context, in *ProjectListParam, opts ...grpc.CallOption) (*ProjectList, error)
	GetProjectById(ctx context.Context, in *ProjectParam, opts ...grpc.CallOption) (*Project, error)
	GetProjectByName(ctx context.Context, in *ProjectParam, opts ...grpc.CallOption) (*Project, error)
}

type projectServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewProjectServiceClient(cc grpc.ClientConnInterface) ProjectServiceClient {
	return &projectServiceClient{cc}
}

func (c *projectServiceClient) NewProject(ctx context.Context, in *Project, opts ...grpc.CallOption) (*ProjectResult, error) {
	out := new(ProjectResult)
	err := c.cc.Invoke(ctx, ProjectService_NewProject_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectServiceClient) DeleteProject(ctx context.Context, in *Project, opts ...grpc.CallOption) (*ProjectResult, error) {
	out := new(ProjectResult)
	err := c.cc.Invoke(ctx, ProjectService_DeleteProject_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectServiceClient) GetProjectList(ctx context.Context, in *ProjectListParam, opts ...grpc.CallOption) (*ProjectList, error) {
	out := new(ProjectList)
	err := c.cc.Invoke(ctx, ProjectService_GetProjectList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectServiceClient) GetProjectById(ctx context.Context, in *ProjectParam, opts ...grpc.CallOption) (*Project, error) {
	out := new(Project)
	err := c.cc.Invoke(ctx, ProjectService_GetProjectById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectServiceClient) GetProjectByName(ctx context.Context, in *ProjectParam, opts ...grpc.CallOption) (*Project, error) {
	out := new(Project)
	err := c.cc.Invoke(ctx, ProjectService_GetProjectByName_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ProjectServiceServer is the server API for ProjectService service.
// All implementations must embed UnimplementedProjectServiceServer
// for forward compatibility
type ProjectServiceServer interface {
	NewProject(context.Context, *Project) (*ProjectResult, error)
	DeleteProject(context.Context, *Project) (*ProjectResult, error)
	GetProjectList(context.Context, *ProjectListParam) (*ProjectList, error)
	GetProjectById(context.Context, *ProjectParam) (*Project, error)
	GetProjectByName(context.Context, *ProjectParam) (*Project, error)
	mustEmbedUnimplementedProjectServiceServer()
}

// UnimplementedProjectServiceServer must be embedded to have forward compatible implementations.
type UnimplementedProjectServiceServer struct {
}

func (UnimplementedProjectServiceServer) NewProject(context.Context, *Project) (*ProjectResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewProject not implemented")
}
func (UnimplementedProjectServiceServer) DeleteProject(context.Context, *Project) (*ProjectResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteProject not implemented")
}
func (UnimplementedProjectServiceServer) GetProjectList(context.Context, *ProjectListParam) (*ProjectList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProjectList not implemented")
}
func (UnimplementedProjectServiceServer) GetProjectById(context.Context, *ProjectParam) (*Project, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProjectById not implemented")
}
func (UnimplementedProjectServiceServer) GetProjectByName(context.Context, *ProjectParam) (*Project, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProjectByName not implemented")
}
func (UnimplementedProjectServiceServer) mustEmbedUnimplementedProjectServiceServer() {}

// UnsafeProjectServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ProjectServiceServer will
// result in compilation errors.
type UnsafeProjectServiceServer interface {
	mustEmbedUnimplementedProjectServiceServer()
}

func RegisterProjectServiceServer(s grpc.ServiceRegistrar, srv ProjectServiceServer) {
	s.RegisterService(&ProjectService_ServiceDesc, srv)
}

func _ProjectService_NewProject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Project)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectServiceServer).NewProject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectService_NewProject_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectServiceServer).NewProject(ctx, req.(*Project))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectService_DeleteProject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Project)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectServiceServer).DeleteProject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectService_DeleteProject_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectServiceServer).DeleteProject(ctx, req.(*Project))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectService_GetProjectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProjectListParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectServiceServer).GetProjectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectService_GetProjectList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectServiceServer).GetProjectList(ctx, req.(*ProjectListParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectService_GetProjectById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProjectParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectServiceServer).GetProjectById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectService_GetProjectById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectServiceServer).GetProjectById(ctx, req.(*ProjectParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectService_GetProjectByName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProjectParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectServiceServer).GetProjectByName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectService_GetProjectByName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectServiceServer).GetProjectByName(ctx, req.(*ProjectParam))
	}
	return interceptor(ctx, in, info, handler)
}

// ProjectService_ServiceDesc is the grpc.ServiceDesc for ProjectService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ProjectService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "app.ProjectService",
	HandlerType: (*ProjectServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "NewProject",
			Handler:    _ProjectService_NewProject_Handler,
		},
		{
			MethodName: "DeleteProject",
			Handler:    _ProjectService_DeleteProject_Handler,
		},
		{
			MethodName: "GetProjectList",
			Handler:    _ProjectService_GetProjectList_Handler,
		},
		{
			MethodName: "GetProjectById",
			Handler:    _ProjectService_GetProjectById_Handler,
		},
		{
			MethodName: "GetProjectByName",
			Handler:    _ProjectService_GetProjectByName_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "app/project.proto",
}
