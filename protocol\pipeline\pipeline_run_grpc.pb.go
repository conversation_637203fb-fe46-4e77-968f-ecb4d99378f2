// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: pipeline/pipeline_run.proto

package pipeline

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PipelineRunService_GetPipelineRunStatus_FullMethodName                         = "/pipeline.PipelineRunService/GetPipelineRunStatus"
	PipelineRunService_GetPipelineRunTask_FullMethodName                           = "/pipeline.PipelineRunService/GetPipelineRunTask"
	PipelineRunService_UpdatePipelineRunStatus_FullMethodName                      = "/pipeline.PipelineRunService/UpdatePipelineRunStatus"
	PipelineRunService_GetTaskRunByIds_FullMethodName                              = "/pipeline.PipelineRunService/GetTaskRunByIds"
	PipelineRunService_GetTaskRunBy_FullMethodName                                 = "/pipeline.PipelineRunService/GetTaskRunBy"
	PipelineRunService_GetTaskRunByStageAndType_FullMethodName                     = "/pipeline.PipelineRunService/GetTaskRunByStageAndType"
	PipelineRunService_GetPipelineRunByTaskRunId_FullMethodName                    = "/pipeline.PipelineRunService/GetPipelineRunByTaskRunId"
	PipelineRunService_GetArtifactVersionByPushImageTaskId_FullMethodName          = "/pipeline.PipelineRunService/GetArtifactVersionByPushImageTaskId"
	PipelineRunService_GetTaskRunWithSameBuildNumberBy_FullMethodName              = "/pipeline.PipelineRunService/GetTaskRunWithSameBuildNumberBy"
	PipelineRunService_GetNewApprovalTaskId_FullMethodName                         = "/pipeline.PipelineRunService/GetNewApprovalTaskId"
	PipelineRunService_GetRetryTimeout_FullMethodName                              = "/pipeline.PipelineRunService/GetRetryTimeout"
	PipelineRunService_GetPipelineRunTaskList_FullMethodName                       = "/pipeline.PipelineRunService/GetPipelineRunTaskList"
	PipelineRunService_ListPipelineRunTasks_FullMethodName                         = "/pipeline.PipelineRunService/ListPipelineRunTasks"
	PipelineRunService_GetLastRetryPipelineRunOfflineCanarySubTasks_FullMethodName = "/pipeline.PipelineRunService/GetLastRetryPipelineRunOfflineCanarySubTasks"
	PipelineRunService_GetTaskRunByStageRunId_FullMethodName                       = "/pipeline.PipelineRunService/GetTaskRunByStageRunId"
	PipelineRunService_GetPipelineRunGitRepoTopK_FullMethodName                    = "/pipeline.PipelineRunService/GetPipelineRunGitRepoTopK"
)

// PipelineRunServiceClient is the client API for PipelineRunService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PipelineRunServiceClient interface {
	GetPipelineRunStatus(ctx context.Context, in *GetPRStatusReq, opts ...grpc.CallOption) (*GetPRStatusResp, error)
	GetPipelineRunTask(ctx context.Context, in *GetPRTaskReq, opts ...grpc.CallOption) (*GetPRTaskResp, error)
	UpdatePipelineRunStatus(ctx context.Context, in *TaskStatus, opts ...grpc.CallOption) (*UpdateStatusResp, error)
	GetTaskRunByIds(ctx context.Context, in *TaskRunReq, opts ...grpc.CallOption) (*TaskRunResp, error)
	GetTaskRunBy(ctx context.Context, in *TaskRunReq, opts ...grpc.CallOption) (*TaskRunResp, error)
	GetTaskRunByStageAndType(ctx context.Context, in *TaskRunQueryReq, opts ...grpc.CallOption) (*GetPRTaskResp, error)
	GetPipelineRunByTaskRunId(ctx context.Context, in *GetPRTaskReq, opts ...grpc.CallOption) (*GetPRStatusResp, error)
	GetArtifactVersionByPushImageTaskId(ctx context.Context, in *GetPRTaskReq, opts ...grpc.CallOption) (*PushImageTaskResp, error)
	GetTaskRunWithSameBuildNumberBy(ctx context.Context, in *GetPRTaskReq, opts ...grpc.CallOption) (*TaskRunRecordsResp, error)
	GetNewApprovalTaskId(ctx context.Context, in *NewApprovalTaskReq, opts ...grpc.CallOption) (*NewApprovalTaskResp, error)
	GetRetryTimeout(ctx context.Context, in *TaskTimeoutReq, opts ...grpc.CallOption) (*TaskTimeoutResp, error)
	GetPipelineRunTaskList(ctx context.Context, in *TaskRunListReq, opts ...grpc.CallOption) (*TaskRunResp, error)
	ListPipelineRunTasks(ctx context.Context, in *ListPRTaskReq, opts ...grpc.CallOption) (*ListPRTaskResp, error)
	GetLastRetryPipelineRunOfflineCanarySubTasks(ctx context.Context, in *GetPRTaskReq, opts ...grpc.CallOption) (*LastRetryPipelineRunOfflineCanarySubTasksResp, error)
	GetTaskRunByStageRunId(ctx context.Context, in *TaskRunQueryReq, opts ...grpc.CallOption) (*GetPRTaskRespList, error)
	// 获取流水线运行 git repo top k 统计
	GetPipelineRunGitRepoTopK(ctx context.Context, in *GetPipelineRunGitRepoTopKRequest, opts ...grpc.CallOption) (*GetPipelineRunGitRepoTopKResponse, error)
}

type pipelineRunServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPipelineRunServiceClient(cc grpc.ClientConnInterface) PipelineRunServiceClient {
	return &pipelineRunServiceClient{cc}
}

func (c *pipelineRunServiceClient) GetPipelineRunStatus(ctx context.Context, in *GetPRStatusReq, opts ...grpc.CallOption) (*GetPRStatusResp, error) {
	out := new(GetPRStatusResp)
	err := c.cc.Invoke(ctx, PipelineRunService_GetPipelineRunStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineRunServiceClient) GetPipelineRunTask(ctx context.Context, in *GetPRTaskReq, opts ...grpc.CallOption) (*GetPRTaskResp, error) {
	out := new(GetPRTaskResp)
	err := c.cc.Invoke(ctx, PipelineRunService_GetPipelineRunTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineRunServiceClient) UpdatePipelineRunStatus(ctx context.Context, in *TaskStatus, opts ...grpc.CallOption) (*UpdateStatusResp, error) {
	out := new(UpdateStatusResp)
	err := c.cc.Invoke(ctx, PipelineRunService_UpdatePipelineRunStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineRunServiceClient) GetTaskRunByIds(ctx context.Context, in *TaskRunReq, opts ...grpc.CallOption) (*TaskRunResp, error) {
	out := new(TaskRunResp)
	err := c.cc.Invoke(ctx, PipelineRunService_GetTaskRunByIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineRunServiceClient) GetTaskRunBy(ctx context.Context, in *TaskRunReq, opts ...grpc.CallOption) (*TaskRunResp, error) {
	out := new(TaskRunResp)
	err := c.cc.Invoke(ctx, PipelineRunService_GetTaskRunBy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineRunServiceClient) GetTaskRunByStageAndType(ctx context.Context, in *TaskRunQueryReq, opts ...grpc.CallOption) (*GetPRTaskResp, error) {
	out := new(GetPRTaskResp)
	err := c.cc.Invoke(ctx, PipelineRunService_GetTaskRunByStageAndType_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineRunServiceClient) GetPipelineRunByTaskRunId(ctx context.Context, in *GetPRTaskReq, opts ...grpc.CallOption) (*GetPRStatusResp, error) {
	out := new(GetPRStatusResp)
	err := c.cc.Invoke(ctx, PipelineRunService_GetPipelineRunByTaskRunId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineRunServiceClient) GetArtifactVersionByPushImageTaskId(ctx context.Context, in *GetPRTaskReq, opts ...grpc.CallOption) (*PushImageTaskResp, error) {
	out := new(PushImageTaskResp)
	err := c.cc.Invoke(ctx, PipelineRunService_GetArtifactVersionByPushImageTaskId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineRunServiceClient) GetTaskRunWithSameBuildNumberBy(ctx context.Context, in *GetPRTaskReq, opts ...grpc.CallOption) (*TaskRunRecordsResp, error) {
	out := new(TaskRunRecordsResp)
	err := c.cc.Invoke(ctx, PipelineRunService_GetTaskRunWithSameBuildNumberBy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineRunServiceClient) GetNewApprovalTaskId(ctx context.Context, in *NewApprovalTaskReq, opts ...grpc.CallOption) (*NewApprovalTaskResp, error) {
	out := new(NewApprovalTaskResp)
	err := c.cc.Invoke(ctx, PipelineRunService_GetNewApprovalTaskId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineRunServiceClient) GetRetryTimeout(ctx context.Context, in *TaskTimeoutReq, opts ...grpc.CallOption) (*TaskTimeoutResp, error) {
	out := new(TaskTimeoutResp)
	err := c.cc.Invoke(ctx, PipelineRunService_GetRetryTimeout_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineRunServiceClient) GetPipelineRunTaskList(ctx context.Context, in *TaskRunListReq, opts ...grpc.CallOption) (*TaskRunResp, error) {
	out := new(TaskRunResp)
	err := c.cc.Invoke(ctx, PipelineRunService_GetPipelineRunTaskList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineRunServiceClient) ListPipelineRunTasks(ctx context.Context, in *ListPRTaskReq, opts ...grpc.CallOption) (*ListPRTaskResp, error) {
	out := new(ListPRTaskResp)
	err := c.cc.Invoke(ctx, PipelineRunService_ListPipelineRunTasks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineRunServiceClient) GetLastRetryPipelineRunOfflineCanarySubTasks(ctx context.Context, in *GetPRTaskReq, opts ...grpc.CallOption) (*LastRetryPipelineRunOfflineCanarySubTasksResp, error) {
	out := new(LastRetryPipelineRunOfflineCanarySubTasksResp)
	err := c.cc.Invoke(ctx, PipelineRunService_GetLastRetryPipelineRunOfflineCanarySubTasks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineRunServiceClient) GetTaskRunByStageRunId(ctx context.Context, in *TaskRunQueryReq, opts ...grpc.CallOption) (*GetPRTaskRespList, error) {
	out := new(GetPRTaskRespList)
	err := c.cc.Invoke(ctx, PipelineRunService_GetTaskRunByStageRunId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineRunServiceClient) GetPipelineRunGitRepoTopK(ctx context.Context, in *GetPipelineRunGitRepoTopKRequest, opts ...grpc.CallOption) (*GetPipelineRunGitRepoTopKResponse, error) {
	out := new(GetPipelineRunGitRepoTopKResponse)
	err := c.cc.Invoke(ctx, PipelineRunService_GetPipelineRunGitRepoTopK_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PipelineRunServiceServer is the server API for PipelineRunService service.
// All implementations must embed UnimplementedPipelineRunServiceServer
// for forward compatibility
type PipelineRunServiceServer interface {
	GetPipelineRunStatus(context.Context, *GetPRStatusReq) (*GetPRStatusResp, error)
	GetPipelineRunTask(context.Context, *GetPRTaskReq) (*GetPRTaskResp, error)
	UpdatePipelineRunStatus(context.Context, *TaskStatus) (*UpdateStatusResp, error)
	GetTaskRunByIds(context.Context, *TaskRunReq) (*TaskRunResp, error)
	GetTaskRunBy(context.Context, *TaskRunReq) (*TaskRunResp, error)
	GetTaskRunByStageAndType(context.Context, *TaskRunQueryReq) (*GetPRTaskResp, error)
	GetPipelineRunByTaskRunId(context.Context, *GetPRTaskReq) (*GetPRStatusResp, error)
	GetArtifactVersionByPushImageTaskId(context.Context, *GetPRTaskReq) (*PushImageTaskResp, error)
	GetTaskRunWithSameBuildNumberBy(context.Context, *GetPRTaskReq) (*TaskRunRecordsResp, error)
	GetNewApprovalTaskId(context.Context, *NewApprovalTaskReq) (*NewApprovalTaskResp, error)
	GetRetryTimeout(context.Context, *TaskTimeoutReq) (*TaskTimeoutResp, error)
	GetPipelineRunTaskList(context.Context, *TaskRunListReq) (*TaskRunResp, error)
	ListPipelineRunTasks(context.Context, *ListPRTaskReq) (*ListPRTaskResp, error)
	GetLastRetryPipelineRunOfflineCanarySubTasks(context.Context, *GetPRTaskReq) (*LastRetryPipelineRunOfflineCanarySubTasksResp, error)
	GetTaskRunByStageRunId(context.Context, *TaskRunQueryReq) (*GetPRTaskRespList, error)
	// 获取流水线运行 git repo top k 统计
	GetPipelineRunGitRepoTopK(context.Context, *GetPipelineRunGitRepoTopKRequest) (*GetPipelineRunGitRepoTopKResponse, error)
	mustEmbedUnimplementedPipelineRunServiceServer()
}

// UnimplementedPipelineRunServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPipelineRunServiceServer struct {
}

func (UnimplementedPipelineRunServiceServer) GetPipelineRunStatus(context.Context, *GetPRStatusReq) (*GetPRStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPipelineRunStatus not implemented")
}
func (UnimplementedPipelineRunServiceServer) GetPipelineRunTask(context.Context, *GetPRTaskReq) (*GetPRTaskResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPipelineRunTask not implemented")
}
func (UnimplementedPipelineRunServiceServer) UpdatePipelineRunStatus(context.Context, *TaskStatus) (*UpdateStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePipelineRunStatus not implemented")
}
func (UnimplementedPipelineRunServiceServer) GetTaskRunByIds(context.Context, *TaskRunReq) (*TaskRunResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaskRunByIds not implemented")
}
func (UnimplementedPipelineRunServiceServer) GetTaskRunBy(context.Context, *TaskRunReq) (*TaskRunResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaskRunBy not implemented")
}
func (UnimplementedPipelineRunServiceServer) GetTaskRunByStageAndType(context.Context, *TaskRunQueryReq) (*GetPRTaskResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaskRunByStageAndType not implemented")
}
func (UnimplementedPipelineRunServiceServer) GetPipelineRunByTaskRunId(context.Context, *GetPRTaskReq) (*GetPRStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPipelineRunByTaskRunId not implemented")
}
func (UnimplementedPipelineRunServiceServer) GetArtifactVersionByPushImageTaskId(context.Context, *GetPRTaskReq) (*PushImageTaskResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetArtifactVersionByPushImageTaskId not implemented")
}
func (UnimplementedPipelineRunServiceServer) GetTaskRunWithSameBuildNumberBy(context.Context, *GetPRTaskReq) (*TaskRunRecordsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaskRunWithSameBuildNumberBy not implemented")
}
func (UnimplementedPipelineRunServiceServer) GetNewApprovalTaskId(context.Context, *NewApprovalTaskReq) (*NewApprovalTaskResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNewApprovalTaskId not implemented")
}
func (UnimplementedPipelineRunServiceServer) GetRetryTimeout(context.Context, *TaskTimeoutReq) (*TaskTimeoutResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRetryTimeout not implemented")
}
func (UnimplementedPipelineRunServiceServer) GetPipelineRunTaskList(context.Context, *TaskRunListReq) (*TaskRunResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPipelineRunTaskList not implemented")
}
func (UnimplementedPipelineRunServiceServer) ListPipelineRunTasks(context.Context, *ListPRTaskReq) (*ListPRTaskResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPipelineRunTasks not implemented")
}
func (UnimplementedPipelineRunServiceServer) GetLastRetryPipelineRunOfflineCanarySubTasks(context.Context, *GetPRTaskReq) (*LastRetryPipelineRunOfflineCanarySubTasksResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLastRetryPipelineRunOfflineCanarySubTasks not implemented")
}
func (UnimplementedPipelineRunServiceServer) GetTaskRunByStageRunId(context.Context, *TaskRunQueryReq) (*GetPRTaskRespList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaskRunByStageRunId not implemented")
}
func (UnimplementedPipelineRunServiceServer) GetPipelineRunGitRepoTopK(context.Context, *GetPipelineRunGitRepoTopKRequest) (*GetPipelineRunGitRepoTopKResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPipelineRunGitRepoTopK not implemented")
}
func (UnimplementedPipelineRunServiceServer) mustEmbedUnimplementedPipelineRunServiceServer() {}

// UnsafePipelineRunServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PipelineRunServiceServer will
// result in compilation errors.
type UnsafePipelineRunServiceServer interface {
	mustEmbedUnimplementedPipelineRunServiceServer()
}

func RegisterPipelineRunServiceServer(s grpc.ServiceRegistrar, srv PipelineRunServiceServer) {
	s.RegisterService(&PipelineRunService_ServiceDesc, srv)
}

func _PipelineRunService_GetPipelineRunStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPRStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineRunServiceServer).GetPipelineRunStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineRunService_GetPipelineRunStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineRunServiceServer).GetPipelineRunStatus(ctx, req.(*GetPRStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineRunService_GetPipelineRunTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPRTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineRunServiceServer).GetPipelineRunTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineRunService_GetPipelineRunTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineRunServiceServer).GetPipelineRunTask(ctx, req.(*GetPRTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineRunService_UpdatePipelineRunStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskStatus)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineRunServiceServer).UpdatePipelineRunStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineRunService_UpdatePipelineRunStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineRunServiceServer).UpdatePipelineRunStatus(ctx, req.(*TaskStatus))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineRunService_GetTaskRunByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskRunReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineRunServiceServer).GetTaskRunByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineRunService_GetTaskRunByIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineRunServiceServer).GetTaskRunByIds(ctx, req.(*TaskRunReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineRunService_GetTaskRunBy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskRunReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineRunServiceServer).GetTaskRunBy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineRunService_GetTaskRunBy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineRunServiceServer).GetTaskRunBy(ctx, req.(*TaskRunReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineRunService_GetTaskRunByStageAndType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskRunQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineRunServiceServer).GetTaskRunByStageAndType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineRunService_GetTaskRunByStageAndType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineRunServiceServer).GetTaskRunByStageAndType(ctx, req.(*TaskRunQueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineRunService_GetPipelineRunByTaskRunId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPRTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineRunServiceServer).GetPipelineRunByTaskRunId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineRunService_GetPipelineRunByTaskRunId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineRunServiceServer).GetPipelineRunByTaskRunId(ctx, req.(*GetPRTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineRunService_GetArtifactVersionByPushImageTaskId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPRTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineRunServiceServer).GetArtifactVersionByPushImageTaskId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineRunService_GetArtifactVersionByPushImageTaskId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineRunServiceServer).GetArtifactVersionByPushImageTaskId(ctx, req.(*GetPRTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineRunService_GetTaskRunWithSameBuildNumberBy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPRTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineRunServiceServer).GetTaskRunWithSameBuildNumberBy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineRunService_GetTaskRunWithSameBuildNumberBy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineRunServiceServer).GetTaskRunWithSameBuildNumberBy(ctx, req.(*GetPRTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineRunService_GetNewApprovalTaskId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewApprovalTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineRunServiceServer).GetNewApprovalTaskId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineRunService_GetNewApprovalTaskId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineRunServiceServer).GetNewApprovalTaskId(ctx, req.(*NewApprovalTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineRunService_GetRetryTimeout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskTimeoutReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineRunServiceServer).GetRetryTimeout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineRunService_GetRetryTimeout_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineRunServiceServer).GetRetryTimeout(ctx, req.(*TaskTimeoutReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineRunService_GetPipelineRunTaskList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskRunListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineRunServiceServer).GetPipelineRunTaskList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineRunService_GetPipelineRunTaskList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineRunServiceServer).GetPipelineRunTaskList(ctx, req.(*TaskRunListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineRunService_ListPipelineRunTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPRTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineRunServiceServer).ListPipelineRunTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineRunService_ListPipelineRunTasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineRunServiceServer).ListPipelineRunTasks(ctx, req.(*ListPRTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineRunService_GetLastRetryPipelineRunOfflineCanarySubTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPRTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineRunServiceServer).GetLastRetryPipelineRunOfflineCanarySubTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineRunService_GetLastRetryPipelineRunOfflineCanarySubTasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineRunServiceServer).GetLastRetryPipelineRunOfflineCanarySubTasks(ctx, req.(*GetPRTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineRunService_GetTaskRunByStageRunId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskRunQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineRunServiceServer).GetTaskRunByStageRunId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineRunService_GetTaskRunByStageRunId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineRunServiceServer).GetTaskRunByStageRunId(ctx, req.(*TaskRunQueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineRunService_GetPipelineRunGitRepoTopK_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPipelineRunGitRepoTopKRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineRunServiceServer).GetPipelineRunGitRepoTopK(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineRunService_GetPipelineRunGitRepoTopK_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineRunServiceServer).GetPipelineRunGitRepoTopK(ctx, req.(*GetPipelineRunGitRepoTopKRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PipelineRunService_ServiceDesc is the grpc.ServiceDesc for PipelineRunService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PipelineRunService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pipeline.PipelineRunService",
	HandlerType: (*PipelineRunServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPipelineRunStatus",
			Handler:    _PipelineRunService_GetPipelineRunStatus_Handler,
		},
		{
			MethodName: "GetPipelineRunTask",
			Handler:    _PipelineRunService_GetPipelineRunTask_Handler,
		},
		{
			MethodName: "UpdatePipelineRunStatus",
			Handler:    _PipelineRunService_UpdatePipelineRunStatus_Handler,
		},
		{
			MethodName: "GetTaskRunByIds",
			Handler:    _PipelineRunService_GetTaskRunByIds_Handler,
		},
		{
			MethodName: "GetTaskRunBy",
			Handler:    _PipelineRunService_GetTaskRunBy_Handler,
		},
		{
			MethodName: "GetTaskRunByStageAndType",
			Handler:    _PipelineRunService_GetTaskRunByStageAndType_Handler,
		},
		{
			MethodName: "GetPipelineRunByTaskRunId",
			Handler:    _PipelineRunService_GetPipelineRunByTaskRunId_Handler,
		},
		{
			MethodName: "GetArtifactVersionByPushImageTaskId",
			Handler:    _PipelineRunService_GetArtifactVersionByPushImageTaskId_Handler,
		},
		{
			MethodName: "GetTaskRunWithSameBuildNumberBy",
			Handler:    _PipelineRunService_GetTaskRunWithSameBuildNumberBy_Handler,
		},
		{
			MethodName: "GetNewApprovalTaskId",
			Handler:    _PipelineRunService_GetNewApprovalTaskId_Handler,
		},
		{
			MethodName: "GetRetryTimeout",
			Handler:    _PipelineRunService_GetRetryTimeout_Handler,
		},
		{
			MethodName: "GetPipelineRunTaskList",
			Handler:    _PipelineRunService_GetPipelineRunTaskList_Handler,
		},
		{
			MethodName: "ListPipelineRunTasks",
			Handler:    _PipelineRunService_ListPipelineRunTasks_Handler,
		},
		{
			MethodName: "GetLastRetryPipelineRunOfflineCanarySubTasks",
			Handler:    _PipelineRunService_GetLastRetryPipelineRunOfflineCanarySubTasks_Handler,
		},
		{
			MethodName: "GetTaskRunByStageRunId",
			Handler:    _PipelineRunService_GetTaskRunByStageRunId_Handler,
		},
		{
			MethodName: "GetPipelineRunGitRepoTopK",
			Handler:    _PipelineRunService_GetPipelineRunGitRepoTopK_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pipeline/pipeline_run.proto",
}
