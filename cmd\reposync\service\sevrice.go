package service

import (
	"context"
	"golang.ttyuyin.com/harmony/csi-driver/pkg/config"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"golang.ttyuyin.com/harmony/csi-driver/pkg/bootstrap"
	"golang.ttyuyin.com/harmony/csi-driver/pkg/task"
	ppb "golang.ttyuyin.com/harmony/csi-driver/protocol/pipeline"
	"golang.ttyuyin.com/harmony/pkg/log"
)

func New(cfg *config.Config, tm *task.Manager, prCli ppb.PipelineRunServiceClient) *LowerDirHandler {
	return &LowerDirHandler{
		cfg:      cfg,
		manager:  tm,
		prClient: prCli,
	}
}

type LowerDirHandler struct {
	cfg      *config.Config
	manager  *task.Manager
	prClient ppb.PipelineRunServiceClient
}

func (h *LowerDirHandler) Handle(ctx context.Context, rootPath, url, branch string) {
	h.manager.Handle(ctx, rootPath, url, branch)
}

func (h *LowerDirHandler) GetRepoBuildBranchTopK(ctx context.Context, url string, topK int32) ([]task.Params, error) {
	currentTime := time.Now()
	previousTime := currentTime.AddDate(0, 0, -30)
	resp, err := h.prClient.GetPipelineRunGitRepoTopK(ctx, &ppb.GetPipelineRunGitRepoTopKRequest{
		RepoAddr:  url,
		StartTime: timestamppb.New(previousTime),
		EndTime:   timestamppb.New(currentTime),
		TopK:      topK,
	})
	if err != nil {
		log.Errorf("get top %d branch failed, err: %v, url=%s", topK, err, url)
		return nil, err
	}

	tasks := make([]task.Params, 0, len(resp.GetBranches()))
	for _, bs := range resp.GetBranches() {
		tasks = append(tasks, task.Params{Branch: bs.GetBranch(), RepoURL: url})
	}

	return tasks, nil
}

func (h *LowerDirHandler) getAllRepoBranch(ctx context.Context) []task.Params {
	topK := h.cfg.TopK
	repos := getAllRepo()

	repoLen := len(repos)
	allTasks := make([]task.Params, 0, repoLen+repoLen*int(topK))
	allTasks = append(allTasks, repos...)
	for _, r := range repos {
		repoBranches, err := h.GetRepoBuildBranchTopK(ctx, r.RepoURL, topK)
		if err != nil {
			log.Infof("get repo %s top %d failed, err: %v", r.RepoURL, topK, err)
			continue
		}

		log.Infof("get repo top %d, repo=%s, branch len=%d", topK, r.RepoURL, len(repoBranches))
		allTasks = append(allTasks, repoBranches...)
	}
	log.Infof("get all repo master branches including top %d branches, all len=%d", topK, len(allTasks))
	return allTasks
}

func (h *LowerDirHandler) RootPath() string {
	return h.manager.GetNextLowerDirRootPath()
}

func (h *LowerDirHandler) Repos() []task.Params {
	return h.getAllRepoBranch(context.Background())
}

// FirstLoading 首次加载用到，部署后不再使用，后面再去掉这个方法
func (h *LowerDirHandler) FirstLoading(ctx context.Context) {
	repos := h.Repos()
	rootPath := h.manager.GetCurrentLowerDirRootPath()
	log.Infof("start first loading, repos=%d, rootPath=%s", len(repos), rootPath)
	for _, param := range repos {
		select {
		case <-ctx.Done():
			log.Infof("stop repo sync, first loading ctx done")
			return
		default:
			log.Infof("start clone the repo(%s) branch(%s)", param.RepoURL, param.Branch)
			if !bootstrap.DebugEnabled() {
				h.Handle(ctx, rootPath, param.RepoURL, param.Branch)
			}
		}
	}
}

func NewTempCronHandler(tm *task.Manager) *TempCronHandler {
	return &TempCronHandler{
		manager: tm,
	}
}

type TempCronHandler struct {
	manager *task.Manager
}

func (h *TempCronHandler) Repos() []task.Params {
	return getAllRepo()
}

func (h *TempCronHandler) Handle(ctx context.Context, rootPath, url, branch string) {
	h.manager.Handle(ctx, rootPath, url, branch)
}

func (h *TempCronHandler) RootPath() string {
	return h.manager.GetCurrentLowerDirRootPath()
}
