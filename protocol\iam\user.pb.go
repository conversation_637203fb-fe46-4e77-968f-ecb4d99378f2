// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        (unknown)
// source: iam/user.proto

package iam

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 用户信息
type UserParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
}

func (x *UserParam) Reset() {
	*x = UserParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_user_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserParam) ProtoMessage() {}

func (x *UserParam) ProtoReflect() protoreflect.Message {
	mi := &file_iam_user_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserParam.ProtoReflect.Descriptor instead.
func (*UserParam) Descriptor() ([]byte, []int) {
	return file_iam_user_proto_rawDescGZIP(), []int{0}
}

func (x *UserParam) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type UserLarkUnionId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LarkUnionId string `protobuf:"bytes,1,opt,name=larkUnionId,proto3" json:"larkUnionId,omitempty"`
}

func (x *UserLarkUnionId) Reset() {
	*x = UserLarkUnionId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_user_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserLarkUnionId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserLarkUnionId) ProtoMessage() {}

func (x *UserLarkUnionId) ProtoReflect() protoreflect.Message {
	mi := &file_iam_user_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserLarkUnionId.ProtoReflect.Descriptor instead.
func (*UserLarkUnionId) Descriptor() ([]byte, []int) {
	return file_iam_user_proto_rawDescGZIP(), []int{1}
}

func (x *UserLarkUnionId) GetLarkUnionId() string {
	if x != nil {
		return x.LarkUnionId
	}
	return ""
}

type UserSearchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keyword string `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
}

func (x *UserSearchReq) Reset() {
	*x = UserSearchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_user_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserSearchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSearchReq) ProtoMessage() {}

func (x *UserSearchReq) ProtoReflect() protoreflect.Message {
	mi := &file_iam_user_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSearchReq.ProtoReflect.Descriptor instead.
func (*UserSearchReq) Descriptor() ([]byte, []int) {
	return file_iam_user_proto_rawDescGZIP(), []int{2}
}

func (x *UserSearchReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

type GitlabQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GitlabId int64 `protobuf:"varint,1,opt,name=gitlabId,proto3" json:"gitlabId,omitempty"`
}

func (x *GitlabQuery) Reset() {
	*x = GitlabQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_user_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GitlabQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GitlabQuery) ProtoMessage() {}

func (x *GitlabQuery) ProtoReflect() protoreflect.Message {
	mi := &file_iam_user_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GitlabQuery.ProtoReflect.Descriptor instead.
func (*GitlabQuery) Descriptor() ([]byte, []int) {
	return file_iam_user_proto_rawDescGZIP(), []int{3}
}

func (x *GitlabQuery) GetGitlabId() int64 {
	if x != nil {
		return x.GitlabId
	}
	return 0
}

type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Username    string `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	ChineseName string `protobuf:"bytes,3,opt,name=chineseName,proto3" json:"chineseName,omitempty"`
	Email       string `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	EmployeeNo  string `protobuf:"bytes,5,opt,name=employeeNo,proto3" json:"employeeNo,omitempty"`
	LarkUnionId string `protobuf:"bytes,6,opt,name=larkUnionId,proto3" json:"larkUnionId,omitempty"`
	Role        string `protobuf:"bytes,7,opt,name=role,proto3" json:"role,omitempty"`
	// 系统角色
	RoleObj   *Role                  `protobuf:"bytes,8,opt,name=roleObj,proto3,oneof" json:"roleObj,omitempty"`
	GitlabId  int64                  `protobuf:"varint,9,opt,name=gitlabId,proto3" json:"gitlabId,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	ProjectId int64                  `protobuf:"varint,12,opt,name=projectId,proto3" json:"projectId,omitempty"`
	// 用户全局角色+项目角色
	Roles []*Role `protobuf:"bytes,13,rep,name=roles,proto3" json:"roles,omitempty"`
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_user_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_iam_user_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_iam_user_proto_rawDescGZIP(), []int{4}
}

func (x *User) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *User) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *User) GetChineseName() string {
	if x != nil {
		return x.ChineseName
	}
	return ""
}

func (x *User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *User) GetEmployeeNo() string {
	if x != nil {
		return x.EmployeeNo
	}
	return ""
}

func (x *User) GetLarkUnionId() string {
	if x != nil {
		return x.LarkUnionId
	}
	return ""
}

func (x *User) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *User) GetRoleObj() *Role {
	if x != nil {
		return x.RoleObj
	}
	return nil
}

func (x *User) GetGitlabId() int64 {
	if x != nil {
		return x.GitlabId
	}
	return 0
}

func (x *User) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *User) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *User) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *User) GetRoles() []*Role {
	if x != nil {
		return x.Roles
	}
	return nil
}

type UserPreference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId          int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	LatestProjectId int64 `protobuf:"varint,2,opt,name=latestProjectId,proto3" json:"latestProjectId,omitempty"`
}

func (x *UserPreference) Reset() {
	*x = UserPreference{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_user_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserPreference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPreference) ProtoMessage() {}

func (x *UserPreference) ProtoReflect() protoreflect.Message {
	mi := &file_iam_user_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPreference.ProtoReflect.Descriptor instead.
func (*UserPreference) Descriptor() ([]byte, []int) {
	return file_iam_user_proto_rawDescGZIP(), []int{5}
}

func (x *UserPreference) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserPreference) GetLatestProjectId() int64 {
	if x != nil {
		return x.LatestProjectId
	}
	return 0
}

type UserSearch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId int64 `protobuf:"varint,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// 页码
	Page int32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	// 每页显示的记录数
	Size int32 `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	// 查询条件(用户名)
	Username string `protobuf:"bytes,4,opt,name=username,proto3" json:"username,omitempty"`
	// 查询条件(工号)
	EmployeeNo string `protobuf:"bytes,5,opt,name=employee_no,json=employeeNo,proto3" json:"employee_no,omitempty"`
	// 查询条件（邮箱）
	Email    string `protobuf:"bytes,6,opt,name=email,proto3" json:"email,omitempty"`
	RoleId   int32  `protobuf:"varint,7,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	RoleName string `protobuf:"bytes,8,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
}

func (x *UserSearch) Reset() {
	*x = UserSearch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_user_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserSearch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSearch) ProtoMessage() {}

func (x *UserSearch) ProtoReflect() protoreflect.Message {
	mi := &file_iam_user_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSearch.ProtoReflect.Descriptor instead.
func (*UserSearch) Descriptor() ([]byte, []int) {
	return file_iam_user_proto_rawDescGZIP(), []int{6}
}

func (x *UserSearch) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *UserSearch) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *UserSearch) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *UserSearch) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UserSearch) GetEmployeeNo() string {
	if x != nil {
		return x.EmployeeNo
	}
	return ""
}

func (x *UserSearch) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserSearch) GetRoleId() int32 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *UserSearch) GetRoleName() string {
	if x != nil {
		return x.RoleName
	}
	return ""
}

type ProjectQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId int64 `protobuf:"varint,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
}

func (x *ProjectQuery) Reset() {
	*x = ProjectQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_user_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectQuery) ProtoMessage() {}

func (x *ProjectQuery) ProtoReflect() protoreflect.Message {
	mi := &file_iam_user_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectQuery.ProtoReflect.Descriptor instead.
func (*ProjectQuery) Descriptor() ([]byte, []int) {
	return file_iam_user_proto_rawDescGZIP(), []int{7}
}

func (x *ProjectQuery) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

type UserIdsQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id []int64 `protobuf:"varint,1,rep,packed,name=id,proto3" json:"id,omitempty"`
}

func (x *UserIdsQuery) Reset() {
	*x = UserIdsQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_user_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIdsQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdsQuery) ProtoMessage() {}

func (x *UserIdsQuery) ProtoReflect() protoreflect.Message {
	mi := &file_iam_user_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdsQuery.ProtoReflect.Descriptor instead.
func (*UserIdsQuery) Descriptor() ([]byte, []int) {
	return file_iam_user_proto_rawDescGZIP(), []int{8}
}

func (x *UserIdsQuery) GetId() []int64 {
	if x != nil {
		return x.Id
	}
	return nil
}

type UserQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Totals int64   `protobuf:"varint,1,opt,name=totals,proto3" json:"totals,omitempty"`
	Users  []*User `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *UserQueryResponse) Reset() {
	*x = UserQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_user_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserQueryResponse) ProtoMessage() {}

func (x *UserQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_iam_user_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserQueryResponse.ProtoReflect.Descriptor instead.
func (*UserQueryResponse) Descriptor() ([]byte, []int) {
	return file_iam_user_proto_rawDescGZIP(), []int{9}
}

func (x *UserQueryResponse) GetTotals() int64 {
	if x != nil {
		return x.Totals
	}
	return 0
}

func (x *UserQueryResponse) GetUsers() []*User {
	if x != nil {
		return x.Users
	}
	return nil
}

type UserPreferenceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserPreferences *UserPreference `protobuf:"bytes,1,opt,name=UserPreferences,proto3" json:"UserPreferences,omitempty"`
}

func (x *UserPreferenceResponse) Reset() {
	*x = UserPreferenceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_user_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserPreferenceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPreferenceResponse) ProtoMessage() {}

func (x *UserPreferenceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_iam_user_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPreferenceResponse.ProtoReflect.Descriptor instead.
func (*UserPreferenceResponse) Descriptor() ([]byte, []int) {
	return file_iam_user_proto_rawDescGZIP(), []int{10}
}

func (x *UserPreferenceResponse) GetUserPreferences() *UserPreference {
	if x != nil {
		return x.UserPreferences
	}
	return nil
}

type UserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	User *User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
}

func (x *UserResponse) Reset() {
	*x = UserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_user_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserResponse) ProtoMessage() {}

func (x *UserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_iam_user_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserResponse.ProtoReflect.Descriptor instead.
func (*UserResponse) Descriptor() ([]byte, []int) {
	return file_iam_user_proto_rawDescGZIP(), []int{11}
}

func (x *UserResponse) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

type FindUserByUniqConditionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"userID"
	UserId   *int64  `protobuf:"varint,1,opt,name=user_id,json=userID,proto3,oneof" json:"user_id,omitempty"`
	Username *string `protobuf:"bytes,2,opt,name=username,proto3,oneof" json:"username,omitempty"`
	Email    *string `protobuf:"bytes,3,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// @gotags: json:"employeeNo"
	EmployeeNo *string `protobuf:"bytes,4,opt,name=employee_no,json=employeeNo,proto3,oneof" json:"employee_no,omitempty"`
	// @gotags: json:"larkUnionID"
	LarkUnionId *string `protobuf:"bytes,5,opt,name=lark_union_id,json=larkUnionID,proto3,oneof" json:"lark_union_id,omitempty"`
	// @gotags: json:"gitlabID"
	GitlabId *int64 `protobuf:"varint,6,opt,name=gitlab_id,json=gitlabID,proto3,oneof" json:"gitlab_id,omitempty"`
}

func (x *FindUserByUniqConditionReq) Reset() {
	*x = FindUserByUniqConditionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_user_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindUserByUniqConditionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindUserByUniqConditionReq) ProtoMessage() {}

func (x *FindUserByUniqConditionReq) ProtoReflect() protoreflect.Message {
	mi := &file_iam_user_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindUserByUniqConditionReq.ProtoReflect.Descriptor instead.
func (*FindUserByUniqConditionReq) Descriptor() ([]byte, []int) {
	return file_iam_user_proto_rawDescGZIP(), []int{12}
}

func (x *FindUserByUniqConditionReq) GetUserId() int64 {
	if x != nil && x.UserId != nil {
		return *x.UserId
	}
	return 0
}

func (x *FindUserByUniqConditionReq) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

func (x *FindUserByUniqConditionReq) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *FindUserByUniqConditionReq) GetEmployeeNo() string {
	if x != nil && x.EmployeeNo != nil {
		return *x.EmployeeNo
	}
	return ""
}

func (x *FindUserByUniqConditionReq) GetLarkUnionId() string {
	if x != nil && x.LarkUnionId != nil {
		return *x.LarkUnionId
	}
	return ""
}

func (x *FindUserByUniqConditionReq) GetGitlabId() int64 {
	if x != nil && x.GitlabId != nil {
		return *x.GitlabId
	}
	return 0
}

var File_iam_user_proto protoreflect.FileDescriptor

var file_iam_user_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x69, 0x61, 0x6d, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x03, 0x69, 0x61, 0x6d, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x0e, 0x69, 0x61, 0x6d, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x23, 0x0a, 0x09, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x33, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72,
	0x4c, 0x61, 0x72, 0x6b, 0x55, 0x6e, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6c,
	0x61, 0x72, 0x6b, 0x55, 0x6e, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x6c, 0x61, 0x72, 0x6b, 0x55, 0x6e, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x29, 0x0a,
	0x0d, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x18,
	0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x29, 0x0a, 0x0b, 0x47, 0x69, 0x74, 0x6c,
	0x61, 0x62, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x49, 0x64, 0x22, 0xc5, 0x03, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x4e, 0x6f, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x4e, 0x6f,
	0x12, 0x20, 0x0a, 0x0b, 0x6c, 0x61, 0x72, 0x6b, 0x55, 0x6e, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x61, 0x72, 0x6b, 0x55, 0x6e, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x4f, 0x62,
	0x6a, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x6f,
	0x6c, 0x65, 0x48, 0x00, 0x52, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x4f, 0x62, 0x6a, 0x88, 0x01, 0x01,
	0x12, 0x1a, 0x0a, 0x08, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x38, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x42,
	0x0a, 0x0a, 0x08, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x4f, 0x62, 0x6a, 0x22, 0x52, 0x0a, 0x0e, 0x55,
	0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f,
	0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22,
	0xdc, 0x01, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x6e, 0x6f,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65,
	0x4e, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x2d,
	0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x1e, 0x0a,
	0x0c, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x4c, 0x0a,
	0x11, 0x55, 0x73, 0x65, 0x72, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x05, 0x75, 0x73,
	0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22, 0x57, 0x0a, 0x16, 0x55,
	0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x52, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x73, 0x22, 0x2d, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x09, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75,
	0x73, 0x65, 0x72, 0x22, 0xba, 0x02, 0x0a, 0x1a, 0x46, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72,
	0x42, 0x79, 0x55, 0x6e, 0x69, 0x71, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x12, 0x1c, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x88, 0x01, 0x01,
	0x12, 0x1f, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x01, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x19, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x02, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x6e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x03, 0x52, 0x0a, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x4e, 0x6f, 0x88,
	0x01, 0x01, 0x12, 0x27, 0x0a, 0x0d, 0x6c, 0x61, 0x72, 0x6b, 0x5f, 0x75, 0x6e, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x0b, 0x6c, 0x61, 0x72,
	0x6b, 0x55, 0x6e, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x05,
	0x52, 0x08, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x49, 0x44, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x6e, 0x6f,
	0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6c, 0x61, 0x72, 0x6b, 0x5f, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x5f, 0x69, 0x64,
	0x32, 0xe6, 0x05, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x42, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x79, 0x12, 0x0e, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x1a, 0x1b, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x79, 0x12, 0x13,
	0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x3b, 0x0a, 0x10, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4f, 0x66, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12,
	0x0f, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x1a, 0x16, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x11, 0x50, 0x61, 0x67, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x4f, 0x66, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x0f, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x1a, 0x16,
	0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x49, 0x64, 0x73, 0x12, 0x11, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x1a, 0x16, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x42, 0x79, 0x49, 0x64, 0x12, 0x0e, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x1a, 0x11, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x42, 0x79, 0x47, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x49, 0x64, 0x12, 0x10, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x47, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x51, 0x75, 0x65, 0x72, 0x79, 0x1a, 0x09,
	0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x0f, 0x2e, 0x69, 0x61,
	0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x1a, 0x09, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x0a, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x55, 0x73, 0x65, 0x72, 0x12, 0x12, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x3f, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x4c, 0x61,
	0x72, 0x6b, 0x55, 0x6e, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x4c, 0x61, 0x72, 0x6b, 0x55, 0x6e, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x1a,
	0x11, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x36, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x42, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x09, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x6f,
	0x6c, 0x65, 0x1a, 0x16, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x17, 0x46, 0x69,
	0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x55, 0x6e, 0x69, 0x71, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x46, 0x69, 0x6e, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x55, 0x6e, 0x69, 0x71, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x6f, 0x6c,
	0x61, 0x6e, 0x67, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x61, 0x72, 0x6d, 0x6f, 0x6e, 0x79,
	0x2f, 0x69, 0x61, 0x6d, 0x3b, 0x69, 0x61, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_iam_user_proto_rawDescOnce sync.Once
	file_iam_user_proto_rawDescData = file_iam_user_proto_rawDesc
)

func file_iam_user_proto_rawDescGZIP() []byte {
	file_iam_user_proto_rawDescOnce.Do(func() {
		file_iam_user_proto_rawDescData = protoimpl.X.CompressGZIP(file_iam_user_proto_rawDescData)
	})
	return file_iam_user_proto_rawDescData
}

var file_iam_user_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_iam_user_proto_goTypes = []interface{}{
	(*UserParam)(nil),                  // 0: iam.UserParam
	(*UserLarkUnionId)(nil),            // 1: iam.UserLarkUnionId
	(*UserSearchReq)(nil),              // 2: iam.UserSearchReq
	(*GitlabQuery)(nil),                // 3: iam.GitlabQuery
	(*User)(nil),                       // 4: iam.User
	(*UserPreference)(nil),             // 5: iam.UserPreference
	(*UserSearch)(nil),                 // 6: iam.UserSearch
	(*ProjectQuery)(nil),               // 7: iam.ProjectQuery
	(*UserIdsQuery)(nil),               // 8: iam.UserIdsQuery
	(*UserQueryResponse)(nil),          // 9: iam.UserQueryResponse
	(*UserPreferenceResponse)(nil),     // 10: iam.UserPreferenceResponse
	(*UserResponse)(nil),               // 11: iam.UserResponse
	(*FindUserByUniqConditionReq)(nil), // 12: iam.FindUserByUniqConditionReq
	(*Role)(nil),                       // 13: iam.Role
	(*timestamppb.Timestamp)(nil),      // 14: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),              // 15: google.protobuf.Empty
}
var file_iam_user_proto_depIdxs = []int32{
	13, // 0: iam.User.roleObj:type_name -> iam.Role
	14, // 1: iam.User.createdAt:type_name -> google.protobuf.Timestamp
	14, // 2: iam.User.updatedAt:type_name -> google.protobuf.Timestamp
	13, // 3: iam.User.roles:type_name -> iam.Role
	4,  // 4: iam.UserQueryResponse.users:type_name -> iam.User
	5,  // 5: iam.UserPreferenceResponse.UserPreferences:type_name -> iam.UserPreference
	4,  // 6: iam.UserResponse.user:type_name -> iam.User
	0,  // 7: iam.UserService.GetUserPreferenceBy:input_type -> iam.UserParam
	5,  // 8: iam.UserService.UpdateUserPreferenceBy:input_type -> iam.UserPreference
	6,  // 9: iam.UserService.GetUserOfProject:input_type -> iam.UserSearch
	6,  // 10: iam.UserService.PageUserOfProject:input_type -> iam.UserSearch
	8,  // 11: iam.UserService.GetUserListByIds:input_type -> iam.UserIdsQuery
	0,  // 12: iam.UserService.GetUserById:input_type -> iam.UserParam
	3,  // 13: iam.UserService.GetUserByGitlabId:input_type -> iam.GitlabQuery
	6,  // 14: iam.UserService.GetUserByEmail:input_type -> iam.UserSearch
	2,  // 15: iam.UserService.SearchUser:input_type -> iam.UserSearchReq
	1,  // 16: iam.UserService.GetUserByLarkUnionId:input_type -> iam.UserLarkUnionId
	13, // 17: iam.UserService.GetAllUsersByRole:input_type -> iam.Role
	12, // 18: iam.UserService.FindUserByUniqCondition:input_type -> iam.FindUserByUniqConditionReq
	10, // 19: iam.UserService.GetUserPreferenceBy:output_type -> iam.UserPreferenceResponse
	15, // 20: iam.UserService.UpdateUserPreferenceBy:output_type -> google.protobuf.Empty
	9,  // 21: iam.UserService.GetUserOfProject:output_type -> iam.UserQueryResponse
	9,  // 22: iam.UserService.PageUserOfProject:output_type -> iam.UserQueryResponse
	9,  // 23: iam.UserService.GetUserListByIds:output_type -> iam.UserQueryResponse
	11, // 24: iam.UserService.GetUserById:output_type -> iam.UserResponse
	4,  // 25: iam.UserService.GetUserByGitlabId:output_type -> iam.User
	4,  // 26: iam.UserService.GetUserByEmail:output_type -> iam.User
	9,  // 27: iam.UserService.SearchUser:output_type -> iam.UserQueryResponse
	11, // 28: iam.UserService.GetUserByLarkUnionId:output_type -> iam.UserResponse
	9,  // 29: iam.UserService.GetAllUsersByRole:output_type -> iam.UserQueryResponse
	11, // 30: iam.UserService.FindUserByUniqCondition:output_type -> iam.UserResponse
	19, // [19:31] is the sub-list for method output_type
	7,  // [7:19] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_iam_user_proto_init() }
func file_iam_user_proto_init() {
	if File_iam_user_proto != nil {
		return
	}
	file_iam_role_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_iam_user_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_user_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserLarkUnionId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_user_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserSearchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_user_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GitlabQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_user_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_user_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserPreference); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_user_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserSearch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_user_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_user_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIdsQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_user_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_user_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserPreferenceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_user_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_user_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindUserByUniqConditionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_iam_user_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_iam_user_proto_msgTypes[12].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_iam_user_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_iam_user_proto_goTypes,
		DependencyIndexes: file_iam_user_proto_depIdxs,
		MessageInfos:      file_iam_user_proto_msgTypes,
	}.Build()
	File_iam_user_proto = out.File
	file_iam_user_proto_rawDesc = nil
	file_iam_user_proto_goTypes = nil
	file_iam_user_proto_depIdxs = nil
}
