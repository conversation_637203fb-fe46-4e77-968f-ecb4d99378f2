// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: pipeline/template.proto

package pipeline

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UpdateTemplateTaskImageConfigReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateTemplateTaskImageConfigReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTemplateTaskImageConfigReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateTemplateTaskImageConfigReqMultiError, or nil if none found.
func (m *UpdateTemplateTaskImageConfigReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTemplateTaskImageConfigReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ImageId

	// no validation rules for ImageAddress

	if len(errors) > 0 {
		return UpdateTemplateTaskImageConfigReqMultiError(errors)
	}

	return nil
}

// UpdateTemplateTaskImageConfigReqMultiError is an error wrapping multiple
// validation errors returned by
// UpdateTemplateTaskImageConfigReq.ValidateAll() if the designated
// constraints aren't met.
type UpdateTemplateTaskImageConfigReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTemplateTaskImageConfigReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTemplateTaskImageConfigReqMultiError) AllErrors() []error { return m }

// UpdateTemplateTaskImageConfigReqValidationError is the validation error
// returned by UpdateTemplateTaskImageConfigReq.Validate if the designated
// constraints aren't met.
type UpdateTemplateTaskImageConfigReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTemplateTaskImageConfigReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTemplateTaskImageConfigReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTemplateTaskImageConfigReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTemplateTaskImageConfigReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTemplateTaskImageConfigReqValidationError) ErrorName() string {
	return "UpdateTemplateTaskImageConfigReqValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTemplateTaskImageConfigReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTemplateTaskImageConfigReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTemplateTaskImageConfigReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTemplateTaskImageConfigReqValidationError{}

// Validate checks the field values on FindTemplateBYImageIDReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FindTemplateBYImageIDReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FindTemplateBYImageIDReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FindTemplateBYImageIDReqMultiError, or nil if none found.
func (m *FindTemplateBYImageIDReq) ValidateAll() error {
	return m.validate(true)
}

func (m *FindTemplateBYImageIDReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ImageId

	if len(errors) > 0 {
		return FindTemplateBYImageIDReqMultiError(errors)
	}

	return nil
}

// FindTemplateBYImageIDReqMultiError is an error wrapping multiple validation
// errors returned by FindTemplateBYImageIDReq.ValidateAll() if the designated
// constraints aren't met.
type FindTemplateBYImageIDReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FindTemplateBYImageIDReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FindTemplateBYImageIDReqMultiError) AllErrors() []error { return m }

// FindTemplateBYImageIDReqValidationError is the validation error returned by
// FindTemplateBYImageIDReq.Validate if the designated constraints aren't met.
type FindTemplateBYImageIDReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FindTemplateBYImageIDReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FindTemplateBYImageIDReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FindTemplateBYImageIDReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FindTemplateBYImageIDReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FindTemplateBYImageIDReqValidationError) ErrorName() string {
	return "FindTemplateBYImageIDReqValidationError"
}

// Error satisfies the builtin error interface
func (e FindTemplateBYImageIDReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFindTemplateBYImageIDReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FindTemplateBYImageIDReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FindTemplateBYImageIDReqValidationError{}

// Validate checks the field values on PipelineTemplate with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PipelineTemplate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PipelineTemplate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PipelineTemplateMultiError, or nil if none found.
func (m *PipelineTemplate) ValidateAll() error {
	return m.validate(true)
}

func (m *PipelineTemplate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if len(errors) > 0 {
		return PipelineTemplateMultiError(errors)
	}

	return nil
}

// PipelineTemplateMultiError is an error wrapping multiple validation errors
// returned by PipelineTemplate.ValidateAll() if the designated constraints
// aren't met.
type PipelineTemplateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PipelineTemplateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PipelineTemplateMultiError) AllErrors() []error { return m }

// PipelineTemplateValidationError is the validation error returned by
// PipelineTemplate.Validate if the designated constraints aren't met.
type PipelineTemplateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PipelineTemplateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PipelineTemplateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PipelineTemplateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PipelineTemplateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PipelineTemplateValidationError) ErrorName() string { return "PipelineTemplateValidationError" }

// Error satisfies the builtin error interface
func (e PipelineTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPipelineTemplate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PipelineTemplateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PipelineTemplateValidationError{}

// Validate checks the field values on FindTemplateBYImageIDResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FindTemplateBYImageIDResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FindTemplateBYImageIDResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FindTemplateBYImageIDRespMultiError, or nil if none found.
func (m *FindTemplateBYImageIDResp) ValidateAll() error {
	return m.validate(true)
}

func (m *FindTemplateBYImageIDResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTemplates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FindTemplateBYImageIDRespValidationError{
						field:  fmt.Sprintf("Templates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FindTemplateBYImageIDRespValidationError{
						field:  fmt.Sprintf("Templates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FindTemplateBYImageIDRespValidationError{
					field:  fmt.Sprintf("Templates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FindTemplateBYImageIDRespMultiError(errors)
	}

	return nil
}

// FindTemplateBYImageIDRespMultiError is an error wrapping multiple validation
// errors returned by FindTemplateBYImageIDResp.ValidateAll() if the
// designated constraints aren't met.
type FindTemplateBYImageIDRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FindTemplateBYImageIDRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FindTemplateBYImageIDRespMultiError) AllErrors() []error { return m }

// FindTemplateBYImageIDRespValidationError is the validation error returned by
// FindTemplateBYImageIDResp.Validate if the designated constraints aren't met.
type FindTemplateBYImageIDRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FindTemplateBYImageIDRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FindTemplateBYImageIDRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FindTemplateBYImageIDRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FindTemplateBYImageIDRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FindTemplateBYImageIDRespValidationError) ErrorName() string {
	return "FindTemplateBYImageIDRespValidationError"
}

// Error satisfies the builtin error interface
func (e FindTemplateBYImageIDRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFindTemplateBYImageIDResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FindTemplateBYImageIDRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FindTemplateBYImageIDRespValidationError{}
