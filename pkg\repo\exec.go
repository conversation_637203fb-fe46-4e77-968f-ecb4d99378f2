package repo

import (
	"context"
	"os/exec"
	"path"
	"strings"
	"time"

	"golang.ttyuyin.com/harmony/pkg/log"
)

type logWriter struct {
	Info bool // 标识是否输出为 Info 还是 Error
}

func (w *logWriter) Write(p []byte) (n int, err error) {
	if w.Info {
		log.Info(string(p))
	} else {
		log.Error(string(p))
	}
	return len(p), nil
}

func execCommand(ctx context.Context, name string, args ...string) error {
	log.Debugf("execCommand: %s %s", name, strings.Join(args, " "))
	cmd := exec.CommandContext(ctx, name, args...)
	// 重定向命令的标准输出和标准错误输出到glog
	cmd.Stdout = &logWriter{Info: true}
	cmd.Stderr = &logWriter{Info: false}
	return cmd.Run()
}

func execCmdDir(ctx context.Context, name, dirPath string, args ...string) error {
	log.Debugf("execCommand: %s %s", name, strings.Join(args, " "))
	cmd := exec.CommandContext(ctx, name, args...)
	// 重定向命令的标准输出和标准错误输出到glog
	cmd.Stdout = &logWriter{Info: true}
	cmd.Stderr = &logWriter{Info: false}
	cmd.Dir = dirPath
	return cmd.Run()
}

func mkdir(ctx context.Context, dir string) error {
	return execCommand(ctx, "mkdir", "-p", dir)
}

func gitClone(ctx context.Context, url, dir string) error {
	time.Sleep(100 * time.Millisecond)
	return execCommand(ctx, "git", "clone", url, dir)
}

func gitBranch(ctx context.Context, dir, branch string) error {
	return execCommand(ctx, "git", "-C", dir, "checkout", branch)
}

func gitCheckout(ctx context.Context, dir, branch string) error {
	// 默认分支不进行操作，默认拉取就是默认分支
	if branch == defaultBranch {
		return nil
	}
	time.Sleep(50 * time.Millisecond)
	log.Debugf("dir(%s) start git checkout", dir)
	return gitCommand(ctx, dir, "checkout", branch)
}

func gitCommand(ctx context.Context, dir string, args ...string) error {
	targetDir := path.Join(dir, repoExt)
	commandArgs := []string{"--git-dir", targetDir, "--work-tree", dir}
	commandArgs = append(commandArgs, args...)
	return execCommand(ctx, "git", commandArgs...)
}

func gitUpdateIndex(ctx context.Context, dir string) error {
	time.Sleep(50 * time.Millisecond)
	if err := execCommand(ctx, "git", "config", "--global", "--add", "safe.directory", dir); err != nil {
		return err
	}
	time.Sleep(50 * time.Millisecond)
	log.Debugf("dir(%s) start git update-index --refresh", dir)
	return gitCommand(ctx, dir, "update-index", "--refresh")
}

func gitFastCheckout(ctx context.Context, dir, branch string) error {
	log.Debugf("dir(%s) start git fetch", dir)
	err := gitCommand(ctx, dir, "fetch", "--depth=1", "origin", "--update-head-ok", "--force", branch)
	if err != nil {
		return err
	}
	return gitCheckout(ctx, dir, branch)
}

func chownGitUser(ctx context.Context, dir string) error {
	return execCommand(ctx, "chown", "-R", "65532:65532", dir)
}

func copyBranch(ctx context.Context, src, dst string) error {
	// 创建时不创建 dirname
	dstList := strings.Split(dst, "/")
	mkdirDst := strings.Join(dstList[:len(dstList)-1], "/")
	err := execCommand(ctx, "mkdir", "-p", mkdirDst)
	if err != nil {
		return err
	}
	// 复制时把 dirname 也复制进去
	return copyDir(ctx, src, dst)
}

func copyDir(ctx context.Context, src, dst string) error {
	time.Sleep(50 * time.Millisecond)
	return execCommand(ctx, "cp", "-r", src, dst)
}

func gitUpdSubmodule(ctx context.Context, dir string) error {
	time.Sleep(50 * time.Millisecond)
	log.Debugf("dir(%s) start git submodule update", dir)
	if err := execCommand(ctx, "git", "config", "--global", "--add", "safe.directory", dir); err != nil {
		return err
	}
	time.Sleep(50 * time.Millisecond)
	return execCmdDir(ctx, "git", dir, "submodule", "update", "--init", "--recursive")
}
