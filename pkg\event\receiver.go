package event

import (
	"context"
	"github.com/IBM/sarama"
	"golang.ttyuyin.com/harmony/pkg/kafka"
	"golang.ttyuyin.com/harmony/pkg/log"
)

type Closer interface {
	Close(ctx context.Context) error
}

type Receiver interface {
	Closer
	StartReceiver(ctx context.Context, processor kafka.MessageProcessor) error
}

// NewReceiver 创建一个接收器
// 返回 Receiver, error，便于把逻辑再次切换为使用 cloudevents
func NewReceiver(name string, topics []string, brokers []string) (Receiver, error) {
	cfg := kafka.NewConfig(
		kafka.Name(name),
		kafka.Brokers(brokers),
		kafka.ConsumerTopics(topics),
		// 现在 cicd 仓库里代码是用这个版本和 offsetNewest
		kafka.Version(sarama.V2_8_1_0),
		kafka.ConsumerOffsetNewest(),
		kafka.ConsumerGroupID(name),
	)

	return newReceiver(cfg), nil
}

func newReceiver(cfg *kafka.Config) *receiver {
	// 延迟创建，start 的时候才注册处理器
	return &receiver{cfg: cfg}
}

type receiver struct {
	cfg      *kafka.Config
	consumer *kafka.Consumer
}

func (c *receiver) Close(ctx context.Context) (err error) {
	log.Infof("stop receiver")
	if c.consumer == nil {
		return nil
	}
	c.consumer.Stop()
	return
}

func (c *receiver) StartReceiver(ctx context.Context, processor kafka.MessageProcessor) error {
	consumer, err := kafka.NewConsumer(ctx, c.cfg, processor)
	if err != nil {
		return err
	}
	c.consumer = consumer
	c.consumer.Start()
	log.Infof("start receiver")
	return nil
}
