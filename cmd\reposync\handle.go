package main

import (
	"context"
	"encoding/json"
	"golang.ttyuyin.com/harmony/csi-driver/pkg/repo"
	"golang.ttyuyin.com/harmony/csi-driver/pkg/task"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"syscall"
	"time"

	"golang.ttyuyin.com/harmony/csi-driver/pkg/event"
	eventpb "golang.ttyuyin.com/harmony/csi-driver/protocol/event"
	"golang.ttyuyin.com/harmony/pkg/kafka"
	"golang.ttyuyin.com/harmony/pkg/log"
)

func handleReleaseDisk(msg *kafka.ConsumerMessage) (error, bool) {
	eventData := event.Event{}
	err := json.Unmarshal(msg.Value, &eventData)
	if err != nil {
		log.Errorf("handleReleasePV unmarshal err: %v", err)
		return err, false
	}
	eventData.EventTime = msg.Timestamp

	// 只接收流水线实例事件
	if eventData.Type() == "cicd.pipeline.pipelinerun" {
		log.Infof("接收到流水线实例事件[%s]，开始进行处理", eventData.ID())
		return handleDisk(eventData)
	}
	return nil, true
}

func handleDisk(e event.Event) (error, bool) {
	var prEvent eventpb.PipelineRunEvent
	if err := e.DataAs(&prEvent); err != nil {
		log.Errorf("事件[%s]解析数据结构体，发生异常: %v", e.ID(), err)
		// todo notify
		return err, false
	}

	if prEvent.GetStatus() != "SUCCESSFUL" {
		log.Debugf("事件[%s]触发，流水线实例[%d]状态为[%s]，不进行处理", e.ID(), prEvent.GetId(), prEvent.GetStatus())
		return nil, true
	}

	needRemoveDir := buildPVPath(&prEvent)
	mergedDir := filepath.Join(needRemoveDir, "merged")
	time.Sleep(1 * time.Second)

	// 清除磁盘，这里只处理umount，如果非umount，先不处理
	if !canRemove(mergedDir) {
		log.Warnf("事件[%s]触发，文件夹[%s]不能被删除，可能是挂载点，或者是其他进程正在使用", e.ID(), mergedDir)
		return nil, true
	}

	log.Infof("事件[%s]触发，开始清理文件夹[%s]", e.ID(), needRemoveDir)
	err := os.RemoveAll(needRemoveDir)
	if err != nil {
		log.Errorf("事件[%s]触发，删除文件夹[%s]失败，错误原因：%v", e.ID(), needRemoveDir, err)
		// todo notify
		return err, false
	} else {
		log.Errorf("事件[%s]触发，成功删除文件夹[%s]", e.ID(), needRemoveDir)
	}
	return nil, true
}

func buildPVPath(e *eventpb.PipelineRunEvent) string {
	env := cfg.Env
	rootDir := cfg.RepoRootPath
	pID := e.GetPipelineId()
	br := e.GetBuildNumber()
	pIDStr := strconv.FormatInt(pID, 10)
	brStr := strconv.FormatInt(br, 10)
	needRemoveDir := filepath.Join(rootDir, env, pIDStr, brStr)
	return needRemoveDir
}

func canRemove(path string) bool {
	fileInfo, err := os.Stat(path)
	if err != nil {
		if os.IsNotExist(err) {
			log.Infof("文件夹[%s]不存在，无需操作", path)
			return false
		}
		log.Errorf("无法获取文件信息：%v", err)
		return false
	}

	// mount point
	rootStat, err := os.Stat("/")
	if err != nil {
		log.Errorf("无法获取根目录信息：%v", err)
		return false
	}

	return fileInfo.Sys().(*syscall.Stat_t).Dev == rootStat.Sys().(*syscall.Stat_t).Dev
}

func handleRepoFirstLoad(msg *kafka.ConsumerMessage) (error, bool) {
	eventData := event.Event{}
	err := json.Unmarshal(msg.Value, &eventData)
	if err != nil {
		log.Errorf("handleRepoFirstLoad unmarshal err: %v", err)
		return err, false
	}
	eventData.EventTime = msg.Timestamp

	// 只处理 app 新建事件
	if eventData.Type() != "cicd.pipeline.app.create" {
		return nil, true
	}

	log.Infof("接收到 app 新建事件[%s]，开始进行处理", eventData.ID())
	var appEvent mockAppEvent
	if err := eventData.DataAs(&appEvent); err != nil {
		log.Errorf("事件[%s]解析数据结构体，发生异常: %v", eventData.ID(), err)
		// todo notify
		return err, false
	}

	rootPath := path.Join(cfg.LowerDirRootPath, cfg.LowerDirVersion, task.GetCurrentDateString())
	repository := repo.New(appEvent.URL, rootPath)
	repository.Clone(context.Background())
	return nil, true
}

// app 还没有事件，先定义一波，后面再换成事件的
type mockAppEvent struct {
	URL string
}
