package bootstrap

import (
	"os"
	"path"
	"path/filepath"
	"runtime"
	"strings"
)

// 获取当前执行文件绝对路径
func getCurrentAbsPathByExecutable() (string, error) {
	exePath, err := os.Executable()
	if err != nil {
		return "", err
	}
	return filepath.EvalSymlinks(filepath.Dir(exePath))
}

// 获取当前执行文件绝对路径（go run）
func getCurrentAbsPathByCaller() string {
	var abPath string
	_, filename, _, ok := runtime.Caller(2)
	if ok {
		abPath = path.Dir(filename)
	}
	return abPath
}

// GetCurrentAbsPath 获取当前执行文件绝对路径
func GetCurrentAbsPath() string {
	dir, _ := getCurrentAbsPathByExecutable()

	tmpDir, _ := filepath.EvalSymlinks(os.TempDir())
	// go run or goland run
	if strings.Contains(dir, tmpDir) || strings.Contains(dir, "JetBrains") {
		return getCurrentAbsPathByCaller()
	}

	return dir
}
