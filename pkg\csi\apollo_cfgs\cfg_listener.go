package apollo_cfgs

import (
	"fmt"
	"os"
	"path"

	"github.com/apolloconfig/agollo/v4"
	"github.com/apolloconfig/agollo/v4/env/config"
	"github.com/apolloconfig/agollo/v4/storage"
	"k8s.io/klog/v2"
)

const (
	SerCfgPath    = "ser"
	GlobalCfgPath = "prj"
)

// 节点上某配置的 监听者
type nodeCfgListener struct {
	client      agollo.Client
	apolloNS    *config.AppConfig
	hostPath    string
	cfgFileName string
}

func (l *nodeCfgListener) Init(dyCfg *config.AppConfig, hostPath string, cfg DyCfg) (err error) {
	l.apolloNS = dyCfg
	client, err := agollo.StartWithConfig(func() (*config.AppConfig, error) {
		return l.apolloNS, nil
	})
	if err != nil {
		err = fmt.Errorf("Start Apollo Config 出错，请确保配置文件 %s,有生效的版本。 Err:%v", cfg.CfgFile, err)
		return
	}
	l.client = client

	l.cfgFileName = cfg.CfgFile
	l.hostPath = path.Join(hostPath, SerCfgPath)
	if cfg.IsGlobal {
		l.hostPath = path.Join(hostPath, GlobalCfgPath)
	}
	cfgFilePath := path.Join(l.hostPath, l.cfgFileName)
	makesureFileExist(cfgFilePath)

	apolloCfg := client.GetConfig(dyCfg.NamespaceName)
	if apolloCfg == nil {
		klog.Errorf("agollo client GetConfig Error: %s", dyCfg.NamespaceName)
		err = fmt.Errorf("agollo client GetConfig Error: %s", dyCfg.NamespaceName)
		return
	}
	// 等待配置获取完成
	ct := apolloCfg.GetStringValue(FileCfgKey, "")
	if ct == "" {
		err = fmt.Errorf("Get Config Timeout ! 请确保配置文件 %s,有生效的版本", cfg.CfgFile)
		return
	}

	err = os.WriteFile(cfgFilePath, []byte(ct), 0755)
	if err != nil {
		return
	}
	klog.Infof("WriteFile Config Success: %s", cfgFilePath)

	client.AddChangeListener(l)

	return
}

func (l *nodeCfgListener) Close() {
	l.client.RemoveChangeListener(l)
	l.client.Close()
	err := os.RemoveAll(l.hostPath)
	if err != nil {
		klog.Errorf("Listener Close Remove %s Error: %v", err, l.hostPath)
	}
	l.client = nil
	l.apolloNS = nil
}

// OnChange 增加变更监控
func (l *nodeCfgListener) OnChange(event *storage.ChangeEvent) {
	klog.Infof("OnChange: Apollo NS %s ,FilePath %s", event.Namespace, l.cfgFileName)
	cfgFilePath := path.Join(l.hostPath, l.cfgFileName)

	for k, v := range event.Changes {
		if k == FileCfgKey && v.ChangeType == storage.MODIFIED {
			file, _ := v.NewValue.(string)
			err := os.WriteFile(cfgFilePath, []byte(file), 0755)
			if err != nil {
				klog.Errorf("OnChange WriteFile Error: %v", err)
			}
		}
	}
}

// OnNewestChange 监控最新变更
func (l *nodeCfgListener) OnNewestChange(event *storage.FullChangeEvent) {
	// fmt.Println("OnNewestChange:", event.Namespace, event.NotificationID)
	// for k, v := range event.Changes {
	// 	fmt.Println("key:", k)
	// 	fmt.Println("OnNewestChange:", v)
	// }
}

func makesureFileExist(filepath string) {
	dir := path.Dir(filepath)

	var err error

	_, err = os.Stat(dir)
	if err != nil {
		if !os.IsNotExist(err) {
			panic(err.Error())
		}

		if err = os.MkdirAll(dir, os.ModeDir|0755); err != nil {
			panic(err.Error())
		}
	}

	_, err = os.Stat(filepath)
	if err != nil {
		if !os.IsNotExist(err) {
			panic(err.Error())
		}

		file, err := os.Create(filepath)
		if err != nil {
			panic(err.Error())
		}

		file.Close()
	}
}
