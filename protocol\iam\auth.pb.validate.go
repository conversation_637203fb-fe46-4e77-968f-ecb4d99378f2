// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: iam/auth.proto

package iam

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetOwnUserinfoReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetOwnUserinfoReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOwnUserinfoReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOwnUserinfoReqMultiError, or nil if none found.
func (m *GetOwnUserinfoReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOwnUserinfoReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetOwnUserinfoReqMultiError(errors)
	}

	return nil
}

// GetOwnUserinfoReqMultiError is an error wrapping multiple validation errors
// returned by GetOwnUserinfoReq.ValidateAll() if the designated constraints
// aren't met.
type GetOwnUserinfoReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOwnUserinfoReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOwnUserinfoReqMultiError) AllErrors() []error { return m }

// GetOwnUserinfoReqValidationError is the validation error returned by
// GetOwnUserinfoReq.Validate if the designated constraints aren't met.
type GetOwnUserinfoReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOwnUserinfoReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOwnUserinfoReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOwnUserinfoReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOwnUserinfoReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOwnUserinfoReqValidationError) ErrorName() string {
	return "GetOwnUserinfoReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetOwnUserinfoReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOwnUserinfoReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOwnUserinfoReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOwnUserinfoReqValidationError{}

// Validate checks the field values on GetOwnUserinfoResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOwnUserinfoResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOwnUserinfoResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOwnUserinfoRespMultiError, or nil if none found.
func (m *GetOwnUserinfoResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOwnUserinfoResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUserInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOwnUserinfoRespValidationError{
					field:  "UserInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOwnUserinfoRespValidationError{
					field:  "UserInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOwnUserinfoRespValidationError{
				field:  "UserInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentProject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOwnUserinfoRespValidationError{
					field:  "CurrentProject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOwnUserinfoRespValidationError{
					field:  "CurrentProject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentProject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOwnUserinfoRespValidationError{
				field:  "CurrentProject",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOwnUserinfoRespMultiError(errors)
	}

	return nil
}

// GetOwnUserinfoRespMultiError is an error wrapping multiple validation errors
// returned by GetOwnUserinfoResp.ValidateAll() if the designated constraints
// aren't met.
type GetOwnUserinfoRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOwnUserinfoRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOwnUserinfoRespMultiError) AllErrors() []error { return m }

// GetOwnUserinfoRespValidationError is the validation error returned by
// GetOwnUserinfoResp.Validate if the designated constraints aren't met.
type GetOwnUserinfoRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOwnUserinfoRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOwnUserinfoRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOwnUserinfoRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOwnUserinfoRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOwnUserinfoRespValidationError) ErrorName() string {
	return "GetOwnUserinfoRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetOwnUserinfoRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOwnUserinfoResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOwnUserinfoRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOwnUserinfoRespValidationError{}

// Validate checks the field values on LoginReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoginReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoginReqMultiError, or nil
// if none found.
func (m *LoginReq) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Ticket

	// no validation rules for Target

	if len(errors) > 0 {
		return LoginReqMultiError(errors)
	}

	return nil
}

// LoginReqMultiError is an error wrapping multiple validation errors returned
// by LoginReq.ValidateAll() if the designated constraints aren't met.
type LoginReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginReqMultiError) AllErrors() []error { return m }

// LoginReqValidationError is the validation error returned by
// LoginReq.Validate if the designated constraints aren't met.
type LoginReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginReqValidationError) ErrorName() string { return "LoginReqValidationError" }

// Error satisfies the builtin error interface
func (e LoginReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginReqValidationError{}

// Validate checks the field values on LoginResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoginResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoginRespMultiError, or nil
// if none found.
func (m *LoginResp) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	// no validation rules for Expires

	if len(errors) > 0 {
		return LoginRespMultiError(errors)
	}

	return nil
}

// LoginRespMultiError is an error wrapping multiple validation errors returned
// by LoginResp.ValidateAll() if the designated constraints aren't met.
type LoginRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginRespMultiError) AllErrors() []error { return m }

// LoginRespValidationError is the validation error returned by
// LoginResp.Validate if the designated constraints aren't met.
type LoginRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginRespValidationError) ErrorName() string { return "LoginRespValidationError" }

// Error satisfies the builtin error interface
func (e LoginRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginRespValidationError{}

// Validate checks the field values on LogoutReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LogoutReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogoutReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LogoutReqMultiError, or nil
// if none found.
func (m *LogoutReq) ValidateAll() error {
	return m.validate(true)
}

func (m *LogoutReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	if len(errors) > 0 {
		return LogoutReqMultiError(errors)
	}

	return nil
}

// LogoutReqMultiError is an error wrapping multiple validation errors returned
// by LogoutReq.ValidateAll() if the designated constraints aren't met.
type LogoutReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogoutReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogoutReqMultiError) AllErrors() []error { return m }

// LogoutReqValidationError is the validation error returned by
// LogoutReq.Validate if the designated constraints aren't met.
type LogoutReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogoutReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogoutReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogoutReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogoutReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogoutReqValidationError) ErrorName() string { return "LogoutReqValidationError" }

// Error satisfies the builtin error interface
func (e LogoutReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogoutReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogoutReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogoutReqValidationError{}

// Validate checks the field values on LogoutResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LogoutResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogoutResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LogoutRespMultiError, or
// nil if none found.
func (m *LogoutResp) ValidateAll() error {
	return m.validate(true)
}

func (m *LogoutResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	if len(errors) > 0 {
		return LogoutRespMultiError(errors)
	}

	return nil
}

// LogoutRespMultiError is an error wrapping multiple validation errors
// returned by LogoutResp.ValidateAll() if the designated constraints aren't met.
type LogoutRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogoutRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogoutRespMultiError) AllErrors() []error { return m }

// LogoutRespValidationError is the validation error returned by
// LogoutResp.Validate if the designated constraints aren't met.
type LogoutRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogoutRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogoutRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogoutRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogoutRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogoutRespValidationError) ErrorName() string { return "LogoutRespValidationError" }

// Error satisfies the builtin error interface
func (e LogoutRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogoutResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogoutRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogoutRespValidationError{}

// Validate checks the field values on CheckTokenIsBanReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckTokenIsBanReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckTokenIsBanReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckTokenIsBanReqMultiError, or nil if none found.
func (m *CheckTokenIsBanReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckTokenIsBanReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	if len(errors) > 0 {
		return CheckTokenIsBanReqMultiError(errors)
	}

	return nil
}

// CheckTokenIsBanReqMultiError is an error wrapping multiple validation errors
// returned by CheckTokenIsBanReq.ValidateAll() if the designated constraints
// aren't met.
type CheckTokenIsBanReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckTokenIsBanReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckTokenIsBanReqMultiError) AllErrors() []error { return m }

// CheckTokenIsBanReqValidationError is the validation error returned by
// CheckTokenIsBanReq.Validate if the designated constraints aren't met.
type CheckTokenIsBanReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckTokenIsBanReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckTokenIsBanReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckTokenIsBanReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckTokenIsBanReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckTokenIsBanReqValidationError) ErrorName() string {
	return "CheckTokenIsBanReqValidationError"
}

// Error satisfies the builtin error interface
func (e CheckTokenIsBanReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckTokenIsBanReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckTokenIsBanReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckTokenIsBanReqValidationError{}

// Validate checks the field values on CheckTokenIsBanResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckTokenIsBanResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckTokenIsBanResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckTokenIsBanRespMultiError, or nil if none found.
func (m *CheckTokenIsBanResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckTokenIsBanResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsBan

	if len(errors) > 0 {
		return CheckTokenIsBanRespMultiError(errors)
	}

	return nil
}

// CheckTokenIsBanRespMultiError is an error wrapping multiple validation
// errors returned by CheckTokenIsBanResp.ValidateAll() if the designated
// constraints aren't met.
type CheckTokenIsBanRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckTokenIsBanRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckTokenIsBanRespMultiError) AllErrors() []error { return m }

// CheckTokenIsBanRespValidationError is the validation error returned by
// CheckTokenIsBanResp.Validate if the designated constraints aren't met.
type CheckTokenIsBanRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckTokenIsBanRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckTokenIsBanRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckTokenIsBanRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckTokenIsBanRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckTokenIsBanRespValidationError) ErrorName() string {
	return "CheckTokenIsBanRespValidationError"
}

// Error satisfies the builtin error interface
func (e CheckTokenIsBanRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckTokenIsBanResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckTokenIsBanRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckTokenIsBanRespValidationError{}

// Validate checks the field values on GetOwnUserinfoRespSelectProject with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOwnUserinfoRespSelectProject) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOwnUserinfoRespSelectProject with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetOwnUserinfoRespSelectProjectMultiError, or nil if none found.
func (m *GetOwnUserinfoRespSelectProject) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOwnUserinfoRespSelectProject) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Role

	if len(errors) > 0 {
		return GetOwnUserinfoRespSelectProjectMultiError(errors)
	}

	return nil
}

// GetOwnUserinfoRespSelectProjectMultiError is an error wrapping multiple
// validation errors returned by GetOwnUserinfoRespSelectProject.ValidateAll()
// if the designated constraints aren't met.
type GetOwnUserinfoRespSelectProjectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOwnUserinfoRespSelectProjectMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOwnUserinfoRespSelectProjectMultiError) AllErrors() []error { return m }

// GetOwnUserinfoRespSelectProjectValidationError is the validation error
// returned by GetOwnUserinfoRespSelectProject.Validate if the designated
// constraints aren't met.
type GetOwnUserinfoRespSelectProjectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOwnUserinfoRespSelectProjectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOwnUserinfoRespSelectProjectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOwnUserinfoRespSelectProjectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOwnUserinfoRespSelectProjectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOwnUserinfoRespSelectProjectValidationError) ErrorName() string {
	return "GetOwnUserinfoRespSelectProjectValidationError"
}

// Error satisfies the builtin error interface
func (e GetOwnUserinfoRespSelectProjectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOwnUserinfoRespSelectProject.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOwnUserinfoRespSelectProjectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOwnUserinfoRespSelectProjectValidationError{}

// Validate checks the field values on GetOwnUserinfoRespOwnUserInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOwnUserinfoRespOwnUserInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOwnUserinfoRespOwnUserInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetOwnUserinfoRespOwnUserInfoMultiError, or nil if none found.
func (m *GetOwnUserinfoRespOwnUserInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOwnUserinfoRespOwnUserInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Username

	// no validation rules for ChineseName

	// no validation rules for Email

	// no validation rules for EmployeeNo

	// no validation rules for Role

	if len(errors) > 0 {
		return GetOwnUserinfoRespOwnUserInfoMultiError(errors)
	}

	return nil
}

// GetOwnUserinfoRespOwnUserInfoMultiError is an error wrapping multiple
// validation errors returned by GetOwnUserinfoRespOwnUserInfo.ValidateAll()
// if the designated constraints aren't met.
type GetOwnUserinfoRespOwnUserInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOwnUserinfoRespOwnUserInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOwnUserinfoRespOwnUserInfoMultiError) AllErrors() []error { return m }

// GetOwnUserinfoRespOwnUserInfoValidationError is the validation error
// returned by GetOwnUserinfoRespOwnUserInfo.Validate if the designated
// constraints aren't met.
type GetOwnUserinfoRespOwnUserInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOwnUserinfoRespOwnUserInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOwnUserinfoRespOwnUserInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOwnUserinfoRespOwnUserInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOwnUserinfoRespOwnUserInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOwnUserinfoRespOwnUserInfoValidationError) ErrorName() string {
	return "GetOwnUserinfoRespOwnUserInfoValidationError"
}

// Error satisfies the builtin error interface
func (e GetOwnUserinfoRespOwnUserInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOwnUserinfoRespOwnUserInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOwnUserinfoRespOwnUserInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOwnUserinfoRespOwnUserInfoValidationError{}
