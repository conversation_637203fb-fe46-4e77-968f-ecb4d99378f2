package main

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"regexp"
	"strings"

	"golang.ttyuyin.com/harmony/csi-driver/pkg/volume"

	"golang.org/x/exp/slog"
)

//CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o app deploy/main.go

func main() {
	ReExport()
}

func ReExport() (err error) {
	body, err := os.OpenFile("csi-overlay.ini", os.O_RDONLY, 0666)
	if err != nil {
		return
	}
	defer body.Close()
	iii := 0
	reader := bufio.NewReader(body)
	for {
		var line []byte
		line, _, err = reader.ReadLine()
		if err != nil {
			if err == io.EOF {
				break
			} else {
				return
			}
		}
		iii += 1

		megDir, err := reVolume(string(line), iii)
		if err != nil {
			slog.Info("reVolume ", "Line", iii, "Err", err.Error())
			continue
		}
		slog.Info("✨✨✨", "Line", iii, "->", megDir)
	}
	return
}

var regLowerdir, _ = regexp.Compile("lowerdir=(.*),upperdir=")
var regUpperdir, _ = regexp.Compile("upperdir=(.*),workdir=")
var regWorkdir, _ = regexp.Compile("workdir=(.*),index=")

func reVolume(lineStr string, line int) (mergeddir string, err error) {
	// overlay on /data/csi/prod/149975/42/merged type overlay (rw,relatime,lowerdir=/gitlab-repo/gamegroup/dafuweng_server/2024-01,upperdir=/data/csi/prod/149975/42/upperdir,workdir=/data/csi/prod/149975/42/workdir,index=on,nfs_export=on)
	// /data/csi-v2/prod/335428/33/merged type overlay (rw,relatime,lowerdir=/gitlab-repo/v2/2025-02/x-project/server/avoice/avoice-business/dev,upperdir=/data/csi-v2/prod/335428/33/upperdir,workdir=/data/csi-v2/prod/335428/33/workdir,index=on,nfs_export=on)
	lowerdir, upperdir, workdir := "", "", ""

	lowerdirs := regLowerdir.FindStringSubmatch(lineStr)
	if len(lowerdirs) != 2 {
		err = fmt.Errorf("lowerdirs Err by %s", lineStr)
		return
	}
	lowerdir = lowerdirs[1]

	workdirs := regWorkdir.FindStringSubmatch(lineStr)
	if len(workdirs) != 2 {
		err = fmt.Errorf("workdirs Err by %s", lineStr)
		return
	}
	workdir = workdirs[1]

	upperdirs := regUpperdir.FindStringSubmatch(lineStr)
	if len(upperdirs) != 2 {
		err = fmt.Errorf("upperdirs Err by %s", lineStr)
		return
	}
	upperdir = upperdirs[1]
	arr := strings.Split(upperdir, "/")
	if len(arr) != 7 {
		err = fmt.Errorf("upperdirs Split Err by %s", upperdir)
		return
	}
	pipelineId := arr[4]
	buildNumber := arr[5]
	mergeddir = fmt.Sprintf("/data/csi-v2/prod/%s/%s/merged", pipelineId, buildNumber)

	// slog.Info("(⊙_⊙)?", "lowerdir", lowerdir)
	// slog.Info("(⊙_⊙)?", "upperdir", upperdir)
	// slog.Info("(⊙_⊙)?", "workdir", workdir)

	// slog.Info("(⊙_⊙)?", "mergeddir", mergeddir)

	// slog.Info("creating overlayfs")
	volume := volume.NewVolume(lowerdir, upperdir, workdir, mergeddir)
	err = volume.Mount()
	if err != nil {
		slog.Error("creating overlayfs", "Err", err)
		return
	}

	// slog.Info("export overlayfs")
	err = volume.Export(pipelineId + buildNumber)
	if err != nil {
		slog.Error("export overlayfs", "Err", err)
		return
	}

	return
}
