// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        (unknown)
// source: rdp/reposync/v1/repo_sync.proto

package reposync

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Version is the version of the repo-sync protocol.
type Version int32

const (
	// VERSION_UNKNOWN is the default version of the repo-sync protocol.
	Version_VERSION_UNKNOWN Version = 0
	// VERSION_V1 is the first version of the repo-sync protocol.
	Version_VERSION_V1 Version = 1
	// VERSION_V2 is the second version of the repo-sync protocol.
	Version_VERSION_V2 Version = 2
)

// Enum value maps for Version.
var (
	Version_name = map[int32]string{
		0: "VERSION_UNKNOWN",
		1: "VERSION_V1",
		2: "VERSION_V2",
	}
	Version_value = map[string]int32{
		"VERSION_UNKNOWN": 0,
		"VERSION_V1":      1,
		"VERSION_V2":      2,
	}
)

func (x Version) Enum() *Version {
	p := new(Version)
	*p = x
	return p
}

func (x Version) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Version) Descriptor() protoreflect.EnumDescriptor {
	return file_rdp_reposync_v1_repo_sync_proto_enumTypes[0].Descriptor()
}

func (Version) Type() protoreflect.EnumType {
	return &file_rdp_reposync_v1_repo_sync_proto_enumTypes[0]
}

func (x Version) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Version.Descriptor instead.
func (Version) EnumDescriptor() ([]byte, []int) {
	return file_rdp_reposync_v1_repo_sync_proto_rawDescGZIP(), []int{0}
}

// CreateLowerDirRequest is the request of CreateLowerDir.
type CreateLowerDirRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// full_path is the full path.
	FullPath string `protobuf:"bytes,1,opt,name=full_path,json=fullPath,proto3" json:"full_path,omitempty"`
	// version is the version of the repo-sync protocol.
	Version Version `protobuf:"varint,2,opt,name=version,proto3,enum=rdp.reposync.v1.Version" json:"version,omitempty"`
	// repo_addr is the address of the repo.
	RepoAddr string `protobuf:"bytes,3,opt,name=repo_addr,json=repoAddr,proto3" json:"repo_addr,omitempty"`
	// branch is the branch of the repo.
	Branch string `protobuf:"bytes,4,opt,name=branch,proto3" json:"branch,omitempty"`
}

func (x *CreateLowerDirRequest) Reset() {
	*x = CreateLowerDirRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rdp_reposync_v1_repo_sync_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLowerDirRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLowerDirRequest) ProtoMessage() {}

func (x *CreateLowerDirRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rdp_reposync_v1_repo_sync_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLowerDirRequest.ProtoReflect.Descriptor instead.
func (*CreateLowerDirRequest) Descriptor() ([]byte, []int) {
	return file_rdp_reposync_v1_repo_sync_proto_rawDescGZIP(), []int{0}
}

func (x *CreateLowerDirRequest) GetFullPath() string {
	if x != nil {
		return x.FullPath
	}
	return ""
}

func (x *CreateLowerDirRequest) GetVersion() Version {
	if x != nil {
		return x.Version
	}
	return Version_VERSION_UNKNOWN
}

func (x *CreateLowerDirRequest) GetRepoAddr() string {
	if x != nil {
		return x.RepoAddr
	}
	return ""
}

func (x *CreateLowerDirRequest) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

// CreateLowerDirResponse is the response of CreateLowerDir.
type CreateLowerDirResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateLowerDirResponse) Reset() {
	*x = CreateLowerDirResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rdp_reposync_v1_repo_sync_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLowerDirResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLowerDirResponse) ProtoMessage() {}

func (x *CreateLowerDirResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rdp_reposync_v1_repo_sync_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLowerDirResponse.ProtoReflect.Descriptor instead.
func (*CreateLowerDirResponse) Descriptor() ([]byte, []int) {
	return file_rdp_reposync_v1_repo_sync_proto_rawDescGZIP(), []int{1}
}

var File_rdp_reposync_v1_repo_sync_proto protoreflect.FileDescriptor

var file_rdp_reposync_v1_repo_sync_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x72, 0x64, 0x70, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x79, 0x6e, 0x63, 0x2f, 0x76,
	0x31, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0f, 0x72, 0x64, 0x70, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x79, 0x6e, 0x63, 0x2e,
	0x76, 0x31, 0x22, 0x9d, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x77,
	0x65, 0x72, 0x44, 0x69, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x12, 0x32, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72, 0x64, 0x70,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a,
	0x09, 0x72, 0x65, 0x70, 0x6f, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x41, 0x64, 0x64, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x72,
	0x61, 0x6e, 0x63, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e,
	0x63, 0x68, 0x22, 0x18, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x77, 0x65,
	0x72, 0x44, 0x69, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2a, 0x3e, 0x0a, 0x07,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x13, 0x0a, 0x0f, 0x56, 0x45, 0x52, 0x53, 0x49,
	0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a,
	0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x31, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a,
	0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x32, 0x10, 0x02, 0x32, 0x76, 0x0a, 0x0f,
	0x52, 0x65, 0x70, 0x6f, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x63, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x69,
	0x72, 0x12, 0x26, 0x2e, 0x72, 0x64, 0x70, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x79, 0x6e, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x77, 0x65, 0x72, 0x44,
	0x69, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x72, 0x64, 0x70, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4c, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x69, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x42, 0x3e, 0x5a, 0x3c, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x74,
	0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x61, 0x72, 0x6d, 0x6f, 0x6e, 0x79, 0x2f, 0x72, 0x64, 0x70, 0x2f,
	0x72, 0x65, 0x70, 0x6f, 0x73, 0x79, 0x6e, 0x63, 0x2f, 0x76, 0x31, 0x3b, 0x72, 0x65, 0x70, 0x6f,
	0x73, 0x79, 0x6e, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_rdp_reposync_v1_repo_sync_proto_rawDescOnce sync.Once
	file_rdp_reposync_v1_repo_sync_proto_rawDescData = file_rdp_reposync_v1_repo_sync_proto_rawDesc
)

func file_rdp_reposync_v1_repo_sync_proto_rawDescGZIP() []byte {
	file_rdp_reposync_v1_repo_sync_proto_rawDescOnce.Do(func() {
		file_rdp_reposync_v1_repo_sync_proto_rawDescData = protoimpl.X.CompressGZIP(file_rdp_reposync_v1_repo_sync_proto_rawDescData)
	})
	return file_rdp_reposync_v1_repo_sync_proto_rawDescData
}

var file_rdp_reposync_v1_repo_sync_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_rdp_reposync_v1_repo_sync_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_rdp_reposync_v1_repo_sync_proto_goTypes = []interface{}{
	(Version)(0),                   // 0: rdp.reposync.v1.Version
	(*CreateLowerDirRequest)(nil),  // 1: rdp.reposync.v1.CreateLowerDirRequest
	(*CreateLowerDirResponse)(nil), // 2: rdp.reposync.v1.CreateLowerDirResponse
}
var file_rdp_reposync_v1_repo_sync_proto_depIdxs = []int32{
	0, // 0: rdp.reposync.v1.CreateLowerDirRequest.version:type_name -> rdp.reposync.v1.Version
	1, // 1: rdp.reposync.v1.RepoSyncService.CreateLowerDir:input_type -> rdp.reposync.v1.CreateLowerDirRequest
	2, // 2: rdp.reposync.v1.RepoSyncService.CreateLowerDir:output_type -> rdp.reposync.v1.CreateLowerDirResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_rdp_reposync_v1_repo_sync_proto_init() }
func file_rdp_reposync_v1_repo_sync_proto_init() {
	if File_rdp_reposync_v1_repo_sync_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_rdp_reposync_v1_repo_sync_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLowerDirRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_rdp_reposync_v1_repo_sync_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLowerDirResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_rdp_reposync_v1_repo_sync_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rdp_reposync_v1_repo_sync_proto_goTypes,
		DependencyIndexes: file_rdp_reposync_v1_repo_sync_proto_depIdxs,
		EnumInfos:         file_rdp_reposync_v1_repo_sync_proto_enumTypes,
		MessageInfos:      file_rdp_reposync_v1_repo_sync_proto_msgTypes,
	}.Build()
	File_rdp_reposync_v1_repo_sync_proto = out.File
	file_rdp_reposync_v1_repo_sync_proto_rawDesc = nil
	file_rdp_reposync_v1_repo_sync_proto_goTypes = nil
	file_rdp_reposync_v1_repo_sync_proto_depIdxs = nil
}
