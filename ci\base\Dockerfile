
FROM cr.ttyuyin.com/devops/alpine:latest as RUNNER

RUN apk --update --no-cache add bash nfs-utils supervisor libcap&& \
    \
    # remove the default config files
    rm -v /etc/idmapd.conf /etc/exports

# http://wiki.linux-nfs.org/wiki/index.php/Nfsv4_configuration
RUN mkdir -p /var/lib/nfs/rpc_pipefs                                                     && \
    mkdir -p /var/lib/nfs/v4recovery                                                     && \
    mkdir -p /data/yunwei/log/repo-sync                                                  && \
    mkdir -p /ttyuyin                                                                    && \
    touch /ttyuyin/.env                                                                  && \
    touch /etc/exports                                                                   && \
    echo "rpc_pipefs  /var/lib/nfs/rpc_pipefs  rpc_pipefs  defaults  0  0" >> /etc/fstab && \
    echo "nfsd        /proc/fs/nfsd            nfsd        defaults  0  0" >> /etc/fstab



ENV USER_ID=65532
ENV GROUP_ID=65532
ENV GROUP_NAME=git
ENV USER_NAME=git

RUN addgroup -g $GROUP_ID $GROUP_NAME && adduser --shell /bin/bash --disabled-password --uid $USER_ID --ingroup $GROUP_NAME $USER_NAME

