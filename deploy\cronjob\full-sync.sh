#!/bin/bash

set -e
set -o pipefail

DATABASE_HOST=${DB_HOST:-**********}
DATABASE_NAME=${DB_NAME:-app}
DATABASE_USER=${DB_USER:-cicd_ro}
DATABASE_PASS=${DB_PASS:-ttyuyin123}
DATABASE_PORT=${DB_PORT:-3306}
MAX_PROCESSES=${MAX_PROCESSES:-4}

SQL_QUERY="select distinct(repo_addr) from app;"

readarray -t REPO_LIST <<<"$(mysql -h$DATABASE_HOST -u$DATABASE_USER -p$DATABASE_PASS $DATABASE_NAME -N -s -e "$SQL_QUERY")"

# Get next month on busybox date
YEAR=$(date +%Y)
MONTH=$(date +%m)
NEXT_MONTH=$((10#$MONTH + 1))
NEXT_YEAR=$YEAR
if [ $NEXT_MONTH -eq 13 ]; then
    NEXT_MONTH=1
    NEXT_YEAR=$((YEAR + 1))
fi

FOLDER_DATE=$(printf "%04d-%02d\n" $NEXT_YEAR $NEXT_MONTH)

CURRENT_DATE_TIME=$(date '+%Y%m%d_%H%M%S')

touch success.txt failure.txt skip.txt

clone_repo() {
    REPO_URL=$1
    GROUP_NAME=$(echo $REPO_URL | awk -F'/' '{print $(NF-1)}')
    PROJECT_NAME=$(echo $REPO_URL | awk -F'/' '{print $NF}' | sed 's/\.git//')

    echo "Cloning $REPO_URL into /data/$GROUP_NAME/$PROJECT_NAME/$FOLDER_DATE"

    sudo mkdir -p "/data/$GROUP_NAME/$PROJECT_NAME/$FOLDER_DATE"
    sudo chown 65532:65532 "/data/$GROUP_NAME/$PROJECT_NAME/$FOLDER_DATE"

    if [ ! -d "/data/$GROUP_NAME/$PROJECT_NAME/$FOLDER_DATE/.git" ]; then
        if git clone "$REPO_URL" "/data/$GROUP_NAME/$PROJECT_NAME/$FOLDER_DATE"; then
            echo "$REPO_URL" >>"success.txt"
        else
            echo "$REPO_URL" >>"failure.txt"
        fi
    else
        echo "$REPO_URL" >>"skip.txt"
    fi
}

export -f clone_repo
export FOLDER_DATE

printf '%s\n' "${REPO_LIST[@]}" | parallel -j $MAX_PROCESSES clone_repo

echo "Cloning process has finished. Please check success.txt and failure.txt for details."

echo ""
echo ""
echo "---------------------- "
echo "| Succeed repo below: |"
cat success.txt
echo "----------------------"

echo ""
echo ""
echo "---------------------- "
echo "| Failed repo below: |"
cat failure.txt
echo "----------------------"

echo ""
echo ""
echo "---------------------- "
echo "| Skiped repo below: |"
cat skip.txt
echo "----------------------"
