// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        (unknown)
// source: event/event.proto

package event

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DeployChangeLogEvent_Method int32

const (
	DeployChangeLogEvent_CREATE DeployChangeLogEvent_Method = 0
	DeployChangeLogEvent_UPDATE DeployChangeLogEvent_Method = 1
)

// Enum value maps for DeployChangeLogEvent_Method.
var (
	DeployChangeLogEvent_Method_name = map[int32]string{
		0: "CREATE",
		1: "UPDATE",
	}
	DeployChangeLogEvent_Method_value = map[string]int32{
		"CREATE": 0,
		"UPDATE": 1,
	}
)

func (x DeployChangeLogEvent_Method) Enum() *DeployChangeLogEvent_Method {
	p := new(DeployChangeLogEvent_Method)
	*p = x
	return p
}

func (x DeployChangeLogEvent_Method) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeployChangeLogEvent_Method) Descriptor() protoreflect.EnumDescriptor {
	return file_event_event_proto_enumTypes[0].Descriptor()
}

func (DeployChangeLogEvent_Method) Type() protoreflect.EnumType {
	return &file_event_event_proto_enumTypes[0]
}

func (x DeployChangeLogEvent_Method) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeployChangeLogEvent_Method.Descriptor instead.
func (DeployChangeLogEvent_Method) EnumDescriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{6, 0}
}

type ProjectEvent_Action int32

const (
	ProjectEvent_DEFAULT ProjectEvent_Action = 0
	ProjectEvent_CREATE  ProjectEvent_Action = 1
	ProjectEvent_UPDATE  ProjectEvent_Action = 2
	ProjectEvent_DELETE  ProjectEvent_Action = 3
)

// Enum value maps for ProjectEvent_Action.
var (
	ProjectEvent_Action_name = map[int32]string{
		0: "DEFAULT",
		1: "CREATE",
		2: "UPDATE",
		3: "DELETE",
	}
	ProjectEvent_Action_value = map[string]int32{
		"DEFAULT": 0,
		"CREATE":  1,
		"UPDATE":  2,
		"DELETE":  3,
	}
)

func (x ProjectEvent_Action) Enum() *ProjectEvent_Action {
	p := new(ProjectEvent_Action)
	*p = x
	return p
}

func (x ProjectEvent_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProjectEvent_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_event_event_proto_enumTypes[1].Descriptor()
}

func (ProjectEvent_Action) Type() protoreflect.EnumType {
	return &file_event_event_proto_enumTypes[1]
}

func (x ProjectEvent_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProjectEvent_Action.Descriptor instead.
func (ProjectEvent_Action) EnumDescriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{15, 0}
}

type DeletePipelineResourceEvent_DeleteFromEnum int32

const (
	DeletePipelineResourceEvent_DEFAULT        DeletePipelineResourceEvent_DeleteFromEnum = 0
	DeletePipelineResourceEvent_PIPELINE       DeletePipelineResourceEvent_DeleteFromEnum = 1
	DeletePipelineResourceEvent_PIPELINE_GROUP DeletePipelineResourceEvent_DeleteFromEnum = 2
	DeletePipelineResourceEvent_TEMPLATE       DeletePipelineResourceEvent_DeleteFromEnum = 3
)

// Enum value maps for DeletePipelineResourceEvent_DeleteFromEnum.
var (
	DeletePipelineResourceEvent_DeleteFromEnum_name = map[int32]string{
		0: "DEFAULT",
		1: "PIPELINE",
		2: "PIPELINE_GROUP",
		3: "TEMPLATE",
	}
	DeletePipelineResourceEvent_DeleteFromEnum_value = map[string]int32{
		"DEFAULT":        0,
		"PIPELINE":       1,
		"PIPELINE_GROUP": 2,
		"TEMPLATE":       3,
	}
)

func (x DeletePipelineResourceEvent_DeleteFromEnum) Enum() *DeletePipelineResourceEvent_DeleteFromEnum {
	p := new(DeletePipelineResourceEvent_DeleteFromEnum)
	*p = x
	return p
}

func (x DeletePipelineResourceEvent_DeleteFromEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeletePipelineResourceEvent_DeleteFromEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_event_event_proto_enumTypes[2].Descriptor()
}

func (DeletePipelineResourceEvent_DeleteFromEnum) Type() protoreflect.EnumType {
	return &file_event_event_proto_enumTypes[2]
}

func (x DeletePipelineResourceEvent_DeleteFromEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeletePipelineResourceEvent_DeleteFromEnum.Descriptor instead.
func (DeletePipelineResourceEvent_DeleteFromEnum) EnumDescriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{16, 0}
}

// * 触发模式
type PipelineStatusChangeEvent_TriggerMode int32

const (
	PipelineStatusChangeEvent_AUTO   PipelineStatusChangeEvent_TriggerMode = 0 //* 自动触发
	PipelineStatusChangeEvent_MANUAL PipelineStatusChangeEvent_TriggerMode = 1 //* 手动触发
)

// Enum value maps for PipelineStatusChangeEvent_TriggerMode.
var (
	PipelineStatusChangeEvent_TriggerMode_name = map[int32]string{
		0: "AUTO",
		1: "MANUAL",
	}
	PipelineStatusChangeEvent_TriggerMode_value = map[string]int32{
		"AUTO":   0,
		"MANUAL": 1,
	}
)

func (x PipelineStatusChangeEvent_TriggerMode) Enum() *PipelineStatusChangeEvent_TriggerMode {
	p := new(PipelineStatusChangeEvent_TriggerMode)
	*p = x
	return p
}

func (x PipelineStatusChangeEvent_TriggerMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PipelineStatusChangeEvent_TriggerMode) Descriptor() protoreflect.EnumDescriptor {
	return file_event_event_proto_enumTypes[3].Descriptor()
}

func (PipelineStatusChangeEvent_TriggerMode) Type() protoreflect.EnumType {
	return &file_event_event_proto_enumTypes[3]
}

func (x PipelineStatusChangeEvent_TriggerMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PipelineStatusChangeEvent_TriggerMode.Descriptor instead.
func (PipelineStatusChangeEvent_TriggerMode) EnumDescriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{17, 0}
}

type AppDeployActionEvent_Action int32

const (
	AppDeployActionEvent_DEPLOY   AppDeployActionEvent_Action = 0 //* 部署
	AppDeployActionEvent_ROLLBACK AppDeployActionEvent_Action = 1 //* 回滚
	AppDeployActionEvent_OFFLINE  AppDeployActionEvent_Action = 2 //* 下线
	AppDeployActionEvent_RESTART  AppDeployActionEvent_Action = 3 //* 重启
	AppDeployActionEvent_RETRY    AppDeployActionEvent_Action = 4 //* 重试
)

// Enum value maps for AppDeployActionEvent_Action.
var (
	AppDeployActionEvent_Action_name = map[int32]string{
		0: "DEPLOY",
		1: "ROLLBACK",
		2: "OFFLINE",
		3: "RESTART",
		4: "RETRY",
	}
	AppDeployActionEvent_Action_value = map[string]int32{
		"DEPLOY":   0,
		"ROLLBACK": 1,
		"OFFLINE":  2,
		"RESTART":  3,
		"RETRY":    4,
	}
)

func (x AppDeployActionEvent_Action) Enum() *AppDeployActionEvent_Action {
	p := new(AppDeployActionEvent_Action)
	*p = x
	return p
}

func (x AppDeployActionEvent_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppDeployActionEvent_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_event_event_proto_enumTypes[4].Descriptor()
}

func (AppDeployActionEvent_Action) Type() protoreflect.EnumType {
	return &file_event_event_proto_enumTypes[4]
}

func (x AppDeployActionEvent_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppDeployActionEvent_Action.Descriptor instead.
func (AppDeployActionEvent_Action) EnumDescriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{20, 0}
}

type AppDeployActionEvent_Status int32

const (
	AppDeployActionEvent_SUCCESS AppDeployActionEvent_Status = 0 //* 成功
	AppDeployActionEvent_FAILED  AppDeployActionEvent_Status = 1 //* 失败
)

// Enum value maps for AppDeployActionEvent_Status.
var (
	AppDeployActionEvent_Status_name = map[int32]string{
		0: "SUCCESS",
		1: "FAILED",
	}
	AppDeployActionEvent_Status_value = map[string]int32{
		"SUCCESS": 0,
		"FAILED":  1,
	}
)

func (x AppDeployActionEvent_Status) Enum() *AppDeployActionEvent_Status {
	p := new(AppDeployActionEvent_Status)
	*p = x
	return p
}

func (x AppDeployActionEvent_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppDeployActionEvent_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_event_event_proto_enumTypes[5].Descriptor()
}

func (AppDeployActionEvent_Status) Type() protoreflect.EnumType {
	return &file_event_event_proto_enumTypes[5]
}

func (x AppDeployActionEvent_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppDeployActionEvent_Status.Descriptor instead.
func (AppDeployActionEvent_Status) EnumDescriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{20, 1}
}

type PipelineRunEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// @gotags: json:"startedTime,omitempty"
	StartedTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=started_time,json=startedTime,proto3" json:"started_time,omitempty"`
	// @gotags: json:"completedTime,omitempty"
	CompletedTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=completed_time,json=completedTime,proto3" json:"completed_time,omitempty"`
	// @gotags: json:"elapsedTime,omitempty"
	ElapsedTime *durationpb.Duration `protobuf:"bytes,4,opt,name=elapsed_time,json=elapsedTime,proto3" json:"elapsed_time,omitempty"`
	Name        string               `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Namespace   string               `protobuf:"bytes,6,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// @gotags: json:"pipelineId,omitempty"
	PipelineId int64 `protobuf:"varint,7,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id,omitempty"`
	// @gotags: json:"appId,omitempty"
	AppId  int64  `protobuf:"varint,8,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Status string `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`
	// @gotags: json:"triggerById,omitempty"
	TriggerById int64 `protobuf:"varint,10,opt,name=trigger_by_id,json=triggerById,proto3" json:"trigger_by_id,omitempty"`
	// @gotags: json:"triggerByChineseName,omitempty"
	TriggerByChineseName string `protobuf:"bytes,11,opt,name=trigger_by_chinese_name,json=triggerByChineseName,proto3" json:"trigger_by_chinese_name,omitempty"`
	BuildNumber          int64  `protobuf:"varint,12,opt,name=build_number,json=buildNumber,proto3" json:"build_number,omitempty"`
}

func (x *PipelineRunEvent) Reset() {
	*x = PipelineRunEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineRunEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineRunEvent) ProtoMessage() {}

func (x *PipelineRunEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineRunEvent.ProtoReflect.Descriptor instead.
func (*PipelineRunEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{0}
}

func (x *PipelineRunEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PipelineRunEvent) GetStartedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedTime
	}
	return nil
}

func (x *PipelineRunEvent) GetCompletedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedTime
	}
	return nil
}

func (x *PipelineRunEvent) GetElapsedTime() *durationpb.Duration {
	if x != nil {
		return x.ElapsedTime
	}
	return nil
}

func (x *PipelineRunEvent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PipelineRunEvent) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *PipelineRunEvent) GetPipelineId() int64 {
	if x != nil {
		return x.PipelineId
	}
	return 0
}

func (x *PipelineRunEvent) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *PipelineRunEvent) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *PipelineRunEvent) GetTriggerById() int64 {
	if x != nil {
		return x.TriggerById
	}
	return 0
}

func (x *PipelineRunEvent) GetTriggerByChineseName() string {
	if x != nil {
		return x.TriggerByChineseName
	}
	return ""
}

func (x *PipelineRunEvent) GetBuildNumber() int64 {
	if x != nil {
		return x.BuildNumber
	}
	return 0
}

type TaskRunEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// @gotags: json:"startedTime,omitempty"
	StartedTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=started_time,json=startedTime,proto3" json:"started_time,omitempty"`
	// @gotags: json:"completedTime,omitempty"
	CompletedTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=completed_time,json=completedTime,proto3" json:"completed_time,omitempty"`
	// @gotags: json:"elapsedTime,omitempty"
	ElapsedTime *durationpb.Duration `protobuf:"bytes,4,opt,name=elapsed_time,json=elapsedTime,proto3" json:"elapsed_time,omitempty"`
	Name        string               `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Namespace   string               `protobuf:"bytes,6,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Type        string               `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
	Status      string               `protobuf:"bytes,8,opt,name=status,proto3" json:"status,omitempty"`
	// @gotags: json:"pipelineRunStageId,omitempty"
	PipelineRunStageId int64            `protobuf:"varint,9,opt,name=pipeline_run_stage_id,json=pipelineRunStageId,proto3" json:"pipeline_run_stage_id,omitempty"`
	Results            *structpb.Struct `protobuf:"bytes,10,opt,name=results,proto3" json:"results,omitempty"`
	// @gotags: json:"pipelineRunId,omitempty"
	PipelineRunId   int64  `protobuf:"varint,11,opt,name=pipeline_run_id,json=pipelineRunId,proto3" json:"pipeline_run_id,omitempty"`
	Reason          string `protobuf:"bytes,12,opt,name=reason,proto3" json:"reason,omitempty"`
	TicketTaskRunId int64  `protobuf:"varint,13,opt,name=ticket_task_run_id,json=ticketTaskRunId,proto3" json:"ticket_task_run_id,omitempty"`
}

func (x *TaskRunEvent) Reset() {
	*x = TaskRunEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskRunEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskRunEvent) ProtoMessage() {}

func (x *TaskRunEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskRunEvent.ProtoReflect.Descriptor instead.
func (*TaskRunEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{1}
}

func (x *TaskRunEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TaskRunEvent) GetStartedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedTime
	}
	return nil
}

func (x *TaskRunEvent) GetCompletedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedTime
	}
	return nil
}

func (x *TaskRunEvent) GetElapsedTime() *durationpb.Duration {
	if x != nil {
		return x.ElapsedTime
	}
	return nil
}

func (x *TaskRunEvent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TaskRunEvent) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *TaskRunEvent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TaskRunEvent) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *TaskRunEvent) GetPipelineRunStageId() int64 {
	if x != nil {
		return x.PipelineRunStageId
	}
	return 0
}

func (x *TaskRunEvent) GetResults() *structpb.Struct {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *TaskRunEvent) GetPipelineRunId() int64 {
	if x != nil {
		return x.PipelineRunId
	}
	return 0
}

func (x *TaskRunEvent) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *TaskRunEvent) GetTicketTaskRunId() int64 {
	if x != nil {
		return x.TicketTaskRunId
	}
	return 0
}

type SubTaskRunEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// @gotags: json:"startedTime,omitempty"
	StartedTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=started_time,json=startedTime,proto3" json:"started_time,omitempty"`
	// @gotags: json:"completedTime,omitempty"
	CompletedTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=completed_time,json=completedTime,proto3" json:"completed_time,omitempty"`
	// @gotags: json:"elapsedTime,omitempty"
	ElapsedTime *durationpb.Duration `protobuf:"bytes,4,opt,name=elapsed_time,json=elapsedTime,proto3" json:"elapsed_time,omitempty"`
	Name        string               `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Namespace   string               `protobuf:"bytes,6,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Type        string               `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
	Status      string               `protobuf:"bytes,8,opt,name=status,proto3" json:"status,omitempty"`
	// @gotags: json:"pipelineRunStageId,omitempty"
	PipelineRunStageId int64 `protobuf:"varint,9,opt,name=pipeline_run_stage_id,json=pipelineRunStageId,proto3" json:"pipeline_run_stage_id,omitempty"`
	// @gotags: json:"taskRunId,omitempty"
	TaskRunId int64            `protobuf:"varint,10,opt,name=task_run_id,json=taskRunId,proto3" json:"task_run_id,omitempty"`
	Results   *structpb.Struct `protobuf:"bytes,11,opt,name=results,proto3" json:"results,omitempty"`
	// @gotags: json:"pipelineRunId,omitempty"
	PipelineRunId int64  `protobuf:"varint,12,opt,name=pipeline_run_id,json=pipelineRunId,proto3" json:"pipeline_run_id,omitempty"`
	Reason        string `protobuf:"bytes,13,opt,name=reason,proto3" json:"reason,omitempty"`
	SubTaskId     int64  `protobuf:"varint,14,opt,name=sub_task_id,json=subTaskId,proto3" json:"sub_task_id,omitempty"`
}

func (x *SubTaskRunEvent) Reset() {
	*x = SubTaskRunEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubTaskRunEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubTaskRunEvent) ProtoMessage() {}

func (x *SubTaskRunEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubTaskRunEvent.ProtoReflect.Descriptor instead.
func (*SubTaskRunEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{2}
}

func (x *SubTaskRunEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SubTaskRunEvent) GetStartedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedTime
	}
	return nil
}

func (x *SubTaskRunEvent) GetCompletedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedTime
	}
	return nil
}

func (x *SubTaskRunEvent) GetElapsedTime() *durationpb.Duration {
	if x != nil {
		return x.ElapsedTime
	}
	return nil
}

func (x *SubTaskRunEvent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SubTaskRunEvent) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *SubTaskRunEvent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *SubTaskRunEvent) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SubTaskRunEvent) GetPipelineRunStageId() int64 {
	if x != nil {
		return x.PipelineRunStageId
	}
	return 0
}

func (x *SubTaskRunEvent) GetTaskRunId() int64 {
	if x != nil {
		return x.TaskRunId
	}
	return 0
}

func (x *SubTaskRunEvent) GetResults() *structpb.Struct {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *SubTaskRunEvent) GetPipelineRunId() int64 {
	if x != nil {
		return x.PipelineRunId
	}
	return 0
}

func (x *SubTaskRunEvent) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *SubTaskRunEvent) GetSubTaskId() int64 {
	if x != nil {
		return x.SubTaskId
	}
	return 0
}

type StageRunEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// @gotags: json:"startedTime,omitempty"
	StartedTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=started_time,json=startedTime,proto3" json:"started_time,omitempty"`
	// @gotags: json:"completedTime,omitempty"
	CompletedTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=completed_time,json=completedTime,proto3" json:"completed_time,omitempty"`
	// @gotags: json:"elapsedTime,omitempty"
	ElapsedTime *durationpb.Duration `protobuf:"bytes,4,opt,name=elapsed_time,json=elapsedTime,proto3" json:"elapsed_time,omitempty"`
	Name        string               `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Type        string               `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	Status      string               `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	// @gotags: json:"pipelineRunId,omitempty"
	PipelineRunId int64 `protobuf:"varint,8,opt,name=pipeline_run_id,json=pipelineRunId,proto3" json:"pipeline_run_id,omitempty"`
}

func (x *StageRunEvent) Reset() {
	*x = StageRunEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageRunEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageRunEvent) ProtoMessage() {}

func (x *StageRunEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageRunEvent.ProtoReflect.Descriptor instead.
func (*StageRunEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{3}
}

func (x *StageRunEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StageRunEvent) GetStartedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedTime
	}
	return nil
}

func (x *StageRunEvent) GetCompletedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedTime
	}
	return nil
}

func (x *StageRunEvent) GetElapsedTime() *durationpb.Duration {
	if x != nil {
		return x.ElapsedTime
	}
	return nil
}

func (x *StageRunEvent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StageRunEvent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *StageRunEvent) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *StageRunEvent) GetPipelineRunId() int64 {
	if x != nil {
		return x.PipelineRunId
	}
	return 0
}

type ApprovalUpdateEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"ticketId,omitempty"
	TicketId int64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	// @gotags: json:"flowInstId,omitempty"
	FlowInstId int64 `protobuf:"varint,2,opt,name=flow_inst_id,json=flowInstId,proto3" json:"flow_inst_id,omitempty"`
	// @gotags: json:"nodeInstId,omitempty"
	NodeInstId int64 `protobuf:"varint,3,opt,name=node_inst_id,json=nodeInstId,proto3" json:"node_inst_id,omitempty"`
	// @gotags: json:"approvalUserId,omitempty"
	ApprovalUserId int64 `protobuf:"varint,4,opt,name=approval_user_id,json=approvalUserId,proto3" json:"approval_user_id,omitempty"`
	// the status of approval
	Status string `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	// the opinion of approval
	Opinion string `protobuf:"bytes,6,opt,name=opinion,proto3" json:"opinion,omitempty"`
	// @gotags: json:"taskId,omitempty"
	TaskId int64 `protobuf:"varint,7,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// @gotags: json:"projectId,omitempty"
	ProjectId    int64  `protobuf:"varint,8,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	TicketReason string `protobuf:"bytes,9,opt,name=ticket_reason,json=ticketReason,proto3" json:"ticket_reason,omitempty"`
}

func (x *ApprovalUpdateEvent) Reset() {
	*x = ApprovalUpdateEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApprovalUpdateEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApprovalUpdateEvent) ProtoMessage() {}

func (x *ApprovalUpdateEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApprovalUpdateEvent.ProtoReflect.Descriptor instead.
func (*ApprovalUpdateEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{4}
}

func (x *ApprovalUpdateEvent) GetTicketId() int64 {
	if x != nil {
		return x.TicketId
	}
	return 0
}

func (x *ApprovalUpdateEvent) GetFlowInstId() int64 {
	if x != nil {
		return x.FlowInstId
	}
	return 0
}

func (x *ApprovalUpdateEvent) GetNodeInstId() int64 {
	if x != nil {
		return x.NodeInstId
	}
	return 0
}

func (x *ApprovalUpdateEvent) GetApprovalUserId() int64 {
	if x != nil {
		return x.ApprovalUserId
	}
	return 0
}

func (x *ApprovalUpdateEvent) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ApprovalUpdateEvent) GetOpinion() string {
	if x != nil {
		return x.Opinion
	}
	return ""
}

func (x *ApprovalUpdateEvent) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *ApprovalUpdateEvent) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *ApprovalUpdateEvent) GetTicketReason() string {
	if x != nil {
		return x.TicketReason
	}
	return ""
}

type ApprovalHandleEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"ticketId,omitempty"
	TicketId int64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	// @gotags: json:"ticketType,omitempty"
	TicketType string `protobuf:"bytes,2,opt,name=ticket_type,json=ticketType,proto3" json:"ticket_type,omitempty"`
	// @gotags: json:"ticketSn,omitempty"
	TicketSn string `protobuf:"bytes,3,opt,name=ticket_sn,json=ticketSn,proto3" json:"ticket_sn,omitempty"`
	// @gotags: json:"ticketReason,omitempty"
	TicketReason string `protobuf:"bytes,4,opt,name=ticket_reason,json=ticketReason,proto3" json:"ticket_reason,omitempty"`
	Branch       string `protobuf:"bytes,5,opt,name=branch,proto3" json:"branch,omitempty"`
	// @gotags: json:"applicantId,omitempty"
	ApplicantId int64 `protobuf:"varint,6,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	// @gotags: json:"createdAt,omitempty"
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// @gotags: json:"approvalNodeName,omitempty"
	ApprovalNodeName string  `protobuf:"bytes,8,opt,name=approval_node_name,json=approvalNodeName,proto3" json:"approval_node_name,omitempty"`
	Approvers        []int64 `protobuf:"varint,9,rep,packed,name=approvers,proto3" json:"approvers,omitempty"`
	// @gotags: json:"appName,omitempty"
	AppName string `protobuf:"bytes,10,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	// @gotags: json:"deployEnvs,omitempty"
	DeployEnvs     []string `protobuf:"bytes,11,rep,name=deploy_envs,json=deployEnvs,proto3" json:"deploy_envs,omitempty"`
	FlowNodeInstId int64    `protobuf:"varint,12,opt,name=flow_node_inst_id,json=flowNodeInstId,proto3" json:"flow_node_inst_id,omitempty"`
	// @gotags: json:"envs,omitempty"
	Envs []*ApprovalHandleEvent_DeployEnv `protobuf:"bytes,13,rep,name=envs,proto3" json:"envs,omitempty"`
	Apps []string                         `protobuf:"bytes,14,rep,name=apps,proto3" json:"apps,omitempty"`
	// @gotags: json:"projectId,omitempty"
	ProjectId  int64  `protobuf:"varint,15,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	TicketName string `protobuf:"bytes,16,opt,name=ticket_name,json=ticketName,proto3" json:"ticket_name,omitempty"`
}

func (x *ApprovalHandleEvent) Reset() {
	*x = ApprovalHandleEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApprovalHandleEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApprovalHandleEvent) ProtoMessage() {}

func (x *ApprovalHandleEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApprovalHandleEvent.ProtoReflect.Descriptor instead.
func (*ApprovalHandleEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{5}
}

func (x *ApprovalHandleEvent) GetTicketId() int64 {
	if x != nil {
		return x.TicketId
	}
	return 0
}

func (x *ApprovalHandleEvent) GetTicketType() string {
	if x != nil {
		return x.TicketType
	}
	return ""
}

func (x *ApprovalHandleEvent) GetTicketSn() string {
	if x != nil {
		return x.TicketSn
	}
	return ""
}

func (x *ApprovalHandleEvent) GetTicketReason() string {
	if x != nil {
		return x.TicketReason
	}
	return ""
}

func (x *ApprovalHandleEvent) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *ApprovalHandleEvent) GetApplicantId() int64 {
	if x != nil {
		return x.ApplicantId
	}
	return 0
}

func (x *ApprovalHandleEvent) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ApprovalHandleEvent) GetApprovalNodeName() string {
	if x != nil {
		return x.ApprovalNodeName
	}
	return ""
}

func (x *ApprovalHandleEvent) GetApprovers() []int64 {
	if x != nil {
		return x.Approvers
	}
	return nil
}

func (x *ApprovalHandleEvent) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *ApprovalHandleEvent) GetDeployEnvs() []string {
	if x != nil {
		return x.DeployEnvs
	}
	return nil
}

func (x *ApprovalHandleEvent) GetFlowNodeInstId() int64 {
	if x != nil {
		return x.FlowNodeInstId
	}
	return 0
}

func (x *ApprovalHandleEvent) GetEnvs() []*ApprovalHandleEvent_DeployEnv {
	if x != nil {
		return x.Envs
	}
	return nil
}

func (x *ApprovalHandleEvent) GetApps() []string {
	if x != nil {
		return x.Apps
	}
	return nil
}

func (x *ApprovalHandleEvent) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *ApprovalHandleEvent) GetTicketName() string {
	if x != nil {
		return x.TicketName
	}
	return ""
}

type DeployChangeLogEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                    int64                       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Env                   int32                       `protobuf:"varint,2,opt,name=env,proto3" json:"env,omitempty"`
	EnvTarget             int32                       `protobuf:"varint,3,opt,name=env_target,json=envTarget,proto3" json:"env_target,omitempty"`
	Cluster               string                      `protobuf:"bytes,4,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace             string                      `protobuf:"bytes,5,opt,name=namespace,proto3" json:"namespace,omitempty"`
	AppId                 int64                       `protobuf:"varint,6,opt,name=app_id,json=appID,proto3" json:"app_id,omitempty"`
	ConfigId              int64                       `protobuf:"varint,7,opt,name=config_id,json=configID,proto3" json:"config_id,omitempty"`
	TaskRunId             int64                       `protobuf:"varint,8,opt,name=task_run_id,json=taskRunID,proto3" json:"task_run_id,omitempty"`
	Status                int32                       `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`
	IsCurrent             bool                        `protobuf:"varint,10,opt,name=is_current,json=isCurrent,proto3" json:"is_current,omitempty"`
	Description           string                      `protobuf:"bytes,11,opt,name=description,proto3" json:"description,omitempty"`
	OperatorBy            int64                       `protobuf:"varint,12,opt,name=operator_by,json=operatorBy,proto3" json:"operator_by,omitempty"`
	OperatorByChineseName string                      `protobuf:"bytes,13,opt,name=operator_by_chinese_name,json=operatorByChineseName,proto3" json:"operator_by_chinese_name,omitempty"`
	OperatorByEmployeeNo  string                      `protobuf:"bytes,14,opt,name=operator_by_employee_no,json=operatorByEmployeeNo,proto3" json:"operator_by_employee_no,omitempty"`
	OperatedAt            *timestamppb.Timestamp      `protobuf:"bytes,15,opt,name=operated_at,json=operatedAt,proto3" json:"operated_at,omitempty"`
	Action                int64                       `protobuf:"varint,16,opt,name=action,proto3" json:"action,omitempty"`
	ConfigVersion         int64                       `protobuf:"varint,17,opt,name=config_version,json=configVersion,proto3" json:"config_version,omitempty"`
	MetadataId            int64                       `protobuf:"varint,18,opt,name=metadata_id,json=metadataID,proto3" json:"metadata_id,omitempty"`
	ArtifactVersion       string                      `protobuf:"bytes,19,opt,name=artifact_version,json=artifactVersion,proto3" json:"artifact_version,omitempty"`
	Branch                string                      `protobuf:"bytes,20,opt,name=branch,proto3" json:"branch,omitempty"`
	Method                DeployChangeLogEvent_Method `protobuf:"varint,21,opt,name=method,proto3,enum=event.DeployChangeLogEvent_Method" json:"method,omitempty"`
	Senv                  string                      `protobuf:"bytes,22,opt,name=senv,proto3" json:"senv,omitempty"`
}

func (x *DeployChangeLogEvent) Reset() {
	*x = DeployChangeLogEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeployChangeLogEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeployChangeLogEvent) ProtoMessage() {}

func (x *DeployChangeLogEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeployChangeLogEvent.ProtoReflect.Descriptor instead.
func (*DeployChangeLogEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{6}
}

func (x *DeployChangeLogEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeployChangeLogEvent) GetEnv() int32 {
	if x != nil {
		return x.Env
	}
	return 0
}

func (x *DeployChangeLogEvent) GetEnvTarget() int32 {
	if x != nil {
		return x.EnvTarget
	}
	return 0
}

func (x *DeployChangeLogEvent) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DeployChangeLogEvent) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DeployChangeLogEvent) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *DeployChangeLogEvent) GetConfigId() int64 {
	if x != nil {
		return x.ConfigId
	}
	return 0
}

func (x *DeployChangeLogEvent) GetTaskRunId() int64 {
	if x != nil {
		return x.TaskRunId
	}
	return 0
}

func (x *DeployChangeLogEvent) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *DeployChangeLogEvent) GetIsCurrent() bool {
	if x != nil {
		return x.IsCurrent
	}
	return false
}

func (x *DeployChangeLogEvent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DeployChangeLogEvent) GetOperatorBy() int64 {
	if x != nil {
		return x.OperatorBy
	}
	return 0
}

func (x *DeployChangeLogEvent) GetOperatorByChineseName() string {
	if x != nil {
		return x.OperatorByChineseName
	}
	return ""
}

func (x *DeployChangeLogEvent) GetOperatorByEmployeeNo() string {
	if x != nil {
		return x.OperatorByEmployeeNo
	}
	return ""
}

func (x *DeployChangeLogEvent) GetOperatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.OperatedAt
	}
	return nil
}

func (x *DeployChangeLogEvent) GetAction() int64 {
	if x != nil {
		return x.Action
	}
	return 0
}

func (x *DeployChangeLogEvent) GetConfigVersion() int64 {
	if x != nil {
		return x.ConfigVersion
	}
	return 0
}

func (x *DeployChangeLogEvent) GetMetadataId() int64 {
	if x != nil {
		return x.MetadataId
	}
	return 0
}

func (x *DeployChangeLogEvent) GetArtifactVersion() string {
	if x != nil {
		return x.ArtifactVersion
	}
	return ""
}

func (x *DeployChangeLogEvent) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *DeployChangeLogEvent) GetMethod() DeployChangeLogEvent_Method {
	if x != nil {
		return x.Method
	}
	return DeployChangeLogEvent_CREATE
}

func (x *DeployChangeLogEvent) GetSenv() string {
	if x != nil {
		return x.Senv
	}
	return ""
}

// 部署配置模板事件
type DeployCfgTmplEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// @gotags: json:"projectID,omitempty"
	ProjectId int64 `protobuf:"varint,2,opt,name=project_id,json=projectID,proto3" json:"project_id,omitempty"`
	// @gotags: json:"configVersion,omitempty"
	ConfigVersion int64 `protobuf:"varint,3,opt,name=config_version,json=configVersion,proto3" json:"config_version,omitempty"`
	// @gotags: json:"configType,omitempty"
	ConfigType int32 `protobuf:"varint,4,opt,name=config_type,json=configType,proto3" json:"config_type,omitempty"`
	// @gotags: json:"createdBy,omitempty"
	CreatedBy int64 `protobuf:"varint,5,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	// @gotags: json:"createdByChineseName,omitempty"
	CreatedByChineseName string `protobuf:"bytes,6,opt,name=created_by_chinese_name,json=createdByChineseName,proto3" json:"created_by_chinese_name,omitempty"`
	// @gotags: json:"createdByEmployeeNo,omitempty"
	CreatedByEmployeeNo string `protobuf:"bytes,7,opt,name=created_by_employee_no,json=createdByEmployeeNo,proto3" json:"created_by_employee_no,omitempty"`
	// @gotags: json:"createdAt,omitempty"
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// @gotags: json:"updatedAt,omitempty"
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *DeployCfgTmplEvent) Reset() {
	*x = DeployCfgTmplEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeployCfgTmplEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeployCfgTmplEvent) ProtoMessage() {}

func (x *DeployCfgTmplEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeployCfgTmplEvent.ProtoReflect.Descriptor instead.
func (*DeployCfgTmplEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{7}
}

func (x *DeployCfgTmplEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeployCfgTmplEvent) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *DeployCfgTmplEvent) GetConfigVersion() int64 {
	if x != nil {
		return x.ConfigVersion
	}
	return 0
}

func (x *DeployCfgTmplEvent) GetConfigType() int32 {
	if x != nil {
		return x.ConfigType
	}
	return 0
}

func (x *DeployCfgTmplEvent) GetCreatedBy() int64 {
	if x != nil {
		return x.CreatedBy
	}
	return 0
}

func (x *DeployCfgTmplEvent) GetCreatedByChineseName() string {
	if x != nil {
		return x.CreatedByChineseName
	}
	return ""
}

func (x *DeployCfgTmplEvent) GetCreatedByEmployeeNo() string {
	if x != nil {
		return x.CreatedByEmployeeNo
	}
	return ""
}

func (x *DeployCfgTmplEvent) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DeployCfgTmplEvent) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// 部署配置模板变更事件
type DeployCfgTmplChangeEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// @gotags: json:"projectID,omitempty"
	ProjectId int64 `protobuf:"varint,2,opt,name=project_id,json=projectID,proto3" json:"project_id,omitempty"`
	// @gotags: json:"configName,omitempty"
	ConfigName string `protobuf:"bytes,3,opt,name=config_name,json=configName,proto3" json:"config_name,omitempty"`
	Operator   int64  `protobuf:"varint,4,opt,name=operator,proto3" json:"operator,omitempty"`
	Email      string `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	// @gotags: json:"updatedAt,omitempty"
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *DeployCfgTmplChangeEvent) Reset() {
	*x = DeployCfgTmplChangeEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeployCfgTmplChangeEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeployCfgTmplChangeEvent) ProtoMessage() {}

func (x *DeployCfgTmplChangeEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeployCfgTmplChangeEvent.ProtoReflect.Descriptor instead.
func (*DeployCfgTmplChangeEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{8}
}

func (x *DeployCfgTmplChangeEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeployCfgTmplChangeEvent) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *DeployCfgTmplChangeEvent) GetConfigName() string {
	if x != nil {
		return x.ConfigName
	}
	return ""
}

func (x *DeployCfgTmplChangeEvent) GetOperator() int64 {
	if x != nil {
		return x.Operator
	}
	return 0
}

func (x *DeployCfgTmplChangeEvent) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *DeployCfgTmplChangeEvent) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type TicketUpdateEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *TicketUpdateEvent) Reset() {
	*x = TicketUpdateEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TicketUpdateEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TicketUpdateEvent) ProtoMessage() {}

func (x *TicketUpdateEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TicketUpdateEvent.ProtoReflect.Descriptor instead.
func (*TicketUpdateEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{9}
}

func (x *TicketUpdateEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ChSetPipelineTaskEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// @gotags: json:"changeSetId,omitempty"
	ChangeSetId int64  `protobuf:"varint,2,opt,name=change_set_id,json=changeSetId,proto3" json:"change_set_id,omitempty"`
	Type        string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Status      string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	// @gotags: json:"pipelineRunId,omitempty"
	PipelineRunId int64 `protobuf:"varint,5,opt,name=pipeline_run_id,json=pipelineRunId,proto3" json:"pipeline_run_id,omitempty"`
	// @gotags: json:"taskRunId,omitempty"
	TaskRunId int64 `protobuf:"varint,6,opt,name=task_run_id,json=taskRunId,proto3" json:"task_run_id,omitempty"`
	// @gotags: json:"stageId,omitempty"
	StageId int64 `protobuf:"varint,7,opt,name=stage_id,json=stageId,proto3" json:"stage_id,omitempty"`
	// @gotags: json:"taskId,omitempty"
	TaskId int64 `protobuf:"varint,8,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// @gotags: json:"changeSetStageId,omitempty"
	ChangeSetStageId int64 `protobuf:"varint,9,opt,name=change_set_stage_id,json=changeSetStageId,proto3" json:"change_set_stage_id,omitempty"`
}

func (x *ChSetPipelineTaskEvent) Reset() {
	*x = ChSetPipelineTaskEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChSetPipelineTaskEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChSetPipelineTaskEvent) ProtoMessage() {}

func (x *ChSetPipelineTaskEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChSetPipelineTaskEvent.ProtoReflect.Descriptor instead.
func (*ChSetPipelineTaskEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{10}
}

func (x *ChSetPipelineTaskEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChSetPipelineTaskEvent) GetChangeSetId() int64 {
	if x != nil {
		return x.ChangeSetId
	}
	return 0
}

func (x *ChSetPipelineTaskEvent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ChSetPipelineTaskEvent) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ChSetPipelineTaskEvent) GetPipelineRunId() int64 {
	if x != nil {
		return x.PipelineRunId
	}
	return 0
}

func (x *ChSetPipelineTaskEvent) GetTaskRunId() int64 {
	if x != nil {
		return x.TaskRunId
	}
	return 0
}

func (x *ChSetPipelineTaskEvent) GetStageId() int64 {
	if x != nil {
		return x.StageId
	}
	return 0
}

func (x *ChSetPipelineTaskEvent) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *ChSetPipelineTaskEvent) GetChangeSetStageId() int64 {
	if x != nil {
		return x.ChangeSetStageId
	}
	return 0
}

type PipelineTemplateChangeEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *PipelineTemplateChangeEvent) Reset() {
	*x = PipelineTemplateChangeEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineTemplateChangeEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineTemplateChangeEvent) ProtoMessage() {}

func (x *PipelineTemplateChangeEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineTemplateChangeEvent.ProtoReflect.Descriptor instead.
func (*PipelineTemplateChangeEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{11}
}

func (x *PipelineTemplateChangeEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ChangeSetTaskEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type   string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Status string `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	// @gotags: json:"changeSetId,omitempty"
	ChangeSetId int64 `protobuf:"varint,4,opt,name=change_set_id,json=changeSetId,proto3" json:"change_set_id,omitempty"`
	// @gotags: json:"changeSetStageId,omitempty"
	ChangeSetStageId int64 `protobuf:"varint,5,opt,name=change_set_stage_id,json=changeSetStageId,proto3" json:"change_set_stage_id,omitempty"`
	IsIgnoreNotify   bool  `protobuf:"varint,6,opt,name=is_ignore_notify,json=isIgnoreNotify,proto3" json:"is_ignore_notify,omitempty"`
}

func (x *ChangeSetTaskEvent) Reset() {
	*x = ChangeSetTaskEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeSetTaskEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeSetTaskEvent) ProtoMessage() {}

func (x *ChangeSetTaskEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeSetTaskEvent.ProtoReflect.Descriptor instead.
func (*ChangeSetTaskEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{12}
}

func (x *ChangeSetTaskEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChangeSetTaskEvent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ChangeSetTaskEvent) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ChangeSetTaskEvent) GetChangeSetId() int64 {
	if x != nil {
		return x.ChangeSetId
	}
	return 0
}

func (x *ChangeSetTaskEvent) GetChangeSetStageId() int64 {
	if x != nil {
		return x.ChangeSetStageId
	}
	return 0
}

func (x *ChangeSetTaskEvent) GetIsIgnoreNotify() bool {
	if x != nil {
		return x.IsIgnoreNotify
	}
	return false
}

type ChangeSetRunEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// @gotags: json:"startedTime,omitempty"
	StartedTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=started_time,json=startedTime,proto3" json:"started_time,omitempty"`
	// @gotags: json:"completedTime,omitempty"
	CompletedTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=completed_time,json=completedTime,proto3" json:"completed_time,omitempty"`
	// @gotags: json:"elapsedTime,omitempty"
	ElapsedTime *durationpb.Duration `protobuf:"bytes,4,opt,name=elapsed_time,json=elapsedTime,proto3" json:"elapsed_time,omitempty"`
	Name        string               `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Status      string               `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ChangeSetRunEvent) Reset() {
	*x = ChangeSetRunEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeSetRunEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeSetRunEvent) ProtoMessage() {}

func (x *ChangeSetRunEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeSetRunEvent.ProtoReflect.Descriptor instead.
func (*ChangeSetRunEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{13}
}

func (x *ChangeSetRunEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChangeSetRunEvent) GetStartedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedTime
	}
	return nil
}

func (x *ChangeSetRunEvent) GetCompletedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedTime
	}
	return nil
}

func (x *ChangeSetRunEvent) GetElapsedTime() *durationpb.Duration {
	if x != nil {
		return x.ElapsedTime
	}
	return nil
}

func (x *ChangeSetRunEvent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ChangeSetRunEvent) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type CanaryShiftEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskRunId int64 `protobuf:"varint,1,opt,name=task_run_id,json=taskRunId,proto3" json:"task_run_id,omitempty"`
	Current   int64 `protobuf:"varint,2,opt,name=current,proto3" json:"current,omitempty"`
	Step      int64 `protobuf:"varint,3,opt,name=step,proto3" json:"step,omitempty"`
	Min       int64 `protobuf:"varint,4,opt,name=min,proto3" json:"min,omitempty"`
	Max       int64 `protobuf:"varint,5,opt,name=max,proto3" json:"max,omitempty"`
}

func (x *CanaryShiftEvent) Reset() {
	*x = CanaryShiftEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CanaryShiftEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CanaryShiftEvent) ProtoMessage() {}

func (x *CanaryShiftEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CanaryShiftEvent.ProtoReflect.Descriptor instead.
func (*CanaryShiftEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{14}
}

func (x *CanaryShiftEvent) GetTaskRunId() int64 {
	if x != nil {
		return x.TaskRunId
	}
	return 0
}

func (x *CanaryShiftEvent) GetCurrent() int64 {
	if x != nil {
		return x.Current
	}
	return 0
}

func (x *CanaryShiftEvent) GetStep() int64 {
	if x != nil {
		return x.Step
	}
	return 0
}

func (x *CanaryShiftEvent) GetMin() int64 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *CanaryShiftEvent) GetMax() int64 {
	if x != nil {
		return x.Max
	}
	return 0
}

type ProjectEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId   int64               `protobuf:"varint,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	ProjectName string              `protobuf:"bytes,2,opt,name=project_name,json=projectName,proto3" json:"project_name,omitempty"`
	ProjectType string              `protobuf:"bytes,3,opt,name=project_type,json=projectType,proto3" json:"project_type,omitempty"` // org, dev
	Action      ProjectEvent_Action `protobuf:"varint,4,opt,name=action,proto3,enum=event.ProjectEvent_Action" json:"action,omitempty"`
}

func (x *ProjectEvent) Reset() {
	*x = ProjectEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectEvent) ProtoMessage() {}

func (x *ProjectEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectEvent.ProtoReflect.Descriptor instead.
func (*ProjectEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{15}
}

func (x *ProjectEvent) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *ProjectEvent) GetProjectName() string {
	if x != nil {
		return x.ProjectName
	}
	return ""
}

func (x *ProjectEvent) GetProjectType() string {
	if x != nil {
		return x.ProjectType
	}
	return ""
}

func (x *ProjectEvent) GetAction() ProjectEvent_Action {
	if x != nil {
		return x.Action
	}
	return ProjectEvent_DEFAULT
}

type DeletePipelineResourceEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	From                  DeletePipelineResourceEvent_DeleteFromEnum `protobuf:"varint,1,opt,name=from,proto3,enum=event.DeletePipelineResourceEvent_DeleteFromEnum" json:"from,omitempty"`
	PipelineId            int64                                      `protobuf:"varint,2,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id,omitempty"`
	PipelineGroupId       int64                                      `protobuf:"varint,3,opt,name=pipeline_group_id,json=pipelineGroupId,proto3" json:"pipeline_group_id,omitempty"`
	TemplateId            int64                                      `protobuf:"varint,4,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	CreateTime            *timestamppb.Timestamp                     `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	ProjectId             int64                                      `protobuf:"varint,6,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	OperatorBy            int64                                      `protobuf:"varint,7,opt,name=operator_by,json=operatorBy,proto3" json:"operator_by,omitempty"`
	OperatorByChineseName string                                     `protobuf:"bytes,8,opt,name=operator_by_chinese_name,json=operatorByChineseName,proto3" json:"operator_by_chinese_name,omitempty"`
	OperatorByEmployeeNo  string                                     `protobuf:"bytes,9,opt,name=operator_by_employee_no,json=operatorByEmployeeNo,proto3" json:"operator_by_employee_no,omitempty"`
}

func (x *DeletePipelineResourceEvent) Reset() {
	*x = DeletePipelineResourceEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePipelineResourceEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePipelineResourceEvent) ProtoMessage() {}

func (x *DeletePipelineResourceEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePipelineResourceEvent.ProtoReflect.Descriptor instead.
func (*DeletePipelineResourceEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{16}
}

func (x *DeletePipelineResourceEvent) GetFrom() DeletePipelineResourceEvent_DeleteFromEnum {
	if x != nil {
		return x.From
	}
	return DeletePipelineResourceEvent_DEFAULT
}

func (x *DeletePipelineResourceEvent) GetPipelineId() int64 {
	if x != nil {
		return x.PipelineId
	}
	return 0
}

func (x *DeletePipelineResourceEvent) GetPipelineGroupId() int64 {
	if x != nil {
		return x.PipelineGroupId
	}
	return 0
}

func (x *DeletePipelineResourceEvent) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *DeletePipelineResourceEvent) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *DeletePipelineResourceEvent) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *DeletePipelineResourceEvent) GetOperatorBy() int64 {
	if x != nil {
		return x.OperatorBy
	}
	return 0
}

func (x *DeletePipelineResourceEvent) GetOperatorByChineseName() string {
	if x != nil {
		return x.OperatorByChineseName
	}
	return ""
}

func (x *DeletePipelineResourceEvent) GetOperatorByEmployeeNo() string {
	if x != nil {
		return x.OperatorByEmployeeNo
	}
	return ""
}

// * PipelineStatusChangeEvent 流水线运行状态变化事件
type PipelineStatusChangeEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"pipelineId"
	PipelineId int64 `protobuf:"varint,1,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id,omitempty"` //* 流水线Id
	// @gotags: json:"pipelineRunId"
	PipelineRunId int64 `protobuf:"varint,2,opt,name=pipeline_run_id,json=pipelineRunId,proto3" json:"pipeline_run_id,omitempty"` //* 流水线运行Id
	// @gotags: json:"appId"
	AppId  int64  `protobuf:"varint,3,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"` //* 流水线触发应用Id
	Name   string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                 //* 流水线名称
	Type   string `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`                 //* 流水线类型
	Status string `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`             //* 流水线运行状态
	// @gotags: json:"triggerEmployeeNo,omitempty"
	TriggerEmployeeNo string `protobuf:"bytes,7,opt,name=trigger_employee_no,json=triggerEmployeeNo,proto3" json:"trigger_employee_no,omitempty"` //* 流水线触发人员工号
	// @gotags: json:"triggerChineseName,omitempty"
	TriggerChineseName string `protobuf:"bytes,8,opt,name=trigger_chinese_name,json=triggerChineseName,proto3" json:"trigger_chinese_name,omitempty"` //* 流水线触发人中文名
	// @gotags: json:"startTime,omitempty"
	StartTime string `protobuf:"bytes,9,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"` //* 流水线运行开始时间
	// @gotags: json:"endTime,omitempty"
	EndTime string `protobuf:"bytes,10,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"` //* 流水线运行结束时间
	// @gotags: json:"triggerMode"
	TriggerMode PipelineStatusChangeEvent_TriggerMode `protobuf:"varint,11,opt,name=trigger_mode,json=triggerMode,proto3,enum=event.PipelineStatusChangeEvent_TriggerMode" json:"trigger_mode,omitempty"` //* 流水线触发模式
	// @gotags: json:"sourceBranch,omitempty"
	SourceBranch *string `protobuf:"bytes,12,opt,name=source_branch,json=sourceBranch,proto3,oneof" json:"source_branch,omitempty"` //* 流水线触发源分支
	Branch       string  `protobuf:"bytes,13,opt,name=branch,proto3" json:"branch,omitempty"`                                       //* 流水线触发分支
	// @gotags: json:"buildNumber"
	BuildNumber int64 `protobuf:"varint,14,opt,name=build_number,json=buildNumber,proto3" json:"build_number,omitempty"` //* 流水线构建号
	// @gotags: json:"repoAddr"
	RepoAddr string `protobuf:"bytes,15,opt,name=repo_addr,json=repoAddr,proto3" json:"repo_addr,omitempty"` //* 流水线触发源代码仓库地址
	// @gotags: json:"appName"
	AppName string `protobuf:"bytes,16,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"` //* 流水线触发应用名称
	// @gotags: json:"projectName"
	ProjectName string `protobuf:"bytes,17,opt,name=project_name,json=projectName,proto3" json:"project_name,omitempty"` //* 流水线所属项目名称
	// @gotags: json:"projectId"
	ProjectId string `protobuf:"bytes,18,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"` //* 流水线所属项目Id
}

func (x *PipelineStatusChangeEvent) Reset() {
	*x = PipelineStatusChangeEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineStatusChangeEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineStatusChangeEvent) ProtoMessage() {}

func (x *PipelineStatusChangeEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineStatusChangeEvent.ProtoReflect.Descriptor instead.
func (*PipelineStatusChangeEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{17}
}

func (x *PipelineStatusChangeEvent) GetPipelineId() int64 {
	if x != nil {
		return x.PipelineId
	}
	return 0
}

func (x *PipelineStatusChangeEvent) GetPipelineRunId() int64 {
	if x != nil {
		return x.PipelineRunId
	}
	return 0
}

func (x *PipelineStatusChangeEvent) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *PipelineStatusChangeEvent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PipelineStatusChangeEvent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *PipelineStatusChangeEvent) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *PipelineStatusChangeEvent) GetTriggerEmployeeNo() string {
	if x != nil {
		return x.TriggerEmployeeNo
	}
	return ""
}

func (x *PipelineStatusChangeEvent) GetTriggerChineseName() string {
	if x != nil {
		return x.TriggerChineseName
	}
	return ""
}

func (x *PipelineStatusChangeEvent) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *PipelineStatusChangeEvent) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *PipelineStatusChangeEvent) GetTriggerMode() PipelineStatusChangeEvent_TriggerMode {
	if x != nil {
		return x.TriggerMode
	}
	return PipelineStatusChangeEvent_AUTO
}

func (x *PipelineStatusChangeEvent) GetSourceBranch() string {
	if x != nil && x.SourceBranch != nil {
		return *x.SourceBranch
	}
	return ""
}

func (x *PipelineStatusChangeEvent) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *PipelineStatusChangeEvent) GetBuildNumber() int64 {
	if x != nil {
		return x.BuildNumber
	}
	return 0
}

func (x *PipelineStatusChangeEvent) GetRepoAddr() string {
	if x != nil {
		return x.RepoAddr
	}
	return ""
}

func (x *PipelineStatusChangeEvent) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *PipelineStatusChangeEvent) GetProjectName() string {
	if x != nil {
		return x.ProjectName
	}
	return ""
}

func (x *PipelineStatusChangeEvent) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

// * TaskStatusChangeEvent 流水线任务运行状态变化事件
type TaskStatusChangeEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                               //* 任务运行Id
	Name      string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                            //* 任务名称
	Type      string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`                            //* 任务类型
	Status    string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`                        //* 任务运行状态
	StartTime string `protobuf:"bytes,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"` //* 任务运行开始时间
	// @gotags: json:"endTime,omitempty"
	EndTime       string `protobuf:"bytes,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`                      //* 任务运行结束时间
	StageRunId    int64  `protobuf:"varint,7,opt,name=stage_run_id,json=stageRunId,proto3" json:"stage_run_id,omitempty"`          //* 任务所属流水线运行阶段Id
	PipelineRunId int64  `protobuf:"varint,8,opt,name=pipeline_run_id,json=pipelineRunId,proto3" json:"pipeline_run_id,omitempty"` //* 任务所属流水线运行Id
	PipelineId    int64  `protobuf:"varint,9,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id,omitempty"`            //* 任务所属流水线Id
}

func (x *TaskStatusChangeEvent) Reset() {
	*x = TaskStatusChangeEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskStatusChangeEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskStatusChangeEvent) ProtoMessage() {}

func (x *TaskStatusChangeEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskStatusChangeEvent.ProtoReflect.Descriptor instead.
func (*TaskStatusChangeEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{18}
}

func (x *TaskStatusChangeEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TaskStatusChangeEvent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TaskStatusChangeEvent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TaskStatusChangeEvent) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *TaskStatusChangeEvent) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *TaskStatusChangeEvent) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *TaskStatusChangeEvent) GetStageRunId() int64 {
	if x != nil {
		return x.StageRunId
	}
	return 0
}

func (x *TaskStatusChangeEvent) GetPipelineRunId() int64 {
	if x != nil {
		return x.PipelineRunId
	}
	return 0
}

func (x *TaskStatusChangeEvent) GetPipelineId() int64 {
	if x != nil {
		return x.PipelineId
	}
	return 0
}

// * SubTaskStatusChangeEvent 流水线子任务运行状态变化事件
type SubTaskStatusChangeEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                               //* 子任务运行Id
	Name      string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                            //* 子任务名称
	Type      string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`                            //* 子任务类型
	Status    string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`                        //* 子任务运行状态
	StartTime string `protobuf:"bytes,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"` //* 子任务运行开始时间
	// @gotags: json:"endTime,omitempty"
	EndTime string `protobuf:"bytes,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"` //* 子任务运行结束时间
	// @gotags: json:"taskRunId"
	TaskRunId int64 `protobuf:"varint,7,opt,name=task_run_id,json=taskRunId,proto3" json:"task_run_id,omitempty"` //* 子任务所属任务运行Id
	// @gotags: json:"stageRunId"
	StageRunId int64 `protobuf:"varint,8,opt,name=stage_run_id,json=stageRunId,proto3" json:"stage_run_id,omitempty"` //* 子任务所属流水线运行阶段Id
	// @gotags: json:"pipelineRunId"
	PipelineRunId int64 `protobuf:"varint,9,opt,name=pipeline_run_id,json=pipelineRunId,proto3" json:"pipeline_run_id,omitempty"` //* 子任务所属流水线运行Id
	// @gotags: json:"pipelineId"
	PipelineId int64 `protobuf:"varint,10,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id,omitempty"` //* 子任务所属流水线Id
}

func (x *SubTaskStatusChangeEvent) Reset() {
	*x = SubTaskStatusChangeEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubTaskStatusChangeEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubTaskStatusChangeEvent) ProtoMessage() {}

func (x *SubTaskStatusChangeEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubTaskStatusChangeEvent.ProtoReflect.Descriptor instead.
func (*SubTaskStatusChangeEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{19}
}

func (x *SubTaskStatusChangeEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SubTaskStatusChangeEvent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SubTaskStatusChangeEvent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *SubTaskStatusChangeEvent) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SubTaskStatusChangeEvent) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *SubTaskStatusChangeEvent) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *SubTaskStatusChangeEvent) GetTaskRunId() int64 {
	if x != nil {
		return x.TaskRunId
	}
	return 0
}

func (x *SubTaskStatusChangeEvent) GetStageRunId() int64 {
	if x != nil {
		return x.StageRunId
	}
	return 0
}

func (x *SubTaskStatusChangeEvent) GetPipelineRunId() int64 {
	if x != nil {
		return x.PipelineRunId
	}
	return 0
}

func (x *SubTaskStatusChangeEvent) GetPipelineId() int64 {
	if x != nil {
		return x.PipelineId
	}
	return 0
}

// * AppDeployActionEvent 服务部署动作事件
type AppDeployActionEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"appId,omitempty"
	AppId int64 `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"` //* 服务Id
	// @gotags: json:"appName,omitempty"
	AppName string `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"` //* 服务名称
	// @gotags: json:"workloadName,omitempty"
	WorkloadName string `protobuf:"bytes,3,opt,name=workload_name,json=workloadName,proto3" json:"workload_name,omitempty"` //* 容器应用负载workload名称
	// @gotags: json:"cmdbId,omitempty"
	CmdbId string `protobuf:"bytes,4,opt,name=cmdb_id,json=cmdbId,proto3" json:"cmdb_id,omitempty"` //* cmdbId
	// @gotags: json:"action"
	Action AppDeployActionEvent_Action `protobuf:"varint,5,opt,name=action,proto3,enum=event.AppDeployActionEvent_Action" json:"action,omitempty"` //* 服务部署动作
	// @gotags: json:"status"
	Status    AppDeployActionEvent_Status `protobuf:"varint,6,opt,name=status,proto3,enum=event.AppDeployActionEvent_Status" json:"status,omitempty"` //* 服务部署动作状态
	Cluster   string                      `protobuf:"bytes,7,opt,name=cluster,proto3" json:"cluster,omitempty"`                                       //* 服务部署集群
	Namespace string                      `protobuf:"bytes,8,opt,name=namespace,proto3" json:"namespace,omitempty"`                                   //* 服务部署命名空间
	// @gotags: json:"senv,omitempty"
	Senv    string `protobuf:"bytes,9,opt,name=senv,proto3" json:"senv,omitempty"`        //* 服务部署子环境标签
	Version string `protobuf:"bytes,10,opt,name=version,proto3" json:"version,omitempty"` //* 服务部署版本
	Env     string `protobuf:"bytes,11,opt,name=env,proto3" json:"env,omitempty"`         //* 服务部署环境
	// @gotags: json:"envTarget,omitempty"
	EnvTarget string `protobuf:"bytes,12,opt,name=env_target,json=envTarget,proto3" json:"env_target,omitempty"` //* 服务部署环境目标
	// @gotags: json:"operatorEmployeeNo,omitempty"
	OperatorEmployeeNo string `protobuf:"bytes,13,opt,name=operator_employee_no,json=operatorEmployeeNo,proto3" json:"operator_employee_no,omitempty"` //* 服务部署操作人员工号
	// @gotags: json:"operatorChineseName,omitempty"
	OperatorChineseName string `protobuf:"bytes,14,opt,name=operator_chinese_name,json=operatorChineseName,proto3" json:"operator_chinese_name,omitempty"` //* 服务部署操作人中文名
}

func (x *AppDeployActionEvent) Reset() {
	*x = AppDeployActionEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppDeployActionEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppDeployActionEvent) ProtoMessage() {}

func (x *AppDeployActionEvent) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppDeployActionEvent.ProtoReflect.Descriptor instead.
func (*AppDeployActionEvent) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{20}
}

func (x *AppDeployActionEvent) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *AppDeployActionEvent) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *AppDeployActionEvent) GetWorkloadName() string {
	if x != nil {
		return x.WorkloadName
	}
	return ""
}

func (x *AppDeployActionEvent) GetCmdbId() string {
	if x != nil {
		return x.CmdbId
	}
	return ""
}

func (x *AppDeployActionEvent) GetAction() AppDeployActionEvent_Action {
	if x != nil {
		return x.Action
	}
	return AppDeployActionEvent_DEPLOY
}

func (x *AppDeployActionEvent) GetStatus() AppDeployActionEvent_Status {
	if x != nil {
		return x.Status
	}
	return AppDeployActionEvent_SUCCESS
}

func (x *AppDeployActionEvent) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *AppDeployActionEvent) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *AppDeployActionEvent) GetSenv() string {
	if x != nil {
		return x.Senv
	}
	return ""
}

func (x *AppDeployActionEvent) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *AppDeployActionEvent) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *AppDeployActionEvent) GetEnvTarget() string {
	if x != nil {
		return x.EnvTarget
	}
	return ""
}

func (x *AppDeployActionEvent) GetOperatorEmployeeNo() string {
	if x != nil {
		return x.OperatorEmployeeNo
	}
	return ""
}

func (x *AppDeployActionEvent) GetOperatorChineseName() string {
	if x != nil {
		return x.OperatorChineseName
	}
	return ""
}

type ApprovalHandleEvent_DeployEnv struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"envTarget,omitempty"
	EnvTarget string `protobuf:"bytes,1,opt,name=env_target,json=envTarget,proto3" json:"env_target,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Senv      string `protobuf:"bytes,4,opt,name=senv,proto3" json:"senv,omitempty"`
}

func (x *ApprovalHandleEvent_DeployEnv) Reset() {
	*x = ApprovalHandleEvent_DeployEnv{}
	if protoimpl.UnsafeEnabled {
		mi := &file_event_event_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApprovalHandleEvent_DeployEnv) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApprovalHandleEvent_DeployEnv) ProtoMessage() {}

func (x *ApprovalHandleEvent_DeployEnv) ProtoReflect() protoreflect.Message {
	mi := &file_event_event_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApprovalHandleEvent_DeployEnv.ProtoReflect.Descriptor instead.
func (*ApprovalHandleEvent_DeployEnv) Descriptor() ([]byte, []int) {
	return file_event_event_proto_rawDescGZIP(), []int{5, 0}
}

func (x *ApprovalHandleEvent_DeployEnv) GetEnvTarget() string {
	if x != nil {
		return x.EnvTarget
	}
	return ""
}

func (x *ApprovalHandleEvent_DeployEnv) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ApprovalHandleEvent_DeployEnv) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ApprovalHandleEvent_DeployEnv) GetSenv() string {
	if x != nil {
		return x.Senv
	}
	return ""
}

var File_event_event_proto protoreflect.FileDescriptor

var file_event_event_proto_rawDesc = []byte{
	0x0a, 0x11, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe2, 0x03, 0x0a, 0x10, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3d,
	0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x41, 0x0a,
	0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x3c, 0x0a, 0x0c, 0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0b, 0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49,
	0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x22, 0x0a, 0x0d, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x42, 0x79, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x17, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f,
	0x62, 0x79, 0x5f, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x42, 0x79,
	0x43, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x62,
	0x75, 0x69, 0x6c, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x8f,
	0x04, 0x0a, 0x0c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x3d, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x41,
	0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x31, 0x0a,
	0x15, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64,
	0x12, 0x31, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x12, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x49, 0x64,
	0x22, 0xa5, 0x04, 0x0a, 0x0f, 0x53, 0x75, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x65, 0x6c, 0x61, 0x70, 0x73, 0x65,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x31, 0x0a, 0x15, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x72,
	0x75, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x12, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x72, 0x75,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b,
	0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52,
	0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73,
	0x75, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0xc7, 0x02, 0x0a, 0x0d, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x52, 0x75, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x0c,
	0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65,
	0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e,
	0x49, 0x64, 0x22, 0xaf, 0x02, 0x0a, 0x13, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x66, 0x6c, 0x6f, 0x77, 0x5f,
	0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x66,
	0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x6f, 0x64,
	0x65, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x6f, 0x70, 0x69, 0x6e, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6f, 0x70, 0x69, 0x6e, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x22, 0xc4, 0x05, 0x0a, 0x13, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61,
	0x6c, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x5f, 0x73, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06,
	0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72,
	0x61, 0x6e, 0x63, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x6e,
	0x6f, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x72, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x09, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x72, 0x73, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x5f, 0x65, 0x6e, 0x76, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a,
	0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x45, 0x6e, 0x76, 0x73, 0x12, 0x29, 0x0a, 0x11, 0x66, 0x6c,
	0x6f, 0x77, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x6f, 0x64, 0x65, 0x49,
	0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x18, 0x0d, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x61, 0x6c, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x45, 0x6e, 0x76, 0x52, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x61, 0x70, 0x70, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x61,
	0x70, 0x70, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x1a, 0x76, 0x0a, 0x09, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x45, 0x6e, 0x76,
	0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x76, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6e, 0x76, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x6e, 0x76, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x65, 0x6e, 0x76, 0x22, 0x9f, 0x06, 0x0a, 0x14,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x76, 0x5f, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x6e, 0x76, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12,
	0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x15, 0x0a,
	0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x44, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49,
	0x44, 0x12, 0x1e, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x49,
	0x44, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69,
	0x73, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x42, 0x79, 0x12, 0x37, 0x0a, 0x18, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x62, 0x79, 0x5f, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x42, 0x79, 0x43, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x17, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x62, 0x79, 0x5f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x6e, 0x6f, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x42,
	0x79, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x4e, 0x6f, 0x12, 0x3b, 0x0a, 0x0b, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x49, 0x44, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x72, 0x74, 0x69,
	0x66, 0x61, 0x63, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x3a, 0x0a, 0x06, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x4c, 0x6f, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52,
	0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x6e, 0x76, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x65, 0x6e, 0x76, 0x22, 0x20, 0x0a, 0x06, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x10,
	0x00, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0x01, 0x22, 0x8c, 0x03,
	0x0a, 0x12, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x43, 0x66, 0x67, 0x54, 0x6d, 0x70, 0x6c, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x44, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x35, 0x0a, 0x17, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x43, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x33, 0x0a, 0x16, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x6e, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x13, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x45, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x65, 0x65, 0x4e, 0x6f, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xd7, 0x01, 0x0a,
	0x18, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x43, 0x66, 0x67, 0x54, 0x6d, 0x70, 0x6c, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x39, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x23, 0x0a, 0x11, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0xa3, 0x02, 0x0a, 0x16,
	0x43, 0x68, 0x53, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x5f, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x65, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x1e,
	0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x73, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x12, 0x2d, 0x0a, 0x13, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x10, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49,
	0x64, 0x22, 0x2d, 0x0a, 0x1b, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x22, 0xcd, 0x01, 0x0a, 0x12, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x65, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x73, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x53, 0x65, 0x74, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x13, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x65, 0x74, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x69, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0e, 0x69, 0x73, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x22, 0x8f, 0x02, 0x0a, 0x11, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x65, 0x74, 0x52, 0x75,
	0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x65, 0x6c, 0x61, 0x70,
	0x73, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x6c, 0x61, 0x70, 0x73,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x84, 0x01, 0x0a, 0x10, 0x43, 0x61, 0x6e, 0x61, 0x72, 0x79, 0x53, 0x68, 0x69,
	0x66, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x61,
	0x73, 0x6b, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x04, 0x73, 0x74, 0x65, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x6d, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6d, 0x61, 0x78, 0x22, 0xe2, 0x01, 0x0a, 0x0c, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x32, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1a, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x39, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0b, 0x0a,
	0x07, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x03, 0x22, 0x8e,
	0x04, 0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x45,
	0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x45, 0x6e, 0x75, 0x6d, 0x52,
	0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x42, 0x79,
	0x12, 0x37, 0x0a, 0x18, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x62, 0x79, 0x5f,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x15, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x42, 0x79, 0x43, 0x68,
	0x69, 0x6e, 0x65, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x17, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x62, 0x79, 0x5f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65,
	0x65, 0x5f, 0x6e, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x42, 0x79, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x4e, 0x6f,
	0x22, 0x4d, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12,
	0x0c, 0x0a, 0x08, 0x50, 0x49, 0x50, 0x45, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x12, 0x0a,
	0x0e, 0x50, 0x49, 0x50, 0x45, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x10,
	0x02, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x10, 0x03, 0x22,
	0xbe, 0x05, 0x0a, 0x19, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x26,
	0x0a, 0x0f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a,
	0x13, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65,
	0x65, 0x5f, 0x6e, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x4e, 0x6f, 0x12, 0x30, 0x0a,
	0x14, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x74, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x43, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4f, 0x0a, 0x0c, 0x74, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2c, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x74,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x0d, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63,
	0x68, 0x88, 0x01, 0x01, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x21, 0x0a, 0x0c,
	0x62, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x41, 0x64, 0x64, 0x72, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x23, 0x0a, 0x0b, 0x54, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x41, 0x55, 0x54, 0x4f,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x10, 0x01, 0x42, 0x10,
	0x0a, 0x0e, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x22, 0x8c, 0x02, 0x0a, 0x15, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x75,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x22,
	0xaf, 0x02, 0x0a, 0x18, 0x53, 0x75, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x72,
	0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x61, 0x73,
	0x6b, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f,
	0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49,
	0x64, 0x22, 0xe7, 0x04, 0x0a, 0x14, 0x41, 0x70, 0x70, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6d, 0x64, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x6d, 0x64, 0x62, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x41,
	0x70, 0x70, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65,
	0x6e, 0x76, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x65, 0x6e, 0x76, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x76, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6e,
	0x76, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x65, 0x6e, 0x76, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x6e,
	0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x4e, 0x6f, 0x12, 0x32, 0x0a, 0x15, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x43, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0x47, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x45, 0x50,
	0x4c, 0x4f, 0x59, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x4f, 0x4c, 0x4c, 0x42, 0x41, 0x43,
	0x4b, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x02,
	0x12, 0x0b, 0x0a, 0x07, 0x52, 0x45, 0x53, 0x54, 0x41, 0x52, 0x54, 0x10, 0x03, 0x12, 0x09, 0x0a,
	0x05, 0x52, 0x45, 0x54, 0x52, 0x59, 0x10, 0x04, 0x22, 0x21, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x00, 0x12,
	0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x42, 0x31, 0x5a, 0x2f, 0x67,
	0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x61, 0x72, 0x6d, 0x6f,
	0x6e, 0x79, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x3b, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_event_event_proto_rawDescOnce sync.Once
	file_event_event_proto_rawDescData = file_event_event_proto_rawDesc
)

func file_event_event_proto_rawDescGZIP() []byte {
	file_event_event_proto_rawDescOnce.Do(func() {
		file_event_event_proto_rawDescData = protoimpl.X.CompressGZIP(file_event_event_proto_rawDescData)
	})
	return file_event_event_proto_rawDescData
}

var file_event_event_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_event_event_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_event_event_proto_goTypes = []interface{}{
	(DeployChangeLogEvent_Method)(0),                // 0: event.DeployChangeLogEvent.Method
	(ProjectEvent_Action)(0),                        // 1: event.ProjectEvent.Action
	(DeletePipelineResourceEvent_DeleteFromEnum)(0), // 2: event.DeletePipelineResourceEvent.DeleteFromEnum
	(PipelineStatusChangeEvent_TriggerMode)(0),      // 3: event.PipelineStatusChangeEvent.TriggerMode
	(AppDeployActionEvent_Action)(0),                // 4: event.AppDeployActionEvent.Action
	(AppDeployActionEvent_Status)(0),                // 5: event.AppDeployActionEvent.Status
	(*PipelineRunEvent)(nil),                        // 6: event.PipelineRunEvent
	(*TaskRunEvent)(nil),                            // 7: event.TaskRunEvent
	(*SubTaskRunEvent)(nil),                         // 8: event.SubTaskRunEvent
	(*StageRunEvent)(nil),                           // 9: event.StageRunEvent
	(*ApprovalUpdateEvent)(nil),                     // 10: event.ApprovalUpdateEvent
	(*ApprovalHandleEvent)(nil),                     // 11: event.ApprovalHandleEvent
	(*DeployChangeLogEvent)(nil),                    // 12: event.DeployChangeLogEvent
	(*DeployCfgTmplEvent)(nil),                      // 13: event.DeployCfgTmplEvent
	(*DeployCfgTmplChangeEvent)(nil),                // 14: event.DeployCfgTmplChangeEvent
	(*TicketUpdateEvent)(nil),                       // 15: event.TicketUpdateEvent
	(*ChSetPipelineTaskEvent)(nil),                  // 16: event.ChSetPipelineTaskEvent
	(*PipelineTemplateChangeEvent)(nil),             // 17: event.PipelineTemplateChangeEvent
	(*ChangeSetTaskEvent)(nil),                      // 18: event.ChangeSetTaskEvent
	(*ChangeSetRunEvent)(nil),                       // 19: event.ChangeSetRunEvent
	(*CanaryShiftEvent)(nil),                        // 20: event.CanaryShiftEvent
	(*ProjectEvent)(nil),                            // 21: event.ProjectEvent
	(*DeletePipelineResourceEvent)(nil),             // 22: event.DeletePipelineResourceEvent
	(*PipelineStatusChangeEvent)(nil),               // 23: event.PipelineStatusChangeEvent
	(*TaskStatusChangeEvent)(nil),                   // 24: event.TaskStatusChangeEvent
	(*SubTaskStatusChangeEvent)(nil),                // 25: event.SubTaskStatusChangeEvent
	(*AppDeployActionEvent)(nil),                    // 26: event.AppDeployActionEvent
	(*ApprovalHandleEvent_DeployEnv)(nil),           // 27: event.ApprovalHandleEvent.DeployEnv
	(*timestamppb.Timestamp)(nil),                   // 28: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),                     // 29: google.protobuf.Duration
	(*structpb.Struct)(nil),                         // 30: google.protobuf.Struct
}
var file_event_event_proto_depIdxs = []int32{
	28, // 0: event.PipelineRunEvent.started_time:type_name -> google.protobuf.Timestamp
	28, // 1: event.PipelineRunEvent.completed_time:type_name -> google.protobuf.Timestamp
	29, // 2: event.PipelineRunEvent.elapsed_time:type_name -> google.protobuf.Duration
	28, // 3: event.TaskRunEvent.started_time:type_name -> google.protobuf.Timestamp
	28, // 4: event.TaskRunEvent.completed_time:type_name -> google.protobuf.Timestamp
	29, // 5: event.TaskRunEvent.elapsed_time:type_name -> google.protobuf.Duration
	30, // 6: event.TaskRunEvent.results:type_name -> google.protobuf.Struct
	28, // 7: event.SubTaskRunEvent.started_time:type_name -> google.protobuf.Timestamp
	28, // 8: event.SubTaskRunEvent.completed_time:type_name -> google.protobuf.Timestamp
	29, // 9: event.SubTaskRunEvent.elapsed_time:type_name -> google.protobuf.Duration
	30, // 10: event.SubTaskRunEvent.results:type_name -> google.protobuf.Struct
	28, // 11: event.StageRunEvent.started_time:type_name -> google.protobuf.Timestamp
	28, // 12: event.StageRunEvent.completed_time:type_name -> google.protobuf.Timestamp
	29, // 13: event.StageRunEvent.elapsed_time:type_name -> google.protobuf.Duration
	28, // 14: event.ApprovalHandleEvent.created_at:type_name -> google.protobuf.Timestamp
	27, // 15: event.ApprovalHandleEvent.envs:type_name -> event.ApprovalHandleEvent.DeployEnv
	28, // 16: event.DeployChangeLogEvent.operated_at:type_name -> google.protobuf.Timestamp
	0,  // 17: event.DeployChangeLogEvent.method:type_name -> event.DeployChangeLogEvent.Method
	28, // 18: event.DeployCfgTmplEvent.created_at:type_name -> google.protobuf.Timestamp
	28, // 19: event.DeployCfgTmplEvent.updated_at:type_name -> google.protobuf.Timestamp
	28, // 20: event.DeployCfgTmplChangeEvent.updated_at:type_name -> google.protobuf.Timestamp
	28, // 21: event.ChangeSetRunEvent.started_time:type_name -> google.protobuf.Timestamp
	28, // 22: event.ChangeSetRunEvent.completed_time:type_name -> google.protobuf.Timestamp
	29, // 23: event.ChangeSetRunEvent.elapsed_time:type_name -> google.protobuf.Duration
	1,  // 24: event.ProjectEvent.action:type_name -> event.ProjectEvent.Action
	2,  // 25: event.DeletePipelineResourceEvent.from:type_name -> event.DeletePipelineResourceEvent.DeleteFromEnum
	28, // 26: event.DeletePipelineResourceEvent.create_time:type_name -> google.protobuf.Timestamp
	3,  // 27: event.PipelineStatusChangeEvent.trigger_mode:type_name -> event.PipelineStatusChangeEvent.TriggerMode
	4,  // 28: event.AppDeployActionEvent.action:type_name -> event.AppDeployActionEvent.Action
	5,  // 29: event.AppDeployActionEvent.status:type_name -> event.AppDeployActionEvent.Status
	30, // [30:30] is the sub-list for method output_type
	30, // [30:30] is the sub-list for method input_type
	30, // [30:30] is the sub-list for extension type_name
	30, // [30:30] is the sub-list for extension extendee
	0,  // [0:30] is the sub-list for field type_name
}

func init() { file_event_event_proto_init() }
func file_event_event_proto_init() {
	if File_event_event_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_event_event_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineRunEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskRunEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubTaskRunEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageRunEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApprovalUpdateEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApprovalHandleEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeployChangeLogEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeployCfgTmplEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeployCfgTmplChangeEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TicketUpdateEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChSetPipelineTaskEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineTemplateChangeEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeSetTaskEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeSetRunEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CanaryShiftEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePipelineResourceEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineStatusChangeEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskStatusChangeEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubTaskStatusChangeEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppDeployActionEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_event_event_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApprovalHandleEvent_DeployEnv); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_event_event_proto_msgTypes[17].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_event_event_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_event_event_proto_goTypes,
		DependencyIndexes: file_event_event_proto_depIdxs,
		EnumInfos:         file_event_event_proto_enumTypes,
		MessageInfos:      file_event_event_proto_msgTypes,
	}.Build()
	File_event_event_proto = out.File
	file_event_event_proto_rawDesc = nil
	file_event_event_proto_goTypes = nil
	file_event_event_proto_depIdxs = nil
}
