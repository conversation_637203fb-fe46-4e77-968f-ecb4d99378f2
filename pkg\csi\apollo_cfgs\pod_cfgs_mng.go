package apollo_cfgs

import (
	"fmt"
	"os"
	"sync"

	"github.com/apolloconfig/agollo/v4/env/config"
)

const (
	FileCfgKey = "content"
)

type DyCfg struct {
	CfgFile   string `json:"cfgFile"`   // 配置子目录&文件名
	ApolloNS  string `json:"apolloNS"`  // apollo namespace
	IsGlobal  bool   `json:"isGlobal"`  // 是否全局配置
	ProjectID int64  `json:"projectId"` // 项目ID
	UserGroup string `json:"userGroup"` // 用户群组
}

type PodCfgsInfo struct {
	AppName string  `json:"appName"`
	Env     int     `json:"env"`    // deploy_change_log 表env字段  部署环境: 1开发、2测试、3灰度、4生产
	EnvTage int     `json:"envTag"` // deploy_change_log 表env_tag字段 部署环境目标：1基准环境、2子环境
	Subenv  string  `json:"subEnv"` // 子环境名称
	DyCfgs  []DyCfg `json:"dyCfgs"`
}

type PodInfo struct {
	Namespace  string `json:"namespace"`
	Name       string `json:"name"`
	UID        string `json:"uid"`
	PvNodePath string `json:"pvNodePath"` // pv对应节点宿主机上的源路径
}

type SyncPodCfgsArgs struct {
	PodInfo     PodInfo     `json:"podInfo"`
	PodCfgsInfo PodCfgsInfo `json:"podCfgsInfo"`
}

type IPodCfgsMng interface {
	StartPodCfgs(args SyncPodCfgsArgs) (err error)
	StopPodCfgs(podId string)
}

// 管理节点配置的下载、监听，以及 取消监听
type podCfgsMng struct {
	*apolloClientMng
}

func InitPodCfgsMng() (mng IPodCfgsMng, err error) {
	apolloMng, err := InitApolloClientMng()
	if err != nil {

		return
	}
	cfgsMng := &podCfgsMng{
		apolloClientMng: apolloMng,
	}
	return cfgsMng, nil
}

func (m *podCfgsMng) StartPodCfgs(args SyncPodCfgsArgs) (err error) {
	podListeners := make(map[string]*nodeCfgListener, len(args.PodCfgsInfo.DyCfgs))
	for _, dyCfg := range args.PodCfgsInfo.DyCfgs {
		apolloNS := &config.AppConfig{
			AppID:          m.apolloAppIds[args.PodCfgsInfo.Env],
			Cluster:        "default",
			IP:             m.apolloIP,
			NamespaceName:  dyCfg.ApolloNS,
			IsBackupConfig: true,
			Secret:         m.apolloAppSecrets[args.PodCfgsInfo.Env],
			Label:          args.PodCfgsInfo.AppName,
			MustStart:      true,
		}

		listener := &nodeCfgListener{}
		err = listener.Init(apolloNS, args.PodInfo.PvNodePath, dyCfg)
		if err != nil {
			return
		}

		podListeners[dyCfg.ApolloNS] = listener
	}

	m.AddPodListeners(args.PodInfo.UID, podListeners)

	return
}

func (m *podCfgsMng) StopPodCfgs(podId string) {
	listeners := m.GetPodListeners(podId)
	if listeners == nil {
		return
	}

	for _, listener := range listeners {
		listener.Close()
	}

	m.DelPodListeners(podId)
}

/*-----------------------------------------------------------------------------------------------------*/

type apolloClientMng struct {
	mu               *sync.RWMutex
	cache            map[string]map[string]*nodeCfgListener
	apolloAppIds     map[int]string
	apolloAppSecrets map[int]string
	apolloIP         string
}

func InitApolloClientMng() (mng *apolloClientMng, err error) {
	cfgsMng := &apolloClientMng{
		mu:               &sync.RWMutex{},
		cache:            make(map[string]map[string]*nodeCfgListener),
		apolloAppIds:     make(map[int]string, 4),
		apolloAppSecrets: make(map[int]string, 4),
	}

	cfgsMng.apolloIP = os.Getenv("APOLLO_IP")
	if cfgsMng.apolloIP == "" {
		err = fmt.Errorf("ENV APOLLO_IP must be provided")
		return
	}

	for i := 1; i < 5; i++ {
		appId := os.Getenv(fmt.Sprintf("APOLLO_APPID_%d", i))
		if appId == "" {
			err = fmt.Errorf("ENV APOLLO_APPID_%d must be provided", i)
			return
		}
		cfgsMng.apolloAppIds[i] = appId

		appSecret := os.Getenv(fmt.Sprintf("APOLLO_APPSECRET_%d", i))
		if appSecret == "" {
			err = fmt.Errorf("ENV APOLLO_APPSECRET_%d must be provided", i)
			return
		}
		cfgsMng.apolloAppSecrets[i] = appSecret
	}

	return cfgsMng, nil
}

func (m *apolloClientMng) AddPodListeners(podID string, cfgs map[string]*nodeCfgListener) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.cache[podID] = cfgs
}

func (m *apolloClientMng) GetPodListeners(podID string) map[string]*nodeCfgListener {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.cache[podID]
}

func (m *apolloClientMng) DelPodListeners(podID string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	delete(m.cache, podID)
}
