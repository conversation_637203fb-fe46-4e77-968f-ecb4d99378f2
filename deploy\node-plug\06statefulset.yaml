apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app.kubernetes.io/component: controller
    app.kubernetes.io/name: ttcicd-csi-controller
  name: ttcicd-csi-controller
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ttcicd-csi-controller
  serviceName: cicd-csi-controller
  template:
    metadata:
      labels:
        app: ttcicd-csi-controller
    spec:
      priorityClassName: system-cluster-critical
      serviceAccountName: cicd-csi-controller
      tolerations:
        - key: CriticalAddonsOnly
          operator: Exists
      containers:
        - args:
            - --endpoint=$(CSI_ENDPOINT)
            - --nodeid=$(NODE_NAME)
          env:
            - name: CSI_ENDPOINT
              value: unix:///var/lib/csi/sockets/pluginproxy/csi.sock
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          image: cr.ttyuyin.com/devops/overseas/cicd-csi/node-plug:v0.4.0
          livenessProbe:
            failureThreshold: 5
            httpGet:
              path: /healthz
              port: healthz
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 3
          name: csi-plugin
          ports:
            - containerPort: 9909
              name: healthz
              protocol: TCP
          securityContext:
            capabilities:
              add:
                - SYS_ADMIN
            privileged: true
          volumeMounts:
            - mountPath: /var/lib/csi/sockets/pluginproxy/
              name: socket-dir
            - mountPath: /mnt
              name: mnt
        - args:
            - --csi-address=$(ADDRESS)
            - --timeout=60s
            - --v=5
          env:
            - name: ADDRESS
              value: /var/lib/csi/sockets/pluginproxy/csi.sock
          image: cr.ttyuyin.com/devops/overseas/k8scsi-csi-provisioner:v2.1.0
          name: csi-provisioner
          volumeMounts:
            - mountPath: /var/lib/csi/sockets/pluginproxy/
              name: socket-dir
        - args:
            - --csi-address=$(ADDRESS)
            - --health-port=$(HEALTH_PORT)
          env:
            - name: ADDRESS
              value: /csi/csi.sock
            - name: HEALTH_PORT
              value: "9909"
          image: cr.ttyuyin.com/devops/overseas/k8scsi-livenessprobe:v1.1.0
          name: liveness-probe
          volumeMounts:
            - mountPath: /csi
              name: socket-dir
      volumes:
        - emptyDir: {}
          name: socket-dir
        - hostPath:
            path: /mnt
            type: DirectoryOrCreate
          name: mnt
