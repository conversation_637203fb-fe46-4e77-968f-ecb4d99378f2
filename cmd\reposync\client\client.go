package client

import (
	"context"
	"time"

	"google.golang.org/grpc"
	grpcinsecure "google.golang.org/grpc/credentials/insecure"
)

type Option func(o *options)

type options struct {
	timeout time.Duration
}

func WithTimeout(t time.Duration) Option {
	return func(o *options) {
		o.timeout = t
	}
}

func NewClientConn(target string, opts ...Option) (grpc.ClientConnInterface, error) {
	conn, err := dial(target, opts)
	if err != nil {
		return nil, err
	}

	cli := &clientConn{
		target:  target,
		options: opts,
		conn:    conn,
	}
	return cli, nil
}

type clientConn struct {
	target  string
	options []Option
	conn    *grpc.ClientConn
}

func (c *clientConn) Close() error {
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}

func (c *clientConn) Invoke(ctx context.Context, method string, args any, reply any, opts ...grpc.CallOption) error {
	return c.conn.Invoke(ctx, method, args, reply, opts...)
}

func (c *clientConn) NewStream(ctx context.Context, desc *grpc.StreamDesc, method string, opts ...grpc.CallOption) (grpc.ClientStream, error) {
	return c.conn.NewStream(ctx, desc, method, opts...)
}

func dial(target string, clientOptions []Option) (*grpc.ClientConn, error) {
	grpcOpts := []grpc.DialOption{
		grpc.WithTransportCredentials(grpcinsecure.NewCredentials()),
	}

	opts := &options{
		timeout: 500 * time.Millisecond,
	}
	for _, opt := range clientOptions {
		opt(opts)
	}

	ctx, cancel := context.WithTimeout(context.Background(), opts.timeout)
	defer cancel()

	conn, err := grpc.DialContext(ctx, target, grpcOpts...)
	if err != nil {
		return nil, err
	}
	return conn, nil
}
