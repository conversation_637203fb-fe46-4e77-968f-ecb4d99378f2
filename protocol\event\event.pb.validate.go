// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: event/event.proto

package event

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PipelineRunEvent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PipelineRunEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PipelineRunEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PipelineRunEventMultiError, or nil if none found.
func (m *PipelineRunEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *PipelineRunEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetStartedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PipelineRunEventValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PipelineRunEventValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PipelineRunEventValidationError{
				field:  "StartedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PipelineRunEventValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PipelineRunEventValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PipelineRunEventValidationError{
				field:  "CompletedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetElapsedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PipelineRunEventValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PipelineRunEventValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetElapsedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PipelineRunEventValidationError{
				field:  "ElapsedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Name

	// no validation rules for Namespace

	// no validation rules for PipelineId

	// no validation rules for AppId

	// no validation rules for Status

	// no validation rules for TriggerById

	// no validation rules for TriggerByChineseName

	// no validation rules for BuildNumber

	if len(errors) > 0 {
		return PipelineRunEventMultiError(errors)
	}

	return nil
}

// PipelineRunEventMultiError is an error wrapping multiple validation errors
// returned by PipelineRunEvent.ValidateAll() if the designated constraints
// aren't met.
type PipelineRunEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PipelineRunEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PipelineRunEventMultiError) AllErrors() []error { return m }

// PipelineRunEventValidationError is the validation error returned by
// PipelineRunEvent.Validate if the designated constraints aren't met.
type PipelineRunEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PipelineRunEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PipelineRunEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PipelineRunEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PipelineRunEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PipelineRunEventValidationError) ErrorName() string { return "PipelineRunEventValidationError" }

// Error satisfies the builtin error interface
func (e PipelineRunEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPipelineRunEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PipelineRunEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PipelineRunEventValidationError{}

// Validate checks the field values on TaskRunEvent with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TaskRunEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskRunEvent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TaskRunEventMultiError, or
// nil if none found.
func (m *TaskRunEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskRunEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetStartedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TaskRunEventValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TaskRunEventValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TaskRunEventValidationError{
				field:  "StartedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TaskRunEventValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TaskRunEventValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TaskRunEventValidationError{
				field:  "CompletedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetElapsedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TaskRunEventValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TaskRunEventValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetElapsedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TaskRunEventValidationError{
				field:  "ElapsedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Name

	// no validation rules for Namespace

	// no validation rules for Type

	// no validation rules for Status

	// no validation rules for PipelineRunStageId

	if all {
		switch v := interface{}(m.GetResults()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TaskRunEventValidationError{
					field:  "Results",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TaskRunEventValidationError{
					field:  "Results",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResults()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TaskRunEventValidationError{
				field:  "Results",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PipelineRunId

	// no validation rules for Reason

	// no validation rules for TicketTaskRunId

	if len(errors) > 0 {
		return TaskRunEventMultiError(errors)
	}

	return nil
}

// TaskRunEventMultiError is an error wrapping multiple validation errors
// returned by TaskRunEvent.ValidateAll() if the designated constraints aren't met.
type TaskRunEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskRunEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskRunEventMultiError) AllErrors() []error { return m }

// TaskRunEventValidationError is the validation error returned by
// TaskRunEvent.Validate if the designated constraints aren't met.
type TaskRunEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskRunEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskRunEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskRunEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskRunEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskRunEventValidationError) ErrorName() string { return "TaskRunEventValidationError" }

// Error satisfies the builtin error interface
func (e TaskRunEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskRunEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskRunEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskRunEventValidationError{}

// Validate checks the field values on SubTaskRunEvent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SubTaskRunEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubTaskRunEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubTaskRunEventMultiError, or nil if none found.
func (m *SubTaskRunEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *SubTaskRunEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetStartedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubTaskRunEventValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubTaskRunEventValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubTaskRunEventValidationError{
				field:  "StartedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubTaskRunEventValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubTaskRunEventValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubTaskRunEventValidationError{
				field:  "CompletedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetElapsedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubTaskRunEventValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubTaskRunEventValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetElapsedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubTaskRunEventValidationError{
				field:  "ElapsedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Name

	// no validation rules for Namespace

	// no validation rules for Type

	// no validation rules for Status

	// no validation rules for PipelineRunStageId

	// no validation rules for TaskRunId

	if all {
		switch v := interface{}(m.GetResults()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubTaskRunEventValidationError{
					field:  "Results",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubTaskRunEventValidationError{
					field:  "Results",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResults()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubTaskRunEventValidationError{
				field:  "Results",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PipelineRunId

	// no validation rules for Reason

	// no validation rules for SubTaskId

	if len(errors) > 0 {
		return SubTaskRunEventMultiError(errors)
	}

	return nil
}

// SubTaskRunEventMultiError is an error wrapping multiple validation errors
// returned by SubTaskRunEvent.ValidateAll() if the designated constraints
// aren't met.
type SubTaskRunEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubTaskRunEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubTaskRunEventMultiError) AllErrors() []error { return m }

// SubTaskRunEventValidationError is the validation error returned by
// SubTaskRunEvent.Validate if the designated constraints aren't met.
type SubTaskRunEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubTaskRunEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubTaskRunEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubTaskRunEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubTaskRunEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubTaskRunEventValidationError) ErrorName() string { return "SubTaskRunEventValidationError" }

// Error satisfies the builtin error interface
func (e SubTaskRunEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubTaskRunEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubTaskRunEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubTaskRunEventValidationError{}

// Validate checks the field values on StageRunEvent with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StageRunEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StageRunEvent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StageRunEventMultiError, or
// nil if none found.
func (m *StageRunEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *StageRunEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetStartedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageRunEventValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageRunEventValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageRunEventValidationError{
				field:  "StartedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageRunEventValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageRunEventValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageRunEventValidationError{
				field:  "CompletedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetElapsedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageRunEventValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageRunEventValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetElapsedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageRunEventValidationError{
				field:  "ElapsedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for Status

	// no validation rules for PipelineRunId

	if len(errors) > 0 {
		return StageRunEventMultiError(errors)
	}

	return nil
}

// StageRunEventMultiError is an error wrapping multiple validation errors
// returned by StageRunEvent.ValidateAll() if the designated constraints
// aren't met.
type StageRunEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StageRunEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StageRunEventMultiError) AllErrors() []error { return m }

// StageRunEventValidationError is the validation error returned by
// StageRunEvent.Validate if the designated constraints aren't met.
type StageRunEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StageRunEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StageRunEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StageRunEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StageRunEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StageRunEventValidationError) ErrorName() string { return "StageRunEventValidationError" }

// Error satisfies the builtin error interface
func (e StageRunEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStageRunEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StageRunEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StageRunEventValidationError{}

// Validate checks the field values on ApprovalUpdateEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApprovalUpdateEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApprovalUpdateEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApprovalUpdateEventMultiError, or nil if none found.
func (m *ApprovalUpdateEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *ApprovalUpdateEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TicketId

	// no validation rules for FlowInstId

	// no validation rules for NodeInstId

	// no validation rules for ApprovalUserId

	// no validation rules for Status

	// no validation rules for Opinion

	// no validation rules for TaskId

	// no validation rules for ProjectId

	// no validation rules for TicketReason

	if len(errors) > 0 {
		return ApprovalUpdateEventMultiError(errors)
	}

	return nil
}

// ApprovalUpdateEventMultiError is an error wrapping multiple validation
// errors returned by ApprovalUpdateEvent.ValidateAll() if the designated
// constraints aren't met.
type ApprovalUpdateEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApprovalUpdateEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApprovalUpdateEventMultiError) AllErrors() []error { return m }

// ApprovalUpdateEventValidationError is the validation error returned by
// ApprovalUpdateEvent.Validate if the designated constraints aren't met.
type ApprovalUpdateEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApprovalUpdateEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApprovalUpdateEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApprovalUpdateEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApprovalUpdateEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApprovalUpdateEventValidationError) ErrorName() string {
	return "ApprovalUpdateEventValidationError"
}

// Error satisfies the builtin error interface
func (e ApprovalUpdateEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApprovalUpdateEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApprovalUpdateEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApprovalUpdateEventValidationError{}

// Validate checks the field values on ApprovalHandleEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApprovalHandleEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApprovalHandleEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApprovalHandleEventMultiError, or nil if none found.
func (m *ApprovalHandleEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *ApprovalHandleEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TicketId

	// no validation rules for TicketType

	// no validation rules for TicketSn

	// no validation rules for TicketReason

	// no validation rules for Branch

	// no validation rules for ApplicantId

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApprovalHandleEventValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApprovalHandleEventValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApprovalHandleEventValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApprovalNodeName

	// no validation rules for AppName

	// no validation rules for FlowNodeInstId

	for idx, item := range m.GetEnvs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ApprovalHandleEventValidationError{
						field:  fmt.Sprintf("Envs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ApprovalHandleEventValidationError{
						field:  fmt.Sprintf("Envs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ApprovalHandleEventValidationError{
					field:  fmt.Sprintf("Envs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ProjectId

	// no validation rules for TicketName

	if len(errors) > 0 {
		return ApprovalHandleEventMultiError(errors)
	}

	return nil
}

// ApprovalHandleEventMultiError is an error wrapping multiple validation
// errors returned by ApprovalHandleEvent.ValidateAll() if the designated
// constraints aren't met.
type ApprovalHandleEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApprovalHandleEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApprovalHandleEventMultiError) AllErrors() []error { return m }

// ApprovalHandleEventValidationError is the validation error returned by
// ApprovalHandleEvent.Validate if the designated constraints aren't met.
type ApprovalHandleEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApprovalHandleEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApprovalHandleEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApprovalHandleEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApprovalHandleEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApprovalHandleEventValidationError) ErrorName() string {
	return "ApprovalHandleEventValidationError"
}

// Error satisfies the builtin error interface
func (e ApprovalHandleEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApprovalHandleEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApprovalHandleEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApprovalHandleEventValidationError{}

// Validate checks the field values on DeployChangeLogEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeployChangeLogEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeployChangeLogEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeployChangeLogEventMultiError, or nil if none found.
func (m *DeployChangeLogEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *DeployChangeLogEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Env

	// no validation rules for EnvTarget

	// no validation rules for Cluster

	// no validation rules for Namespace

	// no validation rules for AppId

	// no validation rules for ConfigId

	// no validation rules for TaskRunId

	// no validation rules for Status

	// no validation rules for IsCurrent

	// no validation rules for Description

	// no validation rules for OperatorBy

	// no validation rules for OperatorByChineseName

	// no validation rules for OperatorByEmployeeNo

	if all {
		switch v := interface{}(m.GetOperatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeployChangeLogEventValidationError{
					field:  "OperatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeployChangeLogEventValidationError{
					field:  "OperatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOperatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeployChangeLogEventValidationError{
				field:  "OperatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Action

	// no validation rules for ConfigVersion

	// no validation rules for MetadataId

	// no validation rules for ArtifactVersion

	// no validation rules for Branch

	// no validation rules for Method

	// no validation rules for Senv

	if len(errors) > 0 {
		return DeployChangeLogEventMultiError(errors)
	}

	return nil
}

// DeployChangeLogEventMultiError is an error wrapping multiple validation
// errors returned by DeployChangeLogEvent.ValidateAll() if the designated
// constraints aren't met.
type DeployChangeLogEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeployChangeLogEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeployChangeLogEventMultiError) AllErrors() []error { return m }

// DeployChangeLogEventValidationError is the validation error returned by
// DeployChangeLogEvent.Validate if the designated constraints aren't met.
type DeployChangeLogEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeployChangeLogEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeployChangeLogEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeployChangeLogEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeployChangeLogEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeployChangeLogEventValidationError) ErrorName() string {
	return "DeployChangeLogEventValidationError"
}

// Error satisfies the builtin error interface
func (e DeployChangeLogEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeployChangeLogEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeployChangeLogEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeployChangeLogEventValidationError{}

// Validate checks the field values on DeployCfgTmplEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeployCfgTmplEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeployCfgTmplEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeployCfgTmplEventMultiError, or nil if none found.
func (m *DeployCfgTmplEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *DeployCfgTmplEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ProjectId

	// no validation rules for ConfigVersion

	// no validation rules for ConfigType

	// no validation rules for CreatedBy

	// no validation rules for CreatedByChineseName

	// no validation rules for CreatedByEmployeeNo

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeployCfgTmplEventValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeployCfgTmplEventValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeployCfgTmplEventValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeployCfgTmplEventValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeployCfgTmplEventValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeployCfgTmplEventValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeployCfgTmplEventMultiError(errors)
	}

	return nil
}

// DeployCfgTmplEventMultiError is an error wrapping multiple validation errors
// returned by DeployCfgTmplEvent.ValidateAll() if the designated constraints
// aren't met.
type DeployCfgTmplEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeployCfgTmplEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeployCfgTmplEventMultiError) AllErrors() []error { return m }

// DeployCfgTmplEventValidationError is the validation error returned by
// DeployCfgTmplEvent.Validate if the designated constraints aren't met.
type DeployCfgTmplEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeployCfgTmplEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeployCfgTmplEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeployCfgTmplEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeployCfgTmplEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeployCfgTmplEventValidationError) ErrorName() string {
	return "DeployCfgTmplEventValidationError"
}

// Error satisfies the builtin error interface
func (e DeployCfgTmplEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeployCfgTmplEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeployCfgTmplEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeployCfgTmplEventValidationError{}

// Validate checks the field values on DeployCfgTmplChangeEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeployCfgTmplChangeEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeployCfgTmplChangeEvent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeployCfgTmplChangeEventMultiError, or nil if none found.
func (m *DeployCfgTmplChangeEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *DeployCfgTmplChangeEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ProjectId

	// no validation rules for ConfigName

	// no validation rules for Operator

	// no validation rules for Email

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeployCfgTmplChangeEventValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeployCfgTmplChangeEventValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeployCfgTmplChangeEventValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeployCfgTmplChangeEventMultiError(errors)
	}

	return nil
}

// DeployCfgTmplChangeEventMultiError is an error wrapping multiple validation
// errors returned by DeployCfgTmplChangeEvent.ValidateAll() if the designated
// constraints aren't met.
type DeployCfgTmplChangeEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeployCfgTmplChangeEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeployCfgTmplChangeEventMultiError) AllErrors() []error { return m }

// DeployCfgTmplChangeEventValidationError is the validation error returned by
// DeployCfgTmplChangeEvent.Validate if the designated constraints aren't met.
type DeployCfgTmplChangeEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeployCfgTmplChangeEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeployCfgTmplChangeEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeployCfgTmplChangeEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeployCfgTmplChangeEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeployCfgTmplChangeEventValidationError) ErrorName() string {
	return "DeployCfgTmplChangeEventValidationError"
}

// Error satisfies the builtin error interface
func (e DeployCfgTmplChangeEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeployCfgTmplChangeEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeployCfgTmplChangeEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeployCfgTmplChangeEventValidationError{}

// Validate checks the field values on TicketUpdateEvent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TicketUpdateEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TicketUpdateEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TicketUpdateEventMultiError, or nil if none found.
func (m *TicketUpdateEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *TicketUpdateEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return TicketUpdateEventMultiError(errors)
	}

	return nil
}

// TicketUpdateEventMultiError is an error wrapping multiple validation errors
// returned by TicketUpdateEvent.ValidateAll() if the designated constraints
// aren't met.
type TicketUpdateEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TicketUpdateEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TicketUpdateEventMultiError) AllErrors() []error { return m }

// TicketUpdateEventValidationError is the validation error returned by
// TicketUpdateEvent.Validate if the designated constraints aren't met.
type TicketUpdateEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TicketUpdateEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TicketUpdateEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TicketUpdateEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TicketUpdateEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TicketUpdateEventValidationError) ErrorName() string {
	return "TicketUpdateEventValidationError"
}

// Error satisfies the builtin error interface
func (e TicketUpdateEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTicketUpdateEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TicketUpdateEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TicketUpdateEventValidationError{}

// Validate checks the field values on ChSetPipelineTaskEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChSetPipelineTaskEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChSetPipelineTaskEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChSetPipelineTaskEventMultiError, or nil if none found.
func (m *ChSetPipelineTaskEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *ChSetPipelineTaskEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ChangeSetId

	// no validation rules for Type

	// no validation rules for Status

	// no validation rules for PipelineRunId

	// no validation rules for TaskRunId

	// no validation rules for StageId

	// no validation rules for TaskId

	// no validation rules for ChangeSetStageId

	if len(errors) > 0 {
		return ChSetPipelineTaskEventMultiError(errors)
	}

	return nil
}

// ChSetPipelineTaskEventMultiError is an error wrapping multiple validation
// errors returned by ChSetPipelineTaskEvent.ValidateAll() if the designated
// constraints aren't met.
type ChSetPipelineTaskEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChSetPipelineTaskEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChSetPipelineTaskEventMultiError) AllErrors() []error { return m }

// ChSetPipelineTaskEventValidationError is the validation error returned by
// ChSetPipelineTaskEvent.Validate if the designated constraints aren't met.
type ChSetPipelineTaskEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChSetPipelineTaskEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChSetPipelineTaskEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChSetPipelineTaskEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChSetPipelineTaskEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChSetPipelineTaskEventValidationError) ErrorName() string {
	return "ChSetPipelineTaskEventValidationError"
}

// Error satisfies the builtin error interface
func (e ChSetPipelineTaskEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChSetPipelineTaskEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChSetPipelineTaskEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChSetPipelineTaskEventValidationError{}

// Validate checks the field values on PipelineTemplateChangeEvent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PipelineTemplateChangeEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PipelineTemplateChangeEvent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PipelineTemplateChangeEventMultiError, or nil if none found.
func (m *PipelineTemplateChangeEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *PipelineTemplateChangeEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return PipelineTemplateChangeEventMultiError(errors)
	}

	return nil
}

// PipelineTemplateChangeEventMultiError is an error wrapping multiple
// validation errors returned by PipelineTemplateChangeEvent.ValidateAll() if
// the designated constraints aren't met.
type PipelineTemplateChangeEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PipelineTemplateChangeEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PipelineTemplateChangeEventMultiError) AllErrors() []error { return m }

// PipelineTemplateChangeEventValidationError is the validation error returned
// by PipelineTemplateChangeEvent.Validate if the designated constraints
// aren't met.
type PipelineTemplateChangeEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PipelineTemplateChangeEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PipelineTemplateChangeEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PipelineTemplateChangeEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PipelineTemplateChangeEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PipelineTemplateChangeEventValidationError) ErrorName() string {
	return "PipelineTemplateChangeEventValidationError"
}

// Error satisfies the builtin error interface
func (e PipelineTemplateChangeEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPipelineTemplateChangeEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PipelineTemplateChangeEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PipelineTemplateChangeEventValidationError{}

// Validate checks the field values on ChangeSetTaskEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChangeSetTaskEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangeSetTaskEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChangeSetTaskEventMultiError, or nil if none found.
func (m *ChangeSetTaskEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangeSetTaskEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Type

	// no validation rules for Status

	// no validation rules for ChangeSetId

	// no validation rules for ChangeSetStageId

	// no validation rules for IsIgnoreNotify

	if len(errors) > 0 {
		return ChangeSetTaskEventMultiError(errors)
	}

	return nil
}

// ChangeSetTaskEventMultiError is an error wrapping multiple validation errors
// returned by ChangeSetTaskEvent.ValidateAll() if the designated constraints
// aren't met.
type ChangeSetTaskEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangeSetTaskEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangeSetTaskEventMultiError) AllErrors() []error { return m }

// ChangeSetTaskEventValidationError is the validation error returned by
// ChangeSetTaskEvent.Validate if the designated constraints aren't met.
type ChangeSetTaskEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangeSetTaskEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangeSetTaskEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangeSetTaskEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangeSetTaskEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangeSetTaskEventValidationError) ErrorName() string {
	return "ChangeSetTaskEventValidationError"
}

// Error satisfies the builtin error interface
func (e ChangeSetTaskEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangeSetTaskEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangeSetTaskEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangeSetTaskEventValidationError{}

// Validate checks the field values on ChangeSetRunEvent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ChangeSetRunEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangeSetRunEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChangeSetRunEventMultiError, or nil if none found.
func (m *ChangeSetRunEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangeSetRunEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetStartedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChangeSetRunEventValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChangeSetRunEventValidationError{
					field:  "StartedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChangeSetRunEventValidationError{
				field:  "StartedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChangeSetRunEventValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChangeSetRunEventValidationError{
					field:  "CompletedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChangeSetRunEventValidationError{
				field:  "CompletedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetElapsedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChangeSetRunEventValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChangeSetRunEventValidationError{
					field:  "ElapsedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetElapsedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChangeSetRunEventValidationError{
				field:  "ElapsedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Name

	// no validation rules for Status

	if len(errors) > 0 {
		return ChangeSetRunEventMultiError(errors)
	}

	return nil
}

// ChangeSetRunEventMultiError is an error wrapping multiple validation errors
// returned by ChangeSetRunEvent.ValidateAll() if the designated constraints
// aren't met.
type ChangeSetRunEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangeSetRunEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangeSetRunEventMultiError) AllErrors() []error { return m }

// ChangeSetRunEventValidationError is the validation error returned by
// ChangeSetRunEvent.Validate if the designated constraints aren't met.
type ChangeSetRunEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangeSetRunEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangeSetRunEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangeSetRunEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangeSetRunEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangeSetRunEventValidationError) ErrorName() string {
	return "ChangeSetRunEventValidationError"
}

// Error satisfies the builtin error interface
func (e ChangeSetRunEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangeSetRunEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangeSetRunEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangeSetRunEventValidationError{}

// Validate checks the field values on CanaryShiftEvent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CanaryShiftEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CanaryShiftEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CanaryShiftEventMultiError, or nil if none found.
func (m *CanaryShiftEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *CanaryShiftEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskRunId

	// no validation rules for Current

	// no validation rules for Step

	// no validation rules for Min

	// no validation rules for Max

	if len(errors) > 0 {
		return CanaryShiftEventMultiError(errors)
	}

	return nil
}

// CanaryShiftEventMultiError is an error wrapping multiple validation errors
// returned by CanaryShiftEvent.ValidateAll() if the designated constraints
// aren't met.
type CanaryShiftEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CanaryShiftEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CanaryShiftEventMultiError) AllErrors() []error { return m }

// CanaryShiftEventValidationError is the validation error returned by
// CanaryShiftEvent.Validate if the designated constraints aren't met.
type CanaryShiftEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CanaryShiftEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CanaryShiftEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CanaryShiftEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CanaryShiftEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CanaryShiftEventValidationError) ErrorName() string { return "CanaryShiftEventValidationError" }

// Error satisfies the builtin error interface
func (e CanaryShiftEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCanaryShiftEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CanaryShiftEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CanaryShiftEventValidationError{}

// Validate checks the field values on ProjectEvent with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProjectEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProjectEvent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProjectEventMultiError, or
// nil if none found.
func (m *ProjectEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *ProjectEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ProjectName

	// no validation rules for ProjectType

	// no validation rules for Action

	if len(errors) > 0 {
		return ProjectEventMultiError(errors)
	}

	return nil
}

// ProjectEventMultiError is an error wrapping multiple validation errors
// returned by ProjectEvent.ValidateAll() if the designated constraints aren't met.
type ProjectEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProjectEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProjectEventMultiError) AllErrors() []error { return m }

// ProjectEventValidationError is the validation error returned by
// ProjectEvent.Validate if the designated constraints aren't met.
type ProjectEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProjectEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProjectEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProjectEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProjectEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProjectEventValidationError) ErrorName() string { return "ProjectEventValidationError" }

// Error satisfies the builtin error interface
func (e ProjectEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProjectEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProjectEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProjectEventValidationError{}

// Validate checks the field values on DeletePipelineResourceEvent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeletePipelineResourceEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeletePipelineResourceEvent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeletePipelineResourceEventMultiError, or nil if none found.
func (m *DeletePipelineResourceEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *DeletePipelineResourceEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for From

	// no validation rules for PipelineId

	// no validation rules for PipelineGroupId

	// no validation rules for TemplateId

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeletePipelineResourceEventValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeletePipelineResourceEventValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeletePipelineResourceEventValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProjectId

	// no validation rules for OperatorBy

	// no validation rules for OperatorByChineseName

	// no validation rules for OperatorByEmployeeNo

	if len(errors) > 0 {
		return DeletePipelineResourceEventMultiError(errors)
	}

	return nil
}

// DeletePipelineResourceEventMultiError is an error wrapping multiple
// validation errors returned by DeletePipelineResourceEvent.ValidateAll() if
// the designated constraints aren't met.
type DeletePipelineResourceEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeletePipelineResourceEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeletePipelineResourceEventMultiError) AllErrors() []error { return m }

// DeletePipelineResourceEventValidationError is the validation error returned
// by DeletePipelineResourceEvent.Validate if the designated constraints
// aren't met.
type DeletePipelineResourceEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeletePipelineResourceEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeletePipelineResourceEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeletePipelineResourceEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeletePipelineResourceEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeletePipelineResourceEventValidationError) ErrorName() string {
	return "DeletePipelineResourceEventValidationError"
}

// Error satisfies the builtin error interface
func (e DeletePipelineResourceEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeletePipelineResourceEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeletePipelineResourceEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeletePipelineResourceEventValidationError{}

// Validate checks the field values on PipelineStatusChangeEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PipelineStatusChangeEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PipelineStatusChangeEvent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PipelineStatusChangeEventMultiError, or nil if none found.
func (m *PipelineStatusChangeEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *PipelineStatusChangeEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PipelineId

	// no validation rules for PipelineRunId

	// no validation rules for AppId

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for Status

	// no validation rules for TriggerEmployeeNo

	// no validation rules for TriggerChineseName

	// no validation rules for StartTime

	// no validation rules for EndTime

	// no validation rules for TriggerMode

	// no validation rules for Branch

	// no validation rules for BuildNumber

	// no validation rules for RepoAddr

	// no validation rules for AppName

	// no validation rules for ProjectName

	// no validation rules for ProjectId

	if m.SourceBranch != nil {
		// no validation rules for SourceBranch
	}

	if len(errors) > 0 {
		return PipelineStatusChangeEventMultiError(errors)
	}

	return nil
}

// PipelineStatusChangeEventMultiError is an error wrapping multiple validation
// errors returned by PipelineStatusChangeEvent.ValidateAll() if the
// designated constraints aren't met.
type PipelineStatusChangeEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PipelineStatusChangeEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PipelineStatusChangeEventMultiError) AllErrors() []error { return m }

// PipelineStatusChangeEventValidationError is the validation error returned by
// PipelineStatusChangeEvent.Validate if the designated constraints aren't met.
type PipelineStatusChangeEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PipelineStatusChangeEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PipelineStatusChangeEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PipelineStatusChangeEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PipelineStatusChangeEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PipelineStatusChangeEventValidationError) ErrorName() string {
	return "PipelineStatusChangeEventValidationError"
}

// Error satisfies the builtin error interface
func (e PipelineStatusChangeEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPipelineStatusChangeEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PipelineStatusChangeEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PipelineStatusChangeEventValidationError{}

// Validate checks the field values on TaskStatusChangeEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TaskStatusChangeEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskStatusChangeEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TaskStatusChangeEventMultiError, or nil if none found.
func (m *TaskStatusChangeEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskStatusChangeEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for Status

	// no validation rules for StartTime

	// no validation rules for EndTime

	// no validation rules for StageRunId

	// no validation rules for PipelineRunId

	// no validation rules for PipelineId

	if len(errors) > 0 {
		return TaskStatusChangeEventMultiError(errors)
	}

	return nil
}

// TaskStatusChangeEventMultiError is an error wrapping multiple validation
// errors returned by TaskStatusChangeEvent.ValidateAll() if the designated
// constraints aren't met.
type TaskStatusChangeEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskStatusChangeEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskStatusChangeEventMultiError) AllErrors() []error { return m }

// TaskStatusChangeEventValidationError is the validation error returned by
// TaskStatusChangeEvent.Validate if the designated constraints aren't met.
type TaskStatusChangeEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskStatusChangeEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskStatusChangeEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskStatusChangeEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskStatusChangeEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskStatusChangeEventValidationError) ErrorName() string {
	return "TaskStatusChangeEventValidationError"
}

// Error satisfies the builtin error interface
func (e TaskStatusChangeEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskStatusChangeEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskStatusChangeEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskStatusChangeEventValidationError{}

// Validate checks the field values on SubTaskStatusChangeEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubTaskStatusChangeEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubTaskStatusChangeEvent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubTaskStatusChangeEventMultiError, or nil if none found.
func (m *SubTaskStatusChangeEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *SubTaskStatusChangeEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for Status

	// no validation rules for StartTime

	// no validation rules for EndTime

	// no validation rules for TaskRunId

	// no validation rules for StageRunId

	// no validation rules for PipelineRunId

	// no validation rules for PipelineId

	if len(errors) > 0 {
		return SubTaskStatusChangeEventMultiError(errors)
	}

	return nil
}

// SubTaskStatusChangeEventMultiError is an error wrapping multiple validation
// errors returned by SubTaskStatusChangeEvent.ValidateAll() if the designated
// constraints aren't met.
type SubTaskStatusChangeEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubTaskStatusChangeEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubTaskStatusChangeEventMultiError) AllErrors() []error { return m }

// SubTaskStatusChangeEventValidationError is the validation error returned by
// SubTaskStatusChangeEvent.Validate if the designated constraints aren't met.
type SubTaskStatusChangeEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubTaskStatusChangeEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubTaskStatusChangeEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubTaskStatusChangeEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubTaskStatusChangeEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubTaskStatusChangeEventValidationError) ErrorName() string {
	return "SubTaskStatusChangeEventValidationError"
}

// Error satisfies the builtin error interface
func (e SubTaskStatusChangeEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubTaskStatusChangeEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubTaskStatusChangeEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubTaskStatusChangeEventValidationError{}

// Validate checks the field values on AppDeployActionEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AppDeployActionEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AppDeployActionEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AppDeployActionEventMultiError, or nil if none found.
func (m *AppDeployActionEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *AppDeployActionEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppId

	// no validation rules for AppName

	// no validation rules for WorkloadName

	// no validation rules for CmdbId

	// no validation rules for Action

	// no validation rules for Status

	// no validation rules for Cluster

	// no validation rules for Namespace

	// no validation rules for Senv

	// no validation rules for Version

	// no validation rules for Env

	// no validation rules for EnvTarget

	// no validation rules for OperatorEmployeeNo

	// no validation rules for OperatorChineseName

	if len(errors) > 0 {
		return AppDeployActionEventMultiError(errors)
	}

	return nil
}

// AppDeployActionEventMultiError is an error wrapping multiple validation
// errors returned by AppDeployActionEvent.ValidateAll() if the designated
// constraints aren't met.
type AppDeployActionEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppDeployActionEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppDeployActionEventMultiError) AllErrors() []error { return m }

// AppDeployActionEventValidationError is the validation error returned by
// AppDeployActionEvent.Validate if the designated constraints aren't met.
type AppDeployActionEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppDeployActionEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppDeployActionEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppDeployActionEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppDeployActionEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppDeployActionEventValidationError) ErrorName() string {
	return "AppDeployActionEventValidationError"
}

// Error satisfies the builtin error interface
func (e AppDeployActionEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppDeployActionEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppDeployActionEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppDeployActionEventValidationError{}

// Validate checks the field values on ApprovalHandleEvent_DeployEnv with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApprovalHandleEvent_DeployEnv) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApprovalHandleEvent_DeployEnv with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ApprovalHandleEvent_DeployEnvMultiError, or nil if none found.
func (m *ApprovalHandleEvent_DeployEnv) ValidateAll() error {
	return m.validate(true)
}

func (m *ApprovalHandleEvent_DeployEnv) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EnvTarget

	// no validation rules for Cluster

	// no validation rules for Namespace

	// no validation rules for Senv

	if len(errors) > 0 {
		return ApprovalHandleEvent_DeployEnvMultiError(errors)
	}

	return nil
}

// ApprovalHandleEvent_DeployEnvMultiError is an error wrapping multiple
// validation errors returned by ApprovalHandleEvent_DeployEnv.ValidateAll()
// if the designated constraints aren't met.
type ApprovalHandleEvent_DeployEnvMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApprovalHandleEvent_DeployEnvMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApprovalHandleEvent_DeployEnvMultiError) AllErrors() []error { return m }

// ApprovalHandleEvent_DeployEnvValidationError is the validation error
// returned by ApprovalHandleEvent_DeployEnv.Validate if the designated
// constraints aren't met.
type ApprovalHandleEvent_DeployEnvValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApprovalHandleEvent_DeployEnvValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApprovalHandleEvent_DeployEnvValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApprovalHandleEvent_DeployEnvValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApprovalHandleEvent_DeployEnvValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApprovalHandleEvent_DeployEnvValidationError) ErrorName() string {
	return "ApprovalHandleEvent_DeployEnvValidationError"
}

// Error satisfies the builtin error interface
func (e ApprovalHandleEvent_DeployEnvValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApprovalHandleEvent_DeployEnv.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApprovalHandleEvent_DeployEnvValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApprovalHandleEvent_DeployEnvValidationError{}
