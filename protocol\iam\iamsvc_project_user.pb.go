// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        (unknown)
// source: iam/iamsvc_project_user.proto

package iam

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProjectUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId      int64  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
	ProjectId   int64  `protobuf:"varint,3,opt,name=projectId,proto3" json:"projectId,omitempty"`
	Role        string `protobuf:"bytes,4,opt,name=role,proto3" json:"role,omitempty"`
	ChineseName string `protobuf:"bytes,5,opt,name=chineseName,proto3" json:"chineseName,omitempty"`
}

func (x *ProjectUser) Reset() {
	*x = ProjectUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_iamsvc_project_user_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectUser) ProtoMessage() {}

func (x *ProjectUser) ProtoReflect() protoreflect.Message {
	mi := &file_iam_iamsvc_project_user_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectUser.ProtoReflect.Descriptor instead.
func (*ProjectUser) Descriptor() ([]byte, []int) {
	return file_iam_iamsvc_project_user_proto_rawDescGZIP(), []int{0}
}

func (x *ProjectUser) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ProjectUser) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ProjectUser) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *ProjectUser) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *ProjectUser) GetChineseName() string {
	if x != nil {
		return x.ChineseName
	}
	return ""
}

type ProjectUserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectUsers []*ProjectUser `protobuf:"bytes,1,rep,name=projectUsers,proto3" json:"projectUsers,omitempty"`
}

func (x *ProjectUserResponse) Reset() {
	*x = ProjectUserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_iamsvc_project_user_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectUserResponse) ProtoMessage() {}

func (x *ProjectUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_iam_iamsvc_project_user_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectUserResponse.ProtoReflect.Descriptor instead.
func (*ProjectUserResponse) Descriptor() ([]byte, []int) {
	return file_iam_iamsvc_project_user_proto_rawDescGZIP(), []int{1}
}

func (x *ProjectUserResponse) GetProjectUsers() []*ProjectUser {
	if x != nil {
		return x.ProjectUsers
	}
	return nil
}

type ProjectUserParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId []int64 `protobuf:"varint,1,rep,packed,name=projectId,proto3" json:"projectId,omitempty"`
}

func (x *ProjectUserParam) Reset() {
	*x = ProjectUserParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_iamsvc_project_user_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectUserParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectUserParam) ProtoMessage() {}

func (x *ProjectUserParam) ProtoReflect() protoreflect.Message {
	mi := &file_iam_iamsvc_project_user_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectUserParam.ProtoReflect.Descriptor instead.
func (*ProjectUserParam) Descriptor() ([]byte, []int) {
	return file_iam_iamsvc_project_user_proto_rawDescGZIP(), []int{2}
}

func (x *ProjectUserParam) GetProjectId() []int64 {
	if x != nil {
		return x.ProjectId
	}
	return nil
}

type UserProParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	ProjectId int64 `protobuf:"varint,2,opt,name=projectId,proto3" json:"projectId,omitempty"`
}

func (x *UserProParam) Reset() {
	*x = UserProParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_iamsvc_project_user_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserProParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserProParam) ProtoMessage() {}

func (x *UserProParam) ProtoReflect() protoreflect.Message {
	mi := &file_iam_iamsvc_project_user_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserProParam.ProtoReflect.Descriptor instead.
func (*UserProParam) Descriptor() ([]byte, []int) {
	return file_iam_iamsvc_project_user_proto_rawDescGZIP(), []int{3}
}

func (x *UserProParam) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserProParam) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

type UserQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// per page size
	PageNum   int32  `protobuf:"varint,1,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize  int32  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	ProjectId int64  `protobuf:"varint,3,opt,name=projectId,proto3" json:"projectId,omitempty"`
	EmployNo  string `protobuf:"bytes,4,opt,name=EmployNo,proto3" json:"EmployNo,omitempty"`
	// search all or search apart
	Search string `protobuf:"bytes,5,opt,name=search,proto3" json:"search,omitempty"`
}

func (x *UserQuery) Reset() {
	*x = UserQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_iamsvc_project_user_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserQuery) ProtoMessage() {}

func (x *UserQuery) ProtoReflect() protoreflect.Message {
	mi := &file_iam_iamsvc_project_user_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserQuery.ProtoReflect.Descriptor instead.
func (*UserQuery) Descriptor() ([]byte, []int) {
	return file_iam_iamsvc_project_user_proto_rawDescGZIP(), []int{4}
}

func (x *UserQuery) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *UserQuery) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *UserQuery) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *UserQuery) GetEmployNo() string {
	if x != nil {
		return x.EmployNo
	}
	return ""
}

func (x *UserQuery) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

type UserInProject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"id"
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// @gotags: json:"username"
	Username string `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	// @gotags: json:"chineseName"
	ChineseName string `protobuf:"bytes,3,opt,name=chineseName,proto3" json:"chineseName,omitempty"`
	// @gotags: json:"email"
	Email string `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	// @gotags: json:"employeeNo"
	EmployeeNo string `protobuf:"bytes,5,opt,name=employeeNo,proto3" json:"employeeNo,omitempty"`
	// @gotags: json:"role"
	Role string `protobuf:"bytes,7,opt,name=role,proto3" json:"role,omitempty"`
}

func (x *UserInProject) Reset() {
	*x = UserInProject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_iamsvc_project_user_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInProject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInProject) ProtoMessage() {}

func (x *UserInProject) ProtoReflect() protoreflect.Message {
	mi := &file_iam_iamsvc_project_user_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInProject.ProtoReflect.Descriptor instead.
func (*UserInProject) Descriptor() ([]byte, []int) {
	return file_iam_iamsvc_project_user_proto_rawDescGZIP(), []int{5}
}

func (x *UserInProject) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserInProject) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UserInProject) GetChineseName() string {
	if x != nil {
		return x.ChineseName
	}
	return ""
}

func (x *UserInProject) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserInProject) GetEmployeeNo() string {
	if x != nil {
		return x.EmployeeNo
	}
	return ""
}

func (x *UserInProject) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

type UserPage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"total_records"
	TotalRecords int64 `protobuf:"varint,3,opt,name=total_records,json=totalRecords,proto3" json:"total_records,omitempty"`
	// @gotags: json:"users"
	Users []*UserInProject `protobuf:"bytes,4,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *UserPage) Reset() {
	*x = UserPage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_iamsvc_project_user_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserPage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPage) ProtoMessage() {}

func (x *UserPage) ProtoReflect() protoreflect.Message {
	mi := &file_iam_iamsvc_project_user_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPage.ProtoReflect.Descriptor instead.
func (*UserPage) Descriptor() ([]byte, []int) {
	return file_iam_iamsvc_project_user_proto_rawDescGZIP(), []int{6}
}

func (x *UserPage) GetTotalRecords() int64 {
	if x != nil {
		return x.TotalRecords
	}
	return 0
}

func (x *UserPage) GetUsers() []*UserInProject {
	if x != nil {
		return x.Users
	}
	return nil
}

type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectUser *ProjectUser `protobuf:"bytes,1,opt,name=projectUser,proto3" json:"projectUser,omitempty"`
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_iamsvc_project_user_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_iam_iamsvc_project_user_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_iam_iamsvc_project_user_proto_rawDescGZIP(), []int{7}
}

func (x *UserInfo) GetProjectUser() *ProjectUser {
	if x != nil {
		return x.ProjectUser
	}
	return nil
}

var File_iam_iamsvc_project_user_proto protoreflect.FileDescriptor

var file_iam_iamsvc_project_user_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x69, 0x61, 0x6d, 0x2f, 0x69, 0x61, 0x6d, 0x73, 0x76, 0x63, 0x5f, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x03, 0x69, 0x61, 0x6d, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x89, 0x01, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x4b, 0x0a,
	0x13, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x73, 0x65, 0x72, 0x52, 0x0c, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x22, 0x30, 0x0a, 0x10, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1c,
	0x0a, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x44, 0x0a, 0x0c,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x22, 0x93, 0x01, 0x0a, 0x09, 0x55, 0x73, 0x65, 0x72, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x4e, 0x6f,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x4e, 0x6f,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x22, 0xa7, 0x01, 0x0a, 0x0d, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1e,
	0x0a, 0x0a, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x4e, 0x6f, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x4e, 0x6f, 0x12, 0x12,
	0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f,
	0x6c, 0x65, 0x22, 0x59, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22, 0x3e, 0x0a,
	0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x0b, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x55, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x73, 0x65, 0x72, 0x32, 0xc3, 0x03,
	0x0a, 0x12, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x44, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x79, 0x12, 0x15, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x1a, 0x18, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x42, 0x79, 0x12,
	0x11, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x1a, 0x18, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x10,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4f, 0x66, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x12, 0x0e, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x1a, 0x0d, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12,
	0x3d, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x10, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x55, 0x73, 0x65, 0x72, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x3d,
	0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x10, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x34, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42,
	0x79, 0x12, 0x11, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x1a, 0x0d, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x3e, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x55, 0x73, 0x65, 0x72, 0x12, 0x11, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x74, 0x74,
	0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x68, 0x61, 0x72, 0x6d, 0x6f, 0x6e, 0x79, 0x2f, 0x69, 0x61, 0x6d, 0x3b, 0x69,
	0x61, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_iam_iamsvc_project_user_proto_rawDescOnce sync.Once
	file_iam_iamsvc_project_user_proto_rawDescData = file_iam_iamsvc_project_user_proto_rawDesc
)

func file_iam_iamsvc_project_user_proto_rawDescGZIP() []byte {
	file_iam_iamsvc_project_user_proto_rawDescOnce.Do(func() {
		file_iam_iamsvc_project_user_proto_rawDescData = protoimpl.X.CompressGZIP(file_iam_iamsvc_project_user_proto_rawDescData)
	})
	return file_iam_iamsvc_project_user_proto_rawDescData
}

var file_iam_iamsvc_project_user_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_iam_iamsvc_project_user_proto_goTypes = []interface{}{
	(*ProjectUser)(nil),         // 0: iam.ProjectUser
	(*ProjectUserResponse)(nil), // 1: iam.ProjectUserResponse
	(*ProjectUserParam)(nil),    // 2: iam.ProjectUserParam
	(*UserProParam)(nil),        // 3: iam.UserProParam
	(*UserQuery)(nil),           // 4: iam.UserQuery
	(*UserInProject)(nil),       // 5: iam.UserInProject
	(*UserPage)(nil),            // 6: iam.UserPage
	(*UserInfo)(nil),            // 7: iam.UserInfo
	(*emptypb.Empty)(nil),       // 8: google.protobuf.Empty
}
var file_iam_iamsvc_project_user_proto_depIdxs = []int32{
	0,  // 0: iam.ProjectUserResponse.projectUsers:type_name -> iam.ProjectUser
	5,  // 1: iam.UserPage.users:type_name -> iam.UserInProject
	0,  // 2: iam.UserInfo.projectUser:type_name -> iam.ProjectUser
	2,  // 3: iam.ProjectUserService.GetProjectUsersBy:input_type -> iam.ProjectUserParam
	3,  // 4: iam.ProjectUserService.GetUserProjectsBy:input_type -> iam.UserProParam
	4,  // 5: iam.ProjectUserService.GetUserOfProject:input_type -> iam.UserQuery
	0,  // 6: iam.ProjectUserService.CreateProjectUser:input_type -> iam.ProjectUser
	0,  // 7: iam.ProjectUserService.UpdateProjectUser:input_type -> iam.ProjectUser
	3,  // 8: iam.ProjectUserService.GetProjectUserBy:input_type -> iam.UserProParam
	3,  // 9: iam.ProjectUserService.DeleteProjectUser:input_type -> iam.UserProParam
	1,  // 10: iam.ProjectUserService.GetProjectUsersBy:output_type -> iam.ProjectUserResponse
	1,  // 11: iam.ProjectUserService.GetUserProjectsBy:output_type -> iam.ProjectUserResponse
	6,  // 12: iam.ProjectUserService.GetUserOfProject:output_type -> iam.UserPage
	8,  // 13: iam.ProjectUserService.CreateProjectUser:output_type -> google.protobuf.Empty
	8,  // 14: iam.ProjectUserService.UpdateProjectUser:output_type -> google.protobuf.Empty
	7,  // 15: iam.ProjectUserService.GetProjectUserBy:output_type -> iam.UserInfo
	8,  // 16: iam.ProjectUserService.DeleteProjectUser:output_type -> google.protobuf.Empty
	10, // [10:17] is the sub-list for method output_type
	3,  // [3:10] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_iam_iamsvc_project_user_proto_init() }
func file_iam_iamsvc_project_user_proto_init() {
	if File_iam_iamsvc_project_user_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_iam_iamsvc_project_user_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_iamsvc_project_user_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectUserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_iamsvc_project_user_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectUserParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_iamsvc_project_user_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserProParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_iamsvc_project_user_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_iamsvc_project_user_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInProject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_iamsvc_project_user_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserPage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_iamsvc_project_user_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_iam_iamsvc_project_user_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_iam_iamsvc_project_user_proto_goTypes,
		DependencyIndexes: file_iam_iamsvc_project_user_proto_depIdxs,
		MessageInfos:      file_iam_iamsvc_project_user_proto_msgTypes,
	}.Build()
	File_iam_iamsvc_project_user_proto = out.File
	file_iam_iamsvc_project_user_proto_rawDesc = nil
	file_iam_iamsvc_project_user_proto_goTypes = nil
	file_iam_iamsvc_project_user_proto_depIdxs = nil
}
