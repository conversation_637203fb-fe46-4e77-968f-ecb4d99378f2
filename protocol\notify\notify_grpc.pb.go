// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: notify/notify.proto

package notify

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Notify_SendEmail_FullMethodName     = "/notify.Notify/SendEmail"
	Notify_SendLack_FullMethodName      = "/notify.Notify/SendLack"
	Notify_ExpireMsgCard_FullMethodName = "/notify.Notify/ExpireMsgCard"
)

// NotifyClient is the client API for Notify service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NotifyClient interface {
	SendEmail(ctx context.Context, in *EmailMessage, opts ...grpc.CallOption) (*Result, error)
	SendLack(ctx context.Context, in *LackMessage, opts ...grpc.CallOption) (*Result, error)
	ExpireMsgCard(ctx context.Context, in *ExpireMsgCardMessage, opts ...grpc.CallOption) (*Result, error)
}

type notifyClient struct {
	cc grpc.ClientConnInterface
}

func NewNotifyClient(cc grpc.ClientConnInterface) NotifyClient {
	return &notifyClient{cc}
}

func (c *notifyClient) SendEmail(ctx context.Context, in *EmailMessage, opts ...grpc.CallOption) (*Result, error) {
	out := new(Result)
	err := c.cc.Invoke(ctx, Notify_SendEmail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) SendLack(ctx context.Context, in *LackMessage, opts ...grpc.CallOption) (*Result, error) {
	out := new(Result)
	err := c.cc.Invoke(ctx, Notify_SendLack_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) ExpireMsgCard(ctx context.Context, in *ExpireMsgCardMessage, opts ...grpc.CallOption) (*Result, error) {
	out := new(Result)
	err := c.cc.Invoke(ctx, Notify_ExpireMsgCard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NotifyServer is the server API for Notify service.
// All implementations must embed UnimplementedNotifyServer
// for forward compatibility
type NotifyServer interface {
	SendEmail(context.Context, *EmailMessage) (*Result, error)
	SendLack(context.Context, *LackMessage) (*Result, error)
	ExpireMsgCard(context.Context, *ExpireMsgCardMessage) (*Result, error)
	mustEmbedUnimplementedNotifyServer()
}

// UnimplementedNotifyServer must be embedded to have forward compatible implementations.
type UnimplementedNotifyServer struct {
}

func (UnimplementedNotifyServer) SendEmail(context.Context, *EmailMessage) (*Result, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendEmail not implemented")
}
func (UnimplementedNotifyServer) SendLack(context.Context, *LackMessage) (*Result, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendLack not implemented")
}
func (UnimplementedNotifyServer) ExpireMsgCard(context.Context, *ExpireMsgCardMessage) (*Result, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExpireMsgCard not implemented")
}
func (UnimplementedNotifyServer) mustEmbedUnimplementedNotifyServer() {}

// UnsafeNotifyServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NotifyServer will
// result in compilation errors.
type UnsafeNotifyServer interface {
	mustEmbedUnimplementedNotifyServer()
}

func RegisterNotifyServer(s grpc.ServiceRegistrar, srv NotifyServer) {
	s.RegisterService(&Notify_ServiceDesc, srv)
}

func _Notify_SendEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmailMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).SendEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Notify_SendEmail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).SendEmail(ctx, req.(*EmailMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_SendLack_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LackMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).SendLack(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Notify_SendLack_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).SendLack(ctx, req.(*LackMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_ExpireMsgCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpireMsgCardMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).ExpireMsgCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Notify_ExpireMsgCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).ExpireMsgCard(ctx, req.(*ExpireMsgCardMessage))
	}
	return interceptor(ctx, in, info, handler)
}

// Notify_ServiceDesc is the grpc.ServiceDesc for Notify service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Notify_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "notify.Notify",
	HandlerType: (*NotifyServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendEmail",
			Handler:    _Notify_SendEmail_Handler,
		},
		{
			MethodName: "SendLack",
			Handler:    _Notify_SendLack_Handler,
		},
		{
			MethodName: "ExpireMsgCard",
			Handler:    _Notify_ExpireMsgCard_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "notify/notify.proto",
}
