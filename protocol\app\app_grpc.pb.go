// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: app/app.proto

package app

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AppService_GetApp_FullMethodName           = "/app.AppService/GetApp"
	AppService_GetAppInfo_FullMethodName       = "/app.AppService/GetAppInfo"
	AppService_GetAppBranchList_FullMethodName = "/app.AppService/GetAppBranchList"
	AppService_GetAppListByIds_FullMethodName  = "/app.AppService/GetAppListByIds"
	AppService_GetAppDeployMsg_FullMethodName  = "/app.AppService/GetAppDeployMsg"
)

// AppServiceClient is the client API for AppService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppServiceClient interface {
	GetApp(ctx context.Context, in *AppParam, opts ...grpc.CallOption) (*APP, error)
	GetAppInfo(ctx context.Context, in *GetAppInfoReq, opts ...grpc.CallOption) (*GetAppInfoResp, error)
	GetAppBranchList(ctx context.Context, in *AppParam, opts ...grpc.CallOption) (*AppBranchList, error)
	GetAppListByIds(ctx context.Context, in *AppsReq, opts ...grpc.CallOption) (*AppList, error)
	GetAppDeployMsg(ctx context.Context, in *GetDeployMsgReq, opts ...grpc.CallOption) (*GetDeployMsgResp, error)
}

type appServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppServiceClient(cc grpc.ClientConnInterface) AppServiceClient {
	return &appServiceClient{cc}
}

func (c *appServiceClient) GetApp(ctx context.Context, in *AppParam, opts ...grpc.CallOption) (*APP, error) {
	out := new(APP)
	err := c.cc.Invoke(ctx, AppService_GetApp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appServiceClient) GetAppInfo(ctx context.Context, in *GetAppInfoReq, opts ...grpc.CallOption) (*GetAppInfoResp, error) {
	out := new(GetAppInfoResp)
	err := c.cc.Invoke(ctx, AppService_GetAppInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appServiceClient) GetAppBranchList(ctx context.Context, in *AppParam, opts ...grpc.CallOption) (*AppBranchList, error) {
	out := new(AppBranchList)
	err := c.cc.Invoke(ctx, AppService_GetAppBranchList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appServiceClient) GetAppListByIds(ctx context.Context, in *AppsReq, opts ...grpc.CallOption) (*AppList, error) {
	out := new(AppList)
	err := c.cc.Invoke(ctx, AppService_GetAppListByIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appServiceClient) GetAppDeployMsg(ctx context.Context, in *GetDeployMsgReq, opts ...grpc.CallOption) (*GetDeployMsgResp, error) {
	out := new(GetDeployMsgResp)
	err := c.cc.Invoke(ctx, AppService_GetAppDeployMsg_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppServiceServer is the server API for AppService service.
// All implementations must embed UnimplementedAppServiceServer
// for forward compatibility
type AppServiceServer interface {
	GetApp(context.Context, *AppParam) (*APP, error)
	GetAppInfo(context.Context, *GetAppInfoReq) (*GetAppInfoResp, error)
	GetAppBranchList(context.Context, *AppParam) (*AppBranchList, error)
	GetAppListByIds(context.Context, *AppsReq) (*AppList, error)
	GetAppDeployMsg(context.Context, *GetDeployMsgReq) (*GetDeployMsgResp, error)
	mustEmbedUnimplementedAppServiceServer()
}

// UnimplementedAppServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAppServiceServer struct {
}

func (UnimplementedAppServiceServer) GetApp(context.Context, *AppParam) (*APP, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetApp not implemented")
}
func (UnimplementedAppServiceServer) GetAppInfo(context.Context, *GetAppInfoReq) (*GetAppInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppInfo not implemented")
}
func (UnimplementedAppServiceServer) GetAppBranchList(context.Context, *AppParam) (*AppBranchList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppBranchList not implemented")
}
func (UnimplementedAppServiceServer) GetAppListByIds(context.Context, *AppsReq) (*AppList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppListByIds not implemented")
}
func (UnimplementedAppServiceServer) GetAppDeployMsg(context.Context, *GetDeployMsgReq) (*GetDeployMsgResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppDeployMsg not implemented")
}
func (UnimplementedAppServiceServer) mustEmbedUnimplementedAppServiceServer() {}

// UnsafeAppServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppServiceServer will
// result in compilation errors.
type UnsafeAppServiceServer interface {
	mustEmbedUnimplementedAppServiceServer()
}

func RegisterAppServiceServer(s grpc.ServiceRegistrar, srv AppServiceServer) {
	s.RegisterService(&AppService_ServiceDesc, srv)
}

func _AppService_GetApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServiceServer).GetApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppService_GetApp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServiceServer).GetApp(ctx, req.(*AppParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppService_GetAppInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServiceServer).GetAppInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppService_GetAppInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServiceServer).GetAppInfo(ctx, req.(*GetAppInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppService_GetAppBranchList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServiceServer).GetAppBranchList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppService_GetAppBranchList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServiceServer).GetAppBranchList(ctx, req.(*AppParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppService_GetAppListByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServiceServer).GetAppListByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppService_GetAppListByIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServiceServer).GetAppListByIds(ctx, req.(*AppsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppService_GetAppDeployMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeployMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServiceServer).GetAppDeployMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppService_GetAppDeployMsg_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServiceServer).GetAppDeployMsg(ctx, req.(*GetDeployMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AppService_ServiceDesc is the grpc.ServiceDesc for AppService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "app.AppService",
	HandlerType: (*AppServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetApp",
			Handler:    _AppService_GetApp_Handler,
		},
		{
			MethodName: "GetAppInfo",
			Handler:    _AppService_GetAppInfo_Handler,
		},
		{
			MethodName: "GetAppBranchList",
			Handler:    _AppService_GetAppBranchList_Handler,
		},
		{
			MethodName: "GetAppListByIds",
			Handler:    _AppService_GetAppListByIds_Handler,
		},
		{
			MethodName: "GetAppDeployMsg",
			Handler:    _AppService_GetAppDeployMsg_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "app/app.proto",
}
