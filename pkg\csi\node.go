package csi

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"regexp"
	"strings"

	"github.com/container-storage-interface/spec/lib/go/csi"
	"github.com/redis/go-redis/v9"
	"golang.ttyuyin.com/harmony/csi-driver/pkg/csi/apollo_cfgs"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"
	k8sexec "k8s.io/utils/exec"
	"k8s.io/utils/mount"
)

var (
	volumeCaps = []csi.VolumeCapability_AccessMode{
		{
			Mode: csi.VolumeCapability_AccessMode_SINGLE_NODE_WRITER,
		},
		{
			Mode: csi.VolumeCapability_AccessMode_MULTI_NODE_MULTI_WRITER,
		},
		{
			Mode: csi.VolumeCapability_AccessMode_MULTI_NODE_READER_ONLY,
		},
	}
)

type nodeService struct {
	csi.UnimplementedNodeServer
	mount.SafeFormatAndMount
	nodeID   string
	client   kubernetes.Interface
	redisCli *redis.Client
	pubSub   *redis.PubSub
	mng      apollo_cfgs.IPodCfgsMng
}

func newNodeService(nodeID string, client kubernetes.Interface,
	redisCli *redis.Client, mng apollo_cfgs.IPodCfgsMng) nodeService {
	mounter := mount.SafeFormatAndMount{
		Interface: mount.New(""),
		Exec:      k8sexec.New(),
	}

	return nodeService{
		SafeFormatAndMount: mounter,
		nodeID:             nodeID,
		client:             client,
		redisCli:           redisCli,
		mng:                mng,
	}
}

// NodeStageVolume is called by the CO when a workload that wants to use the specified volume is placed (scheduled) on a node.
func (n *nodeService) NodeStageVolume(ctx context.Context, request *csi.NodeStageVolumeRequest) (*csi.NodeStageVolumeResponse, error) {
	return nil, status.Error(codes.Unimplemented, "")
}

// NodeUnstageVolume is called by the CO when a workload that was using the specified volume is being moved to a different node.
func (n *nodeService) NodeUnstageVolume(ctx context.Context, request *csi.NodeUnstageVolumeRequest) (*csi.NodeUnstageVolumeResponse, error) {
	return nil, status.Error(codes.Unimplemented, "")
}

// NodePublishVolume mounts the volume on the node.
func (n *nodeService) NodePublishVolume(ctx context.Context, request *csi.NodePublishVolumeRequest) (*csi.NodePublishVolumeResponse, error) {

	klog.Infof("✨✨✨-----> NodePublishVolume: %v", request)

	volumeID := request.GetVolumeId()
	if len(volumeID) == 0 {
		return nil, status.Error(codes.InvalidArgument, "Volume id not provided")
	}

	// /var/lib/kubelet/pods/054494d7-2dfd-48e4-869c-5e7c9d5bd2e3/volumes/kubernetes.io~csi/pvc-5679224b-126e-469b-b48d-c3b50aba190e/mount
	// 华为集群低版本K8S /mnt/paas/kubernetes/kubelet/pods/fd0144bd-b446-40dc-840e-a8b4eea7d76d/volumes/kubernetes.io~csi/pvc-df27a171-b508-4a9e-961c-098f9b09707e/mount
	target := request.GetTargetPath()

	pattern := `\/pods\/(.*?)\/volumes`
	re := regexp.MustCompile(pattern)
	match := re.FindStringSubmatch(target)
	if len(match) < 2 || len(match[1]) != 36 {
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("TargetPath invalid (%s) ", target))
	}

	// 华为集群不知道为何传过来的 targetPath 不是标准的 /var/lib/kubelet/pods/xxxx/volumes/kubernetes.io~csi/xxxx/mount
	if strings.HasPrefix(target, "/mnt/paas/kubernetes/kubelet") {
		target = strings.Replace(target, "/mnt/paas/kubernetes/kubelet", "/var/lib/kubelet", 1)
	}

	volCap := request.GetVolumeCapability()
	if volCap == nil {
		return nil, status.Error(codes.InvalidArgument, "Volume capability not provided")
	}

	if !isValidVolumeCapabilities([]*csi.VolumeCapability{volCap}) {
		return nil, status.Error(codes.InvalidArgument, "Volume capability not supported")
	}

	options := make(map[string]string)
	if m := volCap.GetMount(); m != nil {
		for _, f := range m.MountFlags {
			// get mountOptions from PV.spec.mountOptions
			options[f] = ""
		}
	}

	volCtx := request.GetVolumeContext()
	klog.Infof("NodePublishVolume: volume context: %v", volCtx)

	podUid := request.VolumeContext[KeyPodUID]

	// TODO modify your volume mount logic here
	klog.Infof("NodePublishVolume: creating dir %s", target)
	if err := os.MkdirAll(target, os.FileMode(0755)); err != nil {
		return nil, status.Errorf(codes.Internal, "Could not create dir %q: %v", target, err)
	}

	readOnly := true
	if request.GetReadonly() || request.VolumeCapability.AccessMode.GetMode() == csi.VolumeCapability_AccessMode_MULTI_NODE_READER_ONLY {
		readOnly = true
	}
	if readOnly {
		options["ro"] = ""
	}
	mountOptions := []string{"bind"}
	for k, v := range options {
		if v != "" {
			k = fmt.Sprintf("%s=%s", k, v)
		}
		mountOptions = append(mountOptions, k)
	}
	// hostPath := volCtx["hostPath"]
	// subPath := volCtx["subPath"]
	subPath := podUid
	sourcePath := CSIHostPath
	if subPath != "" {
		sourcePath = path.Join(CSIHostPath, subPath)
		exists, err := mount.PathExists(sourcePath)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "Could not check volume path %q exists: %v", sourcePath, err)
		}
		if !exists {
			klog.Infof("volume not existed")
			err := os.MkdirAll(sourcePath, 0755)
			if err != nil {
				return nil, status.Errorf(codes.Internal, "Could not make directory for meta %q", sourcePath)
			}
		}
	}

	err := n.syncApolloCfg(ctx, request, sourcePath)
	if err != nil {
		klog.Errorf("syncApolloCfgs failed: %v", err)
		return nil, status.Errorf(codes.Internal, "syncApolloCfgs failed: %v", err)
	}

	klog.Infof("NodePublishVolume: binding %s at %s", sourcePath, target)
	if err := n.Mount(sourcePath, target, "none", mountOptions); err != nil {
		klog.Errorf("syncApolloCfgs failed: %v", err)
		os.Remove(target)
		return nil, status.Errorf(codes.Internal, "Could not bind %q at %q: %v", sourcePath, target, err)
	}

	return &csi.NodePublishVolumeResponse{}, nil
}

// NodeUnpublishVolume unmount the volume from the target path
func (n *nodeService) NodeUnpublishVolume(ctx context.Context, request *csi.NodeUnpublishVolumeRequest) (*csi.NodeUnpublishVolumeResponse, error) {
	klog.Infof("✨✨✨-----> NodeUnpublishVolume: %v", request)
	target := request.GetTargetPath()
	if len(target) == 0 {
		return nil, status.Error(codes.InvalidArgument, "Target path not provided")
	}

	// TODO modify your volume umount logic here

	// target 必须是按照如下规则生成的，如果不是 功能GG 需要另找捷径
	// /var/lib/kubelet/pods/e61673ab-824c-4872-b35b-52155569129c/volumes/kubernetes.io~csi/pvc-7bf45d29-41a8-4850-b9d3-ec5482e5494b/mount

	pattern := `\/pods\/(.*?)\/volumes`
	re := regexp.MustCompile(pattern)
	match := re.FindStringSubmatch(target)
	if len(match) < 2 || len(match[1]) != 36 {
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("TargetPath invalid (%s) ", target))
	}

	// 华为集群不知道为何传过来的 targetPath 不是标准的 /var/lib/kubelet/pods/xxxx/volumes/kubernetes.io~csi/xxxx/mount
	if strings.HasPrefix(target, "/mnt/paas/kubernetes/kubelet") {
		target = strings.Replace(target, "/mnt/paas/kubernetes/kubelet", "/var/lib/kubelet", 1)
	}

	podUid := match[1]

	// 清除针对该POD配置的监听，并清理host文件
	n.mng.StopPodCfgs(podUid)
	// 删除redis 该Node 监听的POD 列表记录
	n.redisCli.SRem(ctx, n.nodeID, podUid)
	// 删除redis 该Node 监听的POD 配置记录
	n.redisCli.Del(ctx, n.nodeID+"||"+podUid)

	var corruptedMnt bool
	exists, err := mount.PathExists(target)
	if err == nil {
		if !exists {
			klog.Infof("✨✨✨-----> NodeUnpublishVolume: %s target not exists", target)
			return &csi.NodeUnpublishVolumeResponse{}, nil
		}
		var notMnt bool
		notMnt, err = mount.IsNotMountPoint(n, target)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "Check target path is mountpoint failed: %q", err)
		}
		if notMnt { // target exists but not a mountpoint
			klog.Infof("✨✨✨-----> NodeUnpublishVolume: %s target not mounted", target)
			return &csi.NodeUnpublishVolumeResponse{}, nil
		}
	} else if corruptedMnt = mount.IsCorruptedMnt(err); !corruptedMnt {
		return nil, status.Errorf(codes.Internal, "Check path %s failed: %q", target, err)
	}

	klog.Infof("✨✨✨-----> NodeUnpublishVolume: unmounting %s", target)
	if err := n.Unmount(target); err != nil {
		klog.Infof("Could not unmount %q: %v", target, err)
		return nil, status.Errorf(codes.Internal, "Could not unmount %q: %v", target, err)
	}

	return &csi.NodeUnpublishVolumeResponse{}, nil
}

// NodeGetVolumeStats get the volume stats
func (n *nodeService) NodeGetVolumeStats(ctx context.Context, request *csi.NodeGetVolumeStatsRequest) (*csi.NodeGetVolumeStatsResponse, error) {
	return nil, status.Error(codes.Unimplemented, "")
}

// NodeExpandVolume expand the volume
func (n *nodeService) NodeExpandVolume(ctx context.Context, request *csi.NodeExpandVolumeRequest) (*csi.NodeExpandVolumeResponse, error) {
	return nil, status.Error(codes.Unimplemented, "")
}

// NodeGetCapabilities get the node capabilities
func (n *nodeService) NodeGetCapabilities(ctx context.Context, request *csi.NodeGetCapabilitiesRequest) (*csi.NodeGetCapabilitiesResponse, error) {
	return &csi.NodeGetCapabilitiesResponse{}, nil
}

// NodeGetInfo get the node info
func (n *nodeService) NodeGetInfo(ctx context.Context, request *csi.NodeGetInfoRequest) (*csi.NodeGetInfoResponse, error) {
	return &csi.NodeGetInfoResponse{NodeId: n.nodeID}, nil
}

func isValidVolumeCapabilities(volCaps []*csi.VolumeCapability) bool {
	hasSupport := func(cap *csi.VolumeCapability) bool {
		for _, c := range volumeCaps {
			if c.GetMode() == cap.AccessMode.GetMode() {
				return true
			}
		}
		return false
	}

	foundAll := true
	for _, c := range volCaps {
		if !hasSupport(c) {
			foundAll = false
		}
	}
	return foundAll
}

const (
	KeyPodNamespace = "csi.storage.k8s.io/pod.namespace"
	KeyPodUID       = "csi.storage.k8s.io/pod.uid"
	KeyPodName      = "csi.storage.k8s.io/pod.name"
	KeyPodCfgInfo   = "com.ttyuyin.cicd.csi/pod.cfg.info"
)

func (n *nodeService) syncApolloCfg(ctx context.Context, request *csi.NodePublishVolumeRequest, sourcePath string) (err error) {
	if request.VolumeContext == nil {
		err = fmt.Errorf("VolumeContext is nil")
		return
	}

	podInfo := apollo_cfgs.PodInfo{
		Namespace:  request.VolumeContext[KeyPodNamespace],
		Name:       request.VolumeContext[KeyPodName],
		UID:        request.VolumeContext[KeyPodUID],
		PvNodePath: sourcePath,
	}

	pod, err := n.client.CoreV1().Pods(podInfo.Namespace).Get(ctx, podInfo.Name, metav1.GetOptions{})
	if err != nil {
		return
	}

	cfgAnnos := pod.GetAnnotations()[KeyPodCfgInfo]
	if cfgAnnos == "" {
		return
	}

	podDyCfgs, _ := base64.StdEncoding.DecodeString(cfgAnnos)
	if len(podDyCfgs) == 0 {
		return
	}

	// klog.Infof("✨✨✨-----> syncApolloCfg Annotations : %s", cfgAnnos)
	podCfgsInfo := apollo_cfgs.PodCfgsInfo{}
	err = json.Unmarshal(podDyCfgs, &podCfgsInfo)
	if err != nil {
		return
	}
	if len(podCfgsInfo.DyCfgs) == 0 {
		return
	}

	args := apollo_cfgs.SyncPodCfgsArgs{
		PodInfo:     podInfo,
		PodCfgsInfo: podCfgsInfo,
	}

	argsStr, err := json.Marshal(args)
	if err != nil {
		return
	}

	klog.Infof("✨✨✨-----> syncApolloCfg by : %#v", args)

	err = n.mng.StartPodCfgs(args)
	if err != nil {
		return
	}

	cmd := n.redisCli.SAdd(ctx, n.nodeID, podInfo.UID)
	if cmd.Err() != nil {
		err = fmt.Errorf("nodeID pod list ,redisCli SAdd , %w", cmd.Err())
		return
	}

	rst := n.redisCli.Set(ctx, n.nodeID+"||"+podInfo.UID, argsStr, 0)
	if rst.Err() != nil {
		err = fmt.Errorf("nodeID pod syncCfg info ,redisCli Set, %w", rst.Err())
		return
	}

	return
}

// 重启时从Redis恢复前次的配置同步状态
func (n *nodeService) recovery() (err error) {
	// 从Redis中获取该Node监听的Pod列表
	podList, err := n.redisCli.SMembers(context.Background(), n.nodeID).Result()
	if err != nil {
		return
	}
	klog.Infof("✨✨✨-----> start node %s, recovery %d pods cfg sync ", n.nodeID, len(podList))

	for _, podUid := range podList {
		// 从Redis中获取该Node监听的Pod配置信息
		podCfgInfo, errIn := n.redisCli.Get(context.Background(), n.nodeID+"||"+podUid).Result()
		if errIn != nil {
			err = errIn
			return
		}

		args := apollo_cfgs.SyncPodCfgsArgs{}
		err = json.Unmarshal([]byte(podCfgInfo), &args)
		if err != nil {
			return
		}

		err = n.mng.StartPodCfgs(args)
		if err != nil {
			return
		}
	}

	return
}

// 监听全局配置的新增，优化方向之一
// func (n *nodeService) SubscribeGlobalCfgsChg() error {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			klog.Errorf("SubscribeGlobalCfgsChg panic: %v", err)
// 		}
// 	}()

// 	n.pubSub = n.redisCli.Subscribe(context.Background(), GlobalCfgCh)
// 	for msg := range n.pubSub.Channel() {
// 		fmt.Println(msg.Channel)
// 	}

// 	return nil
// }
