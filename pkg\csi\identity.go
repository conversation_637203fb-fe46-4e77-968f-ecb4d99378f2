package csi

import (
	"context"

	"github.com/container-storage-interface/spec/lib/go/csi"
	"k8s.io/klog/v2"
)

type identitySer struct {
	csi.UnimplementedIdentityServer
}

// GetPluginInfo returns the name and version of the plugin
func (d *identitySer) GetPluginInfo(ctx context.Context, request *csi.GetPluginInfoRequest) (*csi.GetPluginInfoResponse, error) {
	resp := &csi.GetPluginInfoResponse{
		Name:          DriverName,
		VendorVersion: "v1",
	}
	klog.Infof("GetPluginInfo Called ")
	return resp, nil
}

// GetPluginCapabilities returns the capabilities of the plugin
func (d *identitySer) GetPluginCapabilities(ctx context.Context, request *csi.GetPluginCapabilitiesRequest) (*csi.GetPluginCapabilitiesResponse, error) {
	resp := &csi.GetPluginCapabilitiesResponse{
		Capabilities: []*csi.PluginCapability{
			{
				Type: &csi.PluginCapability_Service_{
					Service: &csi.PluginCapability_Service{
						Type: csi.PluginCapability_Service_CONTROLLER_SERVICE,
					},
				},
			},
			// {
			// 	Type: &csi.PluginCapability_Service_{
			// 		Service: &csi.PluginCapability_Service{
			// 			Type: csi.PluginCapability_Service_VOLUME_ACCESSIBILITY_CONSTRAINTS,
			// 		},
			// 	},
			// },
			// {
			// 	Type: &csi.PluginCapability_VolumeExpansion_{
			// 		VolumeExpansion: &csi.PluginCapability_VolumeExpansion{
			// 			Type: csi.PluginCapability_VolumeExpansion_ONLINE,
			// 		},
			// 	},
			// },
		},
	}
	klog.Infof("GetPluginCapabilities Called ")
	return resp, nil
}

// Probe returns the health and readiness of the plugin
func (d *identitySer) Probe(ctx context.Context, request *csi.ProbeRequest) (*csi.ProbeResponse, error) {
	return &csi.ProbeResponse{}, nil
}
