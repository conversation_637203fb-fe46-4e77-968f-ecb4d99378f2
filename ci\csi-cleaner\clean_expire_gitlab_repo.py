# -*- coding:utf-8 -*-
import logging
import os
import subprocess
from datetime import datetime, timedelta

GITLAB_PATH = "/gitlab-repo/v2/"

logging.basicConfig(
    filename='/data/yunwei/log/clean_expire_gitlab_repo.log',
    level=logging.INFO,
    format='[%(asctime)s]:[%(levelname)s]%(name)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)


def get_prefix_month():
    today = datetime.today()
    ago_months = []
    # 获取前三个月 输出格式为 2020-01的数组
    for i in range(3):
        offset = timedelta(days=30 * i)
        month = today - offset
        ago_months.append(month.strftime("%Y-%m"))
    return ago_months


def rm_expire_path(paths):
    for path in paths:
        absolute_path = GITLAB_PATH + path
        if not os.path.exists(absolute_path):
            logging.warning(f"路径 {absolute_path} 不存在或者已清除")
            return
        try:
            subprocess.run(['rm', '-rf', absolute_path], check=True)
            logging.info(f"路径 {absolute_path} 删除完成")
        except Exception as e:
            logging.error(f"路径 {absolute_path} 删除时发生错误: {str(e)}")
            exit(1)


def main():
    last_third_month_sub_paths = get_prefix_month()
    rm_expire_path(last_third_month_sub_paths)


if __name__ == '__main__':
    # 清理3个月前的gitlab仓库
    try:
        main()
    except Exception as e:
        logging.error("清理过期gitlab 仓库时发生错误: %s", str(e))
        exit(1)
