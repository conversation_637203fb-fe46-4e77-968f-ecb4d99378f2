package main

import (
	"context"
	"net"
	"os"
	"os/signal"
	"path"
	rtdebug "runtime/debug"
	"syscall"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"golang.ttyuyin.com/harmony/csi-driver/cmd/reposync/client"
	"golang.ttyuyin.com/harmony/csi-driver/cmd/reposync/server"
	"golang.ttyuyin.com/harmony/csi-driver/cmd/reposync/service"
	"golang.ttyuyin.com/harmony/csi-driver/pkg/bootstrap"
	"golang.ttyuyin.com/harmony/csi-driver/pkg/config"
	"golang.ttyuyin.com/harmony/csi-driver/pkg/event"
	"golang.ttyuyin.com/harmony/csi-driver/pkg/task"
	prpb "golang.ttyuyin.com/harmony/csi-driver/protocol/pipeline"
	repsyncpb "golang.ttyuyin.com/harmony/csi-driver/protocol/rdp/reposync/v1"
	"golang.ttyuyin.com/harmony/pkg/gormx"
	"golang.ttyuyin.com/harmony/pkg/log"
	logcore "golang.ttyuyin.com/harmony/pkg/log/core"
)

var (
	cfg *config.Config
	svc *service.LowerDirHandler
	tm  *task.Manager
)

func main() {
	moduleInit()

	log.Info("start repo-sync")
	ctx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer func() {
		stop()
		log.Infof("exit repo-sync")
	}()

	everLoop(ctx)
}

func moduleInit() {
	config.Init(path.Join(bootstrap.GetCurrentAbsPath(), ".env"))
	if !bootstrap.DebugEnabled() {
		log.SetLevel(logcore.InfoLevel)
	}

	cfg = config.Current()
	err := gormx.InitSingleton(&gormx.Config{
		Username:       cfg.MySQL.Username,
		Password:       cfg.MySQL.Password,
		Host:           cfg.MySQL.Host,
		Port:           3306,
		Dbname:         cfg.MySQL.DBName,
		MaxLifetime:    "30m",
		IdleConnection: 10,
		OpenConnection: 10,
	})
	if err != nil {
		log.Errorf("init db failed, err: %v", err)
		os.Exit(1)
	}

	conn, err := client.NewClientConn(cfg.GRPCTarget.PipelineRunTarget)
	prClient := prpb.NewPipelineRunServiceClient(conn)

	tm = task.NewManager(cfg.LowerDirRootPath, cfg.LowerDirVersion)
	svc = service.New(cfg, tm, prClient)
}

func newReceiver(cfg *config.Kafka) event.Receiver {
	receiver, _ := event.NewReceiver(cfg.Name, cfg.Topics, cfg.Brokers)
	return receiver
}

func everLoop(ctx context.Context) {
	cm := task.NewCronManager()
	// debug
	if bootstrap.DebugEnabled() {
		debug(cm)
	}

	loading(ctx)
	// 2月28号凌晨2点，同步下个月使用的只读层数据
	_, _ = cm.RegisterCronSpec(
		"0 0 2 28 2 *",
		svc,
	)
	// 1,3,5,7,8,10,12月31号凌晨2点，同步下个月使用的只读层数据
	_, _ = cm.RegisterCronSpec(
		"0 0 2 31 1,3,5,7,8,10,12 *",
		svc,
	)
	// 4,6,9,11月30号凌晨2点，同步下个月使用的只读层数据
	_, _ = cm.RegisterCronSpec(
		"0 0 2 30 4,6,9,11 *",
		svc,
	)
	// 现在新增 app 还没有事件，继续使用定时轮训
	ch := service.NewTempCronHandler(tm)
	_, _ = cm.RegisterCronSpec("0 */5 * * * *", ch)

	cm.Start()
	heartbeat := time.NewTicker(time.Minute * 2)
	svr := newGRPCServer()
	prReceiver := newReceiver(&cfg.PipelineEvent)
	_ = prReceiver.StartReceiver(ctx, handleReleaseDisk)

LOOP:
	for {
		select {
		case <-ctx.Done():
			cm.Stop()
			svr.Stop()
			_ = prReceiver.Close(ctx)
			break LOOP
		case <-heartbeat.C:
			log.Info("heartbeat check every 2 minutes")
			heartbeat.Reset(time.Minute * 2)
		default:
			time.Sleep(time.Millisecond * 500)
		}
	}
}

func newGRPCServer() *grpc.Server {
	svr := grpc.NewServer()

	srv := server.NewRepoSyncService(cfg.LowerDirRootPath, tm)
	reflection.Register(svr)
	repsyncpb.RegisterRepoSyncServiceServer(svr, srv)

	l, err := net.Listen("tcp", cfg.GRPCTarget.RepoSyncTarget)
	if err != nil {
		log.Errorf("failed to listen: %v", err)
		os.Exit(1)
		return nil
	}

	log.Infof("grpc server listen on %s", l.Addr().String())
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("grpc server panic: %v", r)
			}
		}()

		if err := svr.Serve(l); err != nil {
			log.Errorf("failed to grpc server: %v", err)
		}
	}()
	return svr
}

func loading(ctx context.Context) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("loading panic: %v ,stack:%s", r, rtdebug.Stack())
			}
		}()

		// 5s 后再开始加载
		time.Sleep(5 * time.Second)
		svc.FirstLoading(ctx)
	}()
}
