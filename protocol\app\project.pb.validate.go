// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: app/project.proto

package app

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Project with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Project) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Project with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ProjectMultiError, or nil if none found.
func (m *Project) ValidateAll() error {
	return m.validate(true)
}

func (m *Project) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Description

	if len(errors) > 0 {
		return ProjectMultiError(errors)
	}

	return nil
}

// ProjectMultiError is an error wrapping multiple validation errors returned
// by Project.ValidateAll() if the designated constraints aren't met.
type ProjectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProjectMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProjectMultiError) AllErrors() []error { return m }

// ProjectValidationError is the validation error returned by Project.Validate
// if the designated constraints aren't met.
type ProjectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProjectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProjectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProjectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProjectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProjectValidationError) ErrorName() string { return "ProjectValidationError" }

// Error satisfies the builtin error interface
func (e ProjectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProject.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProjectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProjectValidationError{}

// Validate checks the field values on ProjectListItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ProjectListItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProjectListItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProjectListItemMultiError, or nil if none found.
func (m *ProjectListItem) ValidateAll() error {
	return m.validate(true)
}

func (m *ProjectListItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for TotalMember

	// no validation rules for Managers

	if len(errors) > 0 {
		return ProjectListItemMultiError(errors)
	}

	return nil
}

// ProjectListItemMultiError is an error wrapping multiple validation errors
// returned by ProjectListItem.ValidateAll() if the designated constraints
// aren't met.
type ProjectListItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProjectListItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProjectListItemMultiError) AllErrors() []error { return m }

// ProjectListItemValidationError is the validation error returned by
// ProjectListItem.Validate if the designated constraints aren't met.
type ProjectListItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProjectListItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProjectListItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProjectListItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProjectListItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProjectListItemValidationError) ErrorName() string { return "ProjectListItemValidationError" }

// Error satisfies the builtin error interface
func (e ProjectListItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProjectListItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProjectListItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProjectListItemValidationError{}

// Validate checks the field values on ProjectList with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProjectList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProjectList with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProjectListMultiError, or
// nil if none found.
func (m *ProjectList) ValidateAll() error {
	return m.validate(true)
}

func (m *ProjectList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetProjects() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProjectListValidationError{
						field:  fmt.Sprintf("Projects[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProjectListValidationError{
						field:  fmt.Sprintf("Projects[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProjectListValidationError{
					field:  fmt.Sprintf("Projects[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProjectListMultiError(errors)
	}

	return nil
}

// ProjectListMultiError is an error wrapping multiple validation errors
// returned by ProjectList.ValidateAll() if the designated constraints aren't met.
type ProjectListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProjectListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProjectListMultiError) AllErrors() []error { return m }

// ProjectListValidationError is the validation error returned by
// ProjectList.Validate if the designated constraints aren't met.
type ProjectListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProjectListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProjectListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProjectListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProjectListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProjectListValidationError) ErrorName() string { return "ProjectListValidationError" }

// Error satisfies the builtin error interface
func (e ProjectListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProjectList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProjectListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProjectListValidationError{}

// Validate checks the field values on ProjectResult with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProjectResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProjectResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProjectResultMultiError, or
// nil if none found.
func (m *ProjectResult) ValidateAll() error {
	return m.validate(true)
}

func (m *ProjectResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return ProjectResultMultiError(errors)
	}

	return nil
}

// ProjectResultMultiError is an error wrapping multiple validation errors
// returned by ProjectResult.ValidateAll() if the designated constraints
// aren't met.
type ProjectResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProjectResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProjectResultMultiError) AllErrors() []error { return m }

// ProjectResultValidationError is the validation error returned by
// ProjectResult.Validate if the designated constraints aren't met.
type ProjectResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProjectResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProjectResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProjectResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProjectResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProjectResultValidationError) ErrorName() string { return "ProjectResultValidationError" }

// Error satisfies the builtin error interface
func (e ProjectResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProjectResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProjectResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProjectResultValidationError{}

// Validate checks the field values on ProjectParam with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProjectParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProjectParam with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProjectParamMultiError, or
// nil if none found.
func (m *ProjectParam) ValidateAll() error {
	return m.validate(true)
}

func (m *ProjectParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if len(errors) > 0 {
		return ProjectParamMultiError(errors)
	}

	return nil
}

// ProjectParamMultiError is an error wrapping multiple validation errors
// returned by ProjectParam.ValidateAll() if the designated constraints aren't met.
type ProjectParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProjectParamMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProjectParamMultiError) AllErrors() []error { return m }

// ProjectParamValidationError is the validation error returned by
// ProjectParam.Validate if the designated constraints aren't met.
type ProjectParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProjectParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProjectParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProjectParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProjectParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProjectParamValidationError) ErrorName() string { return "ProjectParamValidationError" }

// Error satisfies the builtin error interface
func (e ProjectParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProjectParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProjectParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProjectParamValidationError{}

// Validate checks the field values on ProjectListParam with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ProjectListParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProjectListParam with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProjectListParamMultiError, or nil if none found.
func (m *ProjectListParam) ValidateAll() error {
	return m.validate(true)
}

func (m *ProjectListParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PageNum

	// no validation rules for PageSize

	if len(errors) > 0 {
		return ProjectListParamMultiError(errors)
	}

	return nil
}

// ProjectListParamMultiError is an error wrapping multiple validation errors
// returned by ProjectListParam.ValidateAll() if the designated constraints
// aren't met.
type ProjectListParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProjectListParamMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProjectListParamMultiError) AllErrors() []error { return m }

// ProjectListParamValidationError is the validation error returned by
// ProjectListParam.Validate if the designated constraints aren't met.
type ProjectListParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProjectListParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProjectListParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProjectListParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProjectListParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProjectListParamValidationError) ErrorName() string { return "ProjectListParamValidationError" }

// Error satisfies the builtin error interface
func (e ProjectListParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProjectListParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProjectListParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProjectListParamValidationError{}
