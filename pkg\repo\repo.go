package repo

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path"
	"strings"
	"time"

	"golang.ttyuyin.com/harmony/csi-driver/pkg/bootstrap"
	"golang.ttyuyin.com/harmony/pkg/log"
)

const (
	defaultCloneDir   = "/data"
	defaultBranch     = "master"
	defaultMainBranch = "main"
	repoExt           = ".git"
	tmpDirExt         = ".tmp"
)

func New(repoURL, rootPath string) *Repository {
	if rootPath == "" {
		rootPath = defaultCloneDir
	}

	return &Repository{
		repoURL:  repoURL,
		rootPath: rootPath,
		repoDir:  extractRepoDir(repoURL),
		branch:   defaultBranch,
	}
}

type Repository struct {
	// git 仓库地址
	repoURL string
	// 克隆路径的根路径
	rootPath string
	// 仓库解析出来的目录
	repoDir string
	// 待克隆的分支
	branch string
	// 本地仓库路径，设置后将直接从该仓库复制一份进行切换
	localPath string
}

func (r *Repository) SetBranch(branch string) {
	if branch == "" {
		branch = defaultBranch
	}

	r.branch = branch
}

func (r *Repository) SetCloneFromLocalPath(localPath string) {
	r.localPath = localPath
}

func (r *Repository) checkLocalPath() bool {
	_, err := os.Stat(r.localPath)
	if err == nil {
		return true
	}

	return false
}

func (r *Repository) URL() string {
	if !strings.HasSuffix(r.repoURL, ".git") {
		return r.repoURL + ".git"
	}
	return r.repoURL
}

func (r *Repository) tempTargetDir() string {
	return r.TargetDir() + tmpDirExt
}

func (r *Repository) TargetDir() string {
	return path.Join(r.rootPath, r.repoDir, r.branch)
}

func (r *Repository) MainBranchTargetDir() string {
	return path.Join(r.rootPath, r.repoDir, defaultMainBranch)
}

func (r *Repository) Clone(ctx context.Context) {
	log.Infof("[Clone] start clone repo(%s) to %s", r.repoURL, r.TargetDir())

	exists := r.HasTargetDir()
	if exists {
		log.Infof("[Clone] the target dir(%s) already has data, skip clone", r.TargetDir())
		return
	}
	exists = r.HasTempTargetDir()
	if exists {
		log.Infof("[Clone] the tmp target dir(%s) already has data, change to available dir", r.tempTargetDir())
		return
	}

	var err error
	if r.localPath != "" && r.hasDir(r.localPath) {
		err = r.cloneFromLocal(ctx)
	} else {
		err = r.clone(ctx)
	}
	if err != nil {
		tmpDir := r.tempTargetDir()
		log.Warnf("[Clone] clone repo(%s) failed, remove dir %s", r.repoURL, tmpDir)
		_ = os.RemoveAll(tmpDir)
	}

	// Clone 之后拉取子模块 ,不报错，非必须
	errSub := r.updSubmodule(ctx)
	if errSub != nil {
		log.Warnf("[updSubmodule] repo(%s) failed, Err %v", r.repoURL, errSub)
	}
}

func (r *Repository) HasTargetDir() bool {
	// check dir is exists
	return r.hasDir(path.Join(r.TargetDir(), repoExt))
}

func (r *Repository) HasTempTargetDir() bool {
	// tmp dir
	targetDir := r.tempTargetDir()
	// check the tmp dir is exists
	return r.hasDir(path.Join(targetDir, repoExt))
}

func (r *Repository) cloneFromLocal(ctx context.Context) error {
	start := time.Now()
	defer func() {
		log.Infof("[Clone] clone from local %s to %s completed, cost: %v", r.localPath, r.TargetDir(), time.Since(start))
	}()

	// 首次拉master可能会失败，检查下路径
	_, err := os.Stat(r.localPath)
	if err != nil {
		log.Errorf("[Clone] local path %s is not exists", r.localPath)
		return err
	}

	dst := r.tempTargetDir()
	log.Infof("[Clone] start clone from local %s to %s", r.localPath, dst)
	err = copyBranch(ctx, r.localPath, dst)
	if err != nil {
		log.Errorf("[Clone] clone from local %s, copy dir failed, err: %v", r.localPath, err)
		return err
	}
	if err = gitFastCheckout(ctx, dst, r.branch); err != nil {
		log.Errorf("[Clone] dir %s checkout branch %s failed, err: %v", dst, r.branch, err)
		return err
	}

	log.Infof("[Clone] dir %s checkout branch %s completed, start change dir to available dir", dst, r.branch)
	return r.changeToAvailableDir(ctx, dst)
}

func (r *Repository) clone(ctx context.Context) error {
	targetDir := r.tempTargetDir()
	log.Infof("[Clone] start clone repo %s to %s", r.repoURL, targetDir)
	start := time.Now()
	defer func() {
		log.Infof("[Clone] clone repo(%s) to %s completed, cost: %v", r.repoURL, r.TargetDir(), time.Since(start))
	}()

	// create repo dir
	err := mkdir(ctx, targetDir)
	if err != nil {
		log.Errorf("[Clone] mkdir dir(%s) failed, err: %v", targetDir, err)
		return err
	}

	log.Infof("[Clone] start clone repo %s to %s", r.repoURL, targetDir)
	url := r.addCredentials()
	if err = gitClone(ctx, url, targetDir); err != nil {
		log.Errorf("[Clone] clone repo %s failed, err: %v", r.repoURL, err)
		return err
	}

	log.Infof("[Clone] repo %s clone completed, start checkout branch %s", r.repoURL, r.branch)
	if err = gitCheckout(ctx, targetDir, r.branch); err != nil {
		log.Errorf("[Clone] dir %s checkout branch %s failed, err: %v", targetDir, r.branch, err)
		return err
	}

	log.Infof("[Clone] dir %s checkout branch %s completed, start change dir to available dir", targetDir, r.branch)
	return r.changeToAvailableDir(ctx, targetDir)
}

func (r *Repository) addCredentials() string {
	gitCredentials := os.Getenv("GIT_CREDENTIALS")

	if gitCredentials == "" {
		return r.repoURL
	}

	url := gitCredentials + "/" + r.repoDir + repoExt
	return url
}

func (r *Repository) chown(ctx context.Context, targetDir string) bool {
	// check the repo is exists
	_, err := os.Stat(targetDir)
	if err != nil && os.IsNotExist(err) {
		log.Infof("chown dir(%s) is not exists, continue chown", targetDir)
		return false
	}

	// chown git user
	err = chownGitUser(ctx, targetDir)
	if err != nil {
		log.Errorf("chown dir(%s) failed, err: %v", targetDir, err)
		return false
	}
	return true
}

func (r *Repository) changeToAvailableDir(ctx context.Context, targetDir string) error {
	// 同步仓库也使用root 用户，规避掉 构建之后可能的权限问题 0314 cheny
	// local env is debug, skip chown
	if !bootstrap.DebugEnabled() {
		log.Infof("chown dir(%s) to git user", targetDir)
		ok := r.chown(ctx, targetDir)
		if ok {
			if err := gitUpdateIndex(ctx, targetDir); err != nil {
				log.Errorf("dir(%s) update-index failed, err: %v", targetDir, err)
				return err
			}
		} else {
			return errors.New(fmt.Sprintf("chown dir(%s) failed", targetDir))
		}
	}

	// rename
	log.Infof("rename dir(%s) to %s", targetDir, r.TargetDir())
	err := os.Rename(targetDir, r.TargetDir())
	if err != nil {
		log.Errorf("rename dir(%s) failed, err: %v", targetDir, err)
		return err
	}

	return nil
}

func (r *Repository) updSubmodule(ctx context.Context) (err error) {
	targetDir := r.TargetDir()
	err = gitUpdSubmodule(ctx, targetDir)
	if err != nil {
		log.Errorf("[updSubmodule] dir(%s) failed, err: %v", targetDir, err)
		return err
	}
	return
}

func (r *Repository) hasDir(dir string) (has bool) {
	// check the tmp dir is exists
	_, err := os.Stat(dir)
	if err == nil {
		return true
	}
	return
}

func extractRepoDir(url string) string {
	// remove .git
	url = strings.TrimSuffix(url, repoExt)

	items := strings.Split(url, "/")

	// remove protocol and domain
	repoDir := strings.Join(items[3:], "/")
	return repoDir
}
