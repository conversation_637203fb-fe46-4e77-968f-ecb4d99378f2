// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: iam/iamsvc_project_user.proto

package iam

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ProjectUserService_GetProjectUsersBy_FullMethodName = "/iam.ProjectUserService/GetProjectUsersBy"
	ProjectUserService_GetUserProjectsBy_FullMethodName = "/iam.ProjectUserService/GetUserProjectsBy"
	ProjectUserService_GetUserOfProject_FullMethodName  = "/iam.ProjectUserService/GetUserOfProject"
	ProjectUserService_CreateProjectUser_FullMethodName = "/iam.ProjectUserService/CreateProjectUser"
	ProjectUserService_UpdateProjectUser_FullMethodName = "/iam.ProjectUserService/UpdateProjectUser"
	ProjectUserService_GetProjectUserBy_FullMethodName  = "/iam.ProjectUserService/GetProjectUserBy"
	ProjectUserService_DeleteProjectUser_FullMethodName = "/iam.ProjectUserService/DeleteProjectUser"
)

// ProjectUserServiceClient is the client API for ProjectUserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ProjectUserServiceClient interface {
	GetProjectUsersBy(ctx context.Context, in *ProjectUserParam, opts ...grpc.CallOption) (*ProjectUserResponse, error)
	GetUserProjectsBy(ctx context.Context, in *UserProParam, opts ...grpc.CallOption) (*ProjectUserResponse, error)
	// 查询项目下的成员（user of project）
	GetUserOfProject(ctx context.Context, in *UserQuery, opts ...grpc.CallOption) (*UserPage, error)
	// 创建组成员
	CreateProjectUser(ctx context.Context, in *ProjectUser, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新组成员
	UpdateProjectUser(ctx context.Context, in *ProjectUser, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取用户是否存在项目中
	GetProjectUserBy(ctx context.Context, in *UserProParam, opts ...grpc.CallOption) (*UserInfo, error)
	// 删除项目用户
	DeleteProjectUser(ctx context.Context, in *UserProParam, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type projectUserServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewProjectUserServiceClient(cc grpc.ClientConnInterface) ProjectUserServiceClient {
	return &projectUserServiceClient{cc}
}

func (c *projectUserServiceClient) GetProjectUsersBy(ctx context.Context, in *ProjectUserParam, opts ...grpc.CallOption) (*ProjectUserResponse, error) {
	out := new(ProjectUserResponse)
	err := c.cc.Invoke(ctx, ProjectUserService_GetProjectUsersBy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectUserServiceClient) GetUserProjectsBy(ctx context.Context, in *UserProParam, opts ...grpc.CallOption) (*ProjectUserResponse, error) {
	out := new(ProjectUserResponse)
	err := c.cc.Invoke(ctx, ProjectUserService_GetUserProjectsBy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectUserServiceClient) GetUserOfProject(ctx context.Context, in *UserQuery, opts ...grpc.CallOption) (*UserPage, error) {
	out := new(UserPage)
	err := c.cc.Invoke(ctx, ProjectUserService_GetUserOfProject_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectUserServiceClient) CreateProjectUser(ctx context.Context, in *ProjectUser, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ProjectUserService_CreateProjectUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectUserServiceClient) UpdateProjectUser(ctx context.Context, in *ProjectUser, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ProjectUserService_UpdateProjectUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectUserServiceClient) GetProjectUserBy(ctx context.Context, in *UserProParam, opts ...grpc.CallOption) (*UserInfo, error) {
	out := new(UserInfo)
	err := c.cc.Invoke(ctx, ProjectUserService_GetProjectUserBy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectUserServiceClient) DeleteProjectUser(ctx context.Context, in *UserProParam, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ProjectUserService_DeleteProjectUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ProjectUserServiceServer is the server API for ProjectUserService service.
// All implementations must embed UnimplementedProjectUserServiceServer
// for forward compatibility
type ProjectUserServiceServer interface {
	GetProjectUsersBy(context.Context, *ProjectUserParam) (*ProjectUserResponse, error)
	GetUserProjectsBy(context.Context, *UserProParam) (*ProjectUserResponse, error)
	// 查询项目下的成员（user of project）
	GetUserOfProject(context.Context, *UserQuery) (*UserPage, error)
	// 创建组成员
	CreateProjectUser(context.Context, *ProjectUser) (*emptypb.Empty, error)
	// 更新组成员
	UpdateProjectUser(context.Context, *ProjectUser) (*emptypb.Empty, error)
	// 获取用户是否存在项目中
	GetProjectUserBy(context.Context, *UserProParam) (*UserInfo, error)
	// 删除项目用户
	DeleteProjectUser(context.Context, *UserProParam) (*emptypb.Empty, error)
	mustEmbedUnimplementedProjectUserServiceServer()
}

// UnimplementedProjectUserServiceServer must be embedded to have forward compatible implementations.
type UnimplementedProjectUserServiceServer struct {
}

func (UnimplementedProjectUserServiceServer) GetProjectUsersBy(context.Context, *ProjectUserParam) (*ProjectUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProjectUsersBy not implemented")
}
func (UnimplementedProjectUserServiceServer) GetUserProjectsBy(context.Context, *UserProParam) (*ProjectUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserProjectsBy not implemented")
}
func (UnimplementedProjectUserServiceServer) GetUserOfProject(context.Context, *UserQuery) (*UserPage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserOfProject not implemented")
}
func (UnimplementedProjectUserServiceServer) CreateProjectUser(context.Context, *ProjectUser) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateProjectUser not implemented")
}
func (UnimplementedProjectUserServiceServer) UpdateProjectUser(context.Context, *ProjectUser) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateProjectUser not implemented")
}
func (UnimplementedProjectUserServiceServer) GetProjectUserBy(context.Context, *UserProParam) (*UserInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProjectUserBy not implemented")
}
func (UnimplementedProjectUserServiceServer) DeleteProjectUser(context.Context, *UserProParam) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteProjectUser not implemented")
}
func (UnimplementedProjectUserServiceServer) mustEmbedUnimplementedProjectUserServiceServer() {}

// UnsafeProjectUserServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ProjectUserServiceServer will
// result in compilation errors.
type UnsafeProjectUserServiceServer interface {
	mustEmbedUnimplementedProjectUserServiceServer()
}

func RegisterProjectUserServiceServer(s grpc.ServiceRegistrar, srv ProjectUserServiceServer) {
	s.RegisterService(&ProjectUserService_ServiceDesc, srv)
}

func _ProjectUserService_GetProjectUsersBy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProjectUserParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectUserServiceServer).GetProjectUsersBy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectUserService_GetProjectUsersBy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectUserServiceServer).GetProjectUsersBy(ctx, req.(*ProjectUserParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectUserService_GetUserProjectsBy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserProParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectUserServiceServer).GetUserProjectsBy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectUserService_GetUserProjectsBy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectUserServiceServer).GetUserProjectsBy(ctx, req.(*UserProParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectUserService_GetUserOfProject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserQuery)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectUserServiceServer).GetUserOfProject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectUserService_GetUserOfProject_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectUserServiceServer).GetUserOfProject(ctx, req.(*UserQuery))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectUserService_CreateProjectUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProjectUser)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectUserServiceServer).CreateProjectUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectUserService_CreateProjectUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectUserServiceServer).CreateProjectUser(ctx, req.(*ProjectUser))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectUserService_UpdateProjectUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProjectUser)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectUserServiceServer).UpdateProjectUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectUserService_UpdateProjectUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectUserServiceServer).UpdateProjectUser(ctx, req.(*ProjectUser))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectUserService_GetProjectUserBy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserProParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectUserServiceServer).GetProjectUserBy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectUserService_GetProjectUserBy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectUserServiceServer).GetProjectUserBy(ctx, req.(*UserProParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectUserService_DeleteProjectUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserProParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectUserServiceServer).DeleteProjectUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectUserService_DeleteProjectUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectUserServiceServer).DeleteProjectUser(ctx, req.(*UserProParam))
	}
	return interceptor(ctx, in, info, handler)
}

// ProjectUserService_ServiceDesc is the grpc.ServiceDesc for ProjectUserService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ProjectUserService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "iam.ProjectUserService",
	HandlerType: (*ProjectUserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetProjectUsersBy",
			Handler:    _ProjectUserService_GetProjectUsersBy_Handler,
		},
		{
			MethodName: "GetUserProjectsBy",
			Handler:    _ProjectUserService_GetUserProjectsBy_Handler,
		},
		{
			MethodName: "GetUserOfProject",
			Handler:    _ProjectUserService_GetUserOfProject_Handler,
		},
		{
			MethodName: "CreateProjectUser",
			Handler:    _ProjectUserService_CreateProjectUser_Handler,
		},
		{
			MethodName: "UpdateProjectUser",
			Handler:    _ProjectUserService_UpdateProjectUser_Handler,
		},
		{
			MethodName: "GetProjectUserBy",
			Handler:    _ProjectUserService_GetProjectUserBy_Handler,
		},
		{
			MethodName: "DeleteProjectUser",
			Handler:    _ProjectUserService_DeleteProjectUser_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "iam/iamsvc_project_user.proto",
}
