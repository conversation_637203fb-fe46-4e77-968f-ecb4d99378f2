package apollo_cfgs

import (
	"fmt"
	"testing"

	"github.com/apolloconfig/agollo/v4"
	"github.com/apolloconfig/agollo/v4/env/config"
)

func Test_ApolloProd5(t *testing.T) {
	namespace := "wangchen.txt"
	// namespace = "tt-cicd-config-7.txt"
	c := &config.AppConfig{
		AppID:             "cia",
		Cluster:           "default",
		IP:                "https://config.ttyuyin.com",
		NamespaceName:     namespace,
		IsBackupConfig:    true,
		Secret:            "0a8ecb026b8c4276900e46269e62ff14",
		Label:             "appName",
		MustStart:         true,
		SyncServerTimeout: 10,
	}

	// 1988eded21e05acac7a719513e312e7e90ee0792c8c4cce0699b4f9bc7a24fdb

	client, err := agollo.StartWithConfig(func() (*config.AppConfig, error) {
		return c, nil
	})
	if err != nil {
		t.<PERSON>rrorf("初始化Apollo配置失败 %v", err)
		return
	}
	fmt.Println("初始化Apollo配置成功")

	listener := &nodeCfgListener{}

	client.AddChangeListener(listener)

	//Use your apollo key to test
	cfg := client.GetConfig(namespace)
	// 等待配置获取完成
	ct := cfg.GetStringValue("content", "")
	fmt.Println(ct)

	// content := cfg.GetContent()
	// fmt.Println(content)

	// select {}
}

func Test_ApolloProd3(t *testing.T) {
	namespace := "tt-cicd-config-28.txt"
	// namespace = "tt-cicd-config-7.txt"
	c := &config.AppConfig{
		AppID:             "tt-cicd-qa",
		Cluster:           "default",
		IP:                "http://************:8080",
		NamespaceName:     namespace,
		IsBackupConfig:    true,
		Secret:            "a2ce0a922cd34466b4af6b720a5854b5",
		Label:             "appName",
		MustStart:         true,
		SyncServerTimeout: 10,
	}

	// 1988eded21e05acac7a719513e312e7e90ee0792c8c4cce0699b4f9bc7a24fdb

	client, err := agollo.StartWithConfig(func() (*config.AppConfig, error) {
		return c, nil
	})
	if err != nil {
		t.Errorf("初始化Apollo配置失败 %v", err)
		return
	}
	fmt.Println("初始化Apollo配置成功")

	listener := &nodeCfgListener{}

	client.AddChangeListener(listener)

	//Use your apollo key to test
	cfg := client.GetConfig(namespace)
	// 等待配置获取完成
	ct := cfg.GetStringValue("content", "")
	fmt.Println(ct)

	// content := cfg.GetContent()
	// fmt.Println(content)

	// select {}
}

func Test_ApolloProd(t *testing.T) {
	namespace := "tt-cicd-config-28.txt"
	// namespace = "tt-cicd-config-7.txt"
	c := &config.AppConfig{
		AppID:             "tt-cicd-qa",
		Cluster:           "default",
		IP:                "http://apolloconfig.svc.quwan.local",
		NamespaceName:     namespace,
		IsBackupConfig:    true,
		Secret:            "0bea9d90952745cf970ece5e4ba955fc",
		Label:             "appName",
		MustStart:         true,
		SyncServerTimeout: 10,
	}

	// 1988eded21e05acac7a719513e312e7e90ee0792c8c4cce0699b4f9bc7a24fdb

	client, err := agollo.StartWithConfig(func() (*config.AppConfig, error) {
		return c, nil
	})
	if err != nil {
		t.Errorf("初始化Apollo配置失败 %v", err)
		return
	}
	fmt.Println("初始化Apollo配置成功")

	listener := &nodeCfgListener{}

	client.AddChangeListener(listener)

	//Use your apollo key to test
	cfg := client.GetConfig(namespace)
	// 等待配置获取完成
	ct := cfg.GetStringValue("content", "")
	fmt.Println(ct)

	// content := cfg.GetContent()
	// fmt.Println(content)

	// select {}
}

func Test_extractRepoDir(t *testing.T) {
	// namespace := "txt7.txt"
	namespace := "tt-cicd-config-33.txt"
	c := &config.AppConfig{
		AppID:             "tt-cicd-dev",
		Cluster:           "default",
		IP:                "http://cicd-qa3.ttyuyin.com/b/",
		NamespaceName:     namespace,
		IsBackupConfig:    true,
		Secret:            "78e9e53309a14662a2139031fad8bf0b",
		Label:             "appName",
		MustStart:         true,
		SyncServerTimeout: 10,
	}

	// 1988eded21e05acac7a719513e312e7e90ee0792c8c4cce0699b4f9bc7a24fdb

	client, err := agollo.StartWithConfig(func() (*config.AppConfig, error) {
		return c, nil
	})
	if err != nil {
		t.Errorf("初始化Apollo配置失败 %v", err)
		return
	}
	fmt.Println("初始化Apollo配置成功")

	listener := &nodeCfgListener{}

	client.AddChangeListener(listener)

	//Use your apollo key to test
	cfg := client.GetConfig(namespace)
	// 等待配置获取完成
	ct := cfg.GetStringValue("content", "")
	fmt.Println(ct)

	// content := cfg.GetContent()
	// fmt.Println(content)

	// select {}
}
