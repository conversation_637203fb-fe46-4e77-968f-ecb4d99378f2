package apollo_cfgs

import (
	"fmt"
	"testing"

	"github.com/philchia/agollo/v4"
)

func Test_ApolloProd51(t *testing.T) {
	namespace := "wangchen.txt"
	apollo := agollo.NewClient(&agollo.Conf{
		AppID:           "cia",
		Cluster:         "default",
		NameSpaceNames:  []string{namespace},
		MetaAddr:        "https://config.ttyuyin.com",
		AccesskeySecret: "0a8ecb026b8c4276900e46269e62ff14",
	})

	err := apollo.Start()
	if err != nil {
		t.Errorf("初始化Apollo配置失败 %v", err)
		return
	}

	val := apollo.GetString("content")

	fmt.Println(val)
}

func Test_ApolloProd1(t *testing.T) {
	namespace := "tt-cicd-config-28.txt"
	apollo := agollo.NewClient(&agollo.Conf{
		AppID:           "tt-cicd-qa",
		Cluster:         "default",
		NameSpaceNames:  []string{namespace},
		MetaAddr:        "https://config.ttyuyin.com",
		AccesskeySecret: "0bea9d90952745cf970ece5e4ba955fc",
	})

	err := apollo.Start()
	if err != nil {
		t.Errorf("初始化Apollo配置失败 %v", err)
		return
	}

	val := apollo.GetString("content")

	fmt.Println(val)
}
