FROM alpine:latest

RUN apk --update --no-cache add bash git mysql-client parallel sudo



WORKDIR /ttyuyin

# Perform any further action as an unprivileged user.
ENV USER_ID=65532
ENV GROUP_ID=65532
ENV USER_NAME=git
ENV GROUP_NAME=git

RUN addgroup -g $GROUP_ID $GROUP_NAME &&\
    adduser -D $USER_NAME --uid $USER_ID --ingroup $GROUP_NAME && mkdir -p /etc/sudoers.d \
    && echo "$USER_NAME ALL=(ALL) NOPASSWD: ALL" > /etc/sudoers.d/$USER_NAME \
    && chmod 0440 /etc/sudoers.d/$USER_NAME && \
    chown $USER_ID:$GROUP_ID /ttyuyin/

# setup entrypoint
COPY --chown=$USER_ID:$GROUP_ID ./full-sync.sh ./increment-sync.sh  /ttyuyin/

USER $USER_NAME

CMD ["/bin/bash"]