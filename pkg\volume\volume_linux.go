package volume

import (
	"fmt"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"unsafe"

	"golang.org/x/sys/unix"
)

type ProvisionType string

const (
	Init  ProvisionType = "init"
	Retry ProvisionType = "retry"
	UID                 = 0
	GID                 = 65532
)

type volume struct {
	lowerDir  string
	upperDir  string
	workDir   string
	mergedDir string
	pvType    ProvisionType
}

func NewVolume(lowerDir, upperDir, workDir, mergedDir string) *volume {
	return &volume{
		lowerDir:  lowerDir,
		upperDir:  upperDir,
		workDir:   workDir,
		mergedDir: mergedDir,
	}
}

func (v *volume) Mount() error {
	// when there is retry pipeline want to mount a same nfspath, we just skip mount and return success
	if exists := isExported(v.mergedDir); exists {
		return nil
	}

	if err := os.MkdirAll(v.upperDir, 0755); err != nil {
		return fmt.Errorf("failed to create upperdir: %v", err)
	}

	if err := os.MkdirAll(v.workDir, 0755); err != nil {
		return fmt.Errorf("failed to create workdir: %v", err)
	}

	if err := os.MkdirAll(v.mergedDir, 0755); err != nil {
		return fmt.Errorf("failed to create mergeddir: %v", err)
	}

	// change dir own to 65532 for pipeline pod can write to the dir
	if err := os.Chown(v.upperDir, UID, GID); err != nil {
		return fmt.Errorf("change dir own error: %v", err)
	}

	// The mount options.
	options := fmt.Sprintf("lowerdir=%s,upperdir=%s,workdir=%s,index=on,nfs_export=on", v.lowerDir, v.upperDir, v.workDir)
	// optionsPtr := stringToUnsafePointer(options)

	//Mount the overlay.
	if err := unix.Mount("overlay", v.mergedDir, "overlay", 0, options); err != nil {
		return fmt.Errorf("failed to mount overlay with options %s: %v", options, err)
	}

	return nil

}

// export mounted dir to nfs using exportfs
func (v *volume) Export(fsid string) error {
	// TODO: merge export and mount func to a single func to avoid condition  decision twice
	if exists := isExported(v.mergedDir); exists {
		return nil
	}

	//      when multiple overlayfs has been exported, you should use different fsid for each overlayfs.
	// FYI: otherwise nfs-server cann't determite the correct exportfs record becase of overlayfs is a union fs
	//      this will lead a unexpected behavior, each different nfs client will mount same overlayfs(usually the last mounted overlayfs)
	fsidInt, err := strconv.Atoi(fsid)
	if err != nil {
		return fmt.Errorf("failed to convert fsid to int: %v", err)
	}

	// The export options. all_squash need to be set to allow root user to write to the nfs share.
	options := "rw,sync,no_subtree_check,all_squash,insecure,anonuid=0,anongid=0"

	options = options + ",fsid=" + strconv.Itoa(fsidInt)

	cmd := exec.Command("/usr/sbin/exportfs", "-o", options, "*:"+v.mergedDir)

	stdout, err := cmd.Output()

	if err != nil {
		return fmt.Errorf("failed to export dir %s: %v, output: %v", v.mergedDir, err, stdout)
	}
	return nil
}

func Umount(mergedDir string) error {
	if exists := isExported(mergedDir); exists {
		cmd := exec.Command("/usr/sbin/exportfs", "-u", "*:"+mergedDir)
		stdout, err := cmd.Output()
		if err != nil {
			return fmt.Errorf("failed to unexport dir %s: %v, output: %v", mergedDir, err, stdout)
		}
	}

	// Check if the directory has been mounted.
	cmd := exec.Command("/bin/mountpoint", "-q", mergedDir)
	if err := cmd.Run(); err == nil {
		// Umount the overlay.
		if err := unix.Unmount(mergedDir, 0); err != nil {
			return fmt.Errorf("failed to unmount overlay %s: %v", mergedDir, err)
		}
	}

	return nil
}

func stringToUnsafePointer(str string) unsafe.Pointer {
	b := append([]byte(str), 0) // NULL terminated
	return unsafe.Pointer(&b[0])
}

func isExported(mergedDir string) bool {
	// Check if the directory has been exported.
	etabData, err := os.ReadFile("/var/lib/nfs/etab")
	if err != nil {
		return false
	}
	if strings.Contains(string(etabData), mergedDir) {
		return true
	}
	return false
}
