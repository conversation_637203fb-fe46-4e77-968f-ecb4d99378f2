apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/instance: csi-driver-in-cluster-default
    app.oam.dev/appRevision: ""
    app.oam.dev/component: csi-driver
    app.oam.dev/name: csi-driver
    app.oam.dev/namespace: default
    app.oam.dev/resourceType: WORKLOAD
    workload.oam.dev/type: general
  name: csi-driver
  namespace: default
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.oam.dev/component: csi-driver
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.oam.dev/component: csi-driver
        app.oam.dev/name: csi-driver
    spec:
      containers:
      - env:
        - name: NFS_SERVER
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        - name: NFS_VERSION
          value: "3"
        - name: REPOSYNC_GRPC_TARGET
          value: "csi-reposync:9050"
        image: cr.ttyuyin.com/devops/1/csi-driver:V20250116093327-ded3675 
        imagePullPolicy: IfNotPresent
        name: csi-driver
        resources:
          limits:
            cpu: "3"
            memory: 2048Mi
          requests:
            cpu: 500m
            memory: 512Mi
        securityContext:
          privileged: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /gitlab-repo
          name: test-volume
        - mountPath: /data/csi-v2
          name: test-volume
      dnsPolicy: ClusterFirst
      nodeSelector:
        kubernetes.io/hostname: ************
      tolerations:
        - key: tt.com/cicd
          value: csi
          effect: NoSchedule
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: csi-driver
      serviceAccountName: csi-driver
      terminationGracePeriodSeconds: 30
      volumes:
      - hostPath:
          path: /data/gitlab-sync/
          type: DirectoryOrCreate
        name: test-volume
