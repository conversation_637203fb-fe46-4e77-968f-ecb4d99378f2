kind: DaemonSet
apiVersion: apps/v1
metadata:
  name: ttcicd-csi-node
  namespace: default
spec:
  selector:
    matchLabels:
      app: ttcicd-csi-node
  template:
    metadata:
      labels:
        app: ttcicd-csi-node
    spec:
      serviceAccountName: cicd-csi-node
      tolerations:
        - operator: Exists
      priorityClassName: system-node-critical
      dnsPolicy: ClusterFirstWithHostNet
      containers:
        - args:
            - --endpoint=$(CSI_ENDPOINT)
            - --nodeid=$(NODE_NAME)
            - --sertype=NodeServer
          env:
            - name: CICD_REDIS_ADDR
              value: *********:6379
            - name: APOLLO_IP
              value: http://apolloconfig.svc.quwan.local
            - name: APOLLO_APPID_1
              value: tt-cicd-dev
            - name: APOLLO_APPSECRET_1
              value: f100d16b39fc4833a6da8c766151d816
            - name: APOLLO_APPID_2
              value: tt-cicd-qa
            - name: APOLLO_APPSECRET_2
              value: a2ce0a922cd34466b4af6b720a5854b5
            - name: APOLLO_APPID_3
              value: tt-cicd-stag
            - name: APOLLO_APPSECRET_3
              value: f89958af18dc43548a2756932a90884a
            - name: APOLLO_APPID_4
              value: tt-cicd-prod
            - name: APOLLO_APPSECRET_4
              value: b61402621b0c498280a9102fa663b39c
            - name: CSI_ENDPOINT
              value: unix:/csi/csi.sock
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          image: cr.ttyuyin.com/devops/overseas/cicd-csi/node-plug:v0.4.0
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - rm /csi/csi.sock
          livenessProbe:
            failureThreshold: 5
            httpGet:
              path: /healthz
              port: healthz
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 3
          name: csi-plugin
          ports:
            - containerPort: 9909
              name: healthz
              protocol: TCP
          securityContext:
            privileged: true
          volumeMounts:
            - mountPath: /var/lib/kubelet
              mountPropagation: Bidirectional
              name: kubelet-dir
            - mountPath: /csi
              name: plugin-dir
            - mountPath: /registration
              name: registration-dir
            - mountPath: /data/tt-cicd-csi/
              name: mnt
        - args:
            - --csi-address=$(ADDRESS)
            - --kubelet-registration-path=$(DRIVER_REG_SOCK_PATH)
            - --v=5
          env:
            - name: ADDRESS
              value: /csi/csi.sock
            - name: DRIVER_REG_SOCK_PATH
              value: /var/lib/kubelet/csi-plugins/com.ttyuyin.cicd.csi/csi.sock
          image: cr.ttyuyin.com/devops/overseas/k8scsi-csi-node-driver-registrar:v2.1.0
          name: node-driver-registrar
          volumeMounts:
            - mountPath: /csi
              name: plugin-dir
            - mountPath: /registration
              name: registration-dir
        - args:
            - --csi-address=$(ADDRESS)
            - --health-port=$(HEALTH_PORT)
          env:
            - name: ADDRESS
              value: /csi/csi.sock
            - name: HEALTH_PORT
              value: "9909"
          image: cr.ttyuyin.com/devops/overseas/k8scsi-livenessprobe:v1.1.0
          name: liveness-probe
          volumeMounts:
            - mountPath: /csi
              name: plugin-dir
      volumes:
        - hostPath:
            path: /var/lib/kubelet
            type: Directory
          name: kubelet-dir
        - hostPath:
            path: /var/lib/kubelet/csi-plugins/com.ttyuyin.cicd.csi/
            type: DirectoryOrCreate
          name: plugin-dir
        - hostPath:
            path: /var/lib/kubelet/plugins_registry/
            type: Directory
          name: registration-dir
        - hostPath:
            path: /data/tt-cicd-csi/
            type: DirectoryOrCreate
          name: mnt
