// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: pipeline/template.proto

package pipeline

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PipelineTemplateService_UpdateTemplateTaskImageConfig_FullMethodName = "/pipeline.PipelineTemplateService/UpdateTemplateTaskImageConfig"
	PipelineTemplateService_FindTemplateByImageID_FullMethodName         = "/pipeline.PipelineTemplateService/FindTemplateByImageID"
)

// PipelineTemplateServiceClient is the client API for PipelineTemplateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PipelineTemplateServiceClient interface {
	UpdateTemplateTaskImageConfig(ctx context.Context, in *UpdateTemplateTaskImageConfigReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	FindTemplateByImageID(ctx context.Context, in *FindTemplateBYImageIDReq, opts ...grpc.CallOption) (*FindTemplateBYImageIDResp, error)
}

type pipelineTemplateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPipelineTemplateServiceClient(cc grpc.ClientConnInterface) PipelineTemplateServiceClient {
	return &pipelineTemplateServiceClient{cc}
}

func (c *pipelineTemplateServiceClient) UpdateTemplateTaskImageConfig(ctx context.Context, in *UpdateTemplateTaskImageConfigReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PipelineTemplateService_UpdateTemplateTaskImageConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineTemplateServiceClient) FindTemplateByImageID(ctx context.Context, in *FindTemplateBYImageIDReq, opts ...grpc.CallOption) (*FindTemplateBYImageIDResp, error) {
	out := new(FindTemplateBYImageIDResp)
	err := c.cc.Invoke(ctx, PipelineTemplateService_FindTemplateByImageID_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PipelineTemplateServiceServer is the server API for PipelineTemplateService service.
// All implementations must embed UnimplementedPipelineTemplateServiceServer
// for forward compatibility
type PipelineTemplateServiceServer interface {
	UpdateTemplateTaskImageConfig(context.Context, *UpdateTemplateTaskImageConfigReq) (*emptypb.Empty, error)
	FindTemplateByImageID(context.Context, *FindTemplateBYImageIDReq) (*FindTemplateBYImageIDResp, error)
	mustEmbedUnimplementedPipelineTemplateServiceServer()
}

// UnimplementedPipelineTemplateServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPipelineTemplateServiceServer struct {
}

func (UnimplementedPipelineTemplateServiceServer) UpdateTemplateTaskImageConfig(context.Context, *UpdateTemplateTaskImageConfigReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTemplateTaskImageConfig not implemented")
}
func (UnimplementedPipelineTemplateServiceServer) FindTemplateByImageID(context.Context, *FindTemplateBYImageIDReq) (*FindTemplateBYImageIDResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindTemplateByImageID not implemented")
}
func (UnimplementedPipelineTemplateServiceServer) mustEmbedUnimplementedPipelineTemplateServiceServer() {
}

// UnsafePipelineTemplateServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PipelineTemplateServiceServer will
// result in compilation errors.
type UnsafePipelineTemplateServiceServer interface {
	mustEmbedUnimplementedPipelineTemplateServiceServer()
}

func RegisterPipelineTemplateServiceServer(s grpc.ServiceRegistrar, srv PipelineTemplateServiceServer) {
	s.RegisterService(&PipelineTemplateService_ServiceDesc, srv)
}

func _PipelineTemplateService_UpdateTemplateTaskImageConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTemplateTaskImageConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineTemplateServiceServer).UpdateTemplateTaskImageConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineTemplateService_UpdateTemplateTaskImageConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineTemplateServiceServer).UpdateTemplateTaskImageConfig(ctx, req.(*UpdateTemplateTaskImageConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineTemplateService_FindTemplateByImageID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindTemplateBYImageIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineTemplateServiceServer).FindTemplateByImageID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineTemplateService_FindTemplateByImageID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineTemplateServiceServer).FindTemplateByImageID(ctx, req.(*FindTemplateBYImageIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PipelineTemplateService_ServiceDesc is the grpc.ServiceDesc for PipelineTemplateService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PipelineTemplateService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pipeline.PipelineTemplateService",
	HandlerType: (*PipelineTemplateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateTemplateTaskImageConfig",
			Handler:    _PipelineTemplateService_UpdateTemplateTaskImageConfig_Handler,
		},
		{
			MethodName: "FindTemplateByImageID",
			Handler:    _PipelineTemplateService_FindTemplateByImageID_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pipeline/template.proto",
}
