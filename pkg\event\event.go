package event

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

type UserInfo struct {
	UserID      int64
	Username    string
	ChineseName string
	EmployeeNo  string
	Roles       []string
	ProjectID   int64
}

func (ui *UserInfo) String() string {
	return fmt.Sprintf("UserInfo:{ uid:%d username:%s displayName:%s(%s) project:%d roles:%v }",
		ui.UserID, ui.Username, ui.ChineseName, ui.EmployeeNo, ui.ProjectID, ui.Roles)
}

func (ui *UserInfo) RolesString() string {
	return strings.Join(ui.Roles, ",")
}

type RequestInfo struct {
	UserInfo
	RequestID string
}

func (ri *RequestInfo) String() string {
	return fmt.Sprintf("RequestInfo:{ uid:%d username:%s displayName:%s(%s) project:%d roles:%v requestID:%s }",
		ri.UserID, ri.Username, ri.ChineseName, ri.EmployeeNo, ri.ProjectID, ri.Roles, ri.RequestID)
}

type Event struct {
	EventTime time.Time   `json:"eventTime"`
	Types     string      `json:"types"`
	Sources   string      `json:"source"`
	Datas     any         `json:"data"`
	MsgId     string      `json:"msgId"`
	TktEvtId  string      `json:"tktEvtId"`
	ReqInfo   RequestInfo `json:"reqInfo"`
}

func (e *Event) SetID(msgId string) {
	e.MsgId = msgId
}

func (e *Event) SetType(types string) {
	e.Types = types
}

func (e *Event) SetSource(source string) {
	e.Sources = source
}

// contentType JSON  Only
func (e *Event) SetData(contentType string, data any) (err error) {
	e.Datas = data
	return
}

func (e *Event) SetDatas(data any) {
	e.Datas = data
}

func (e *Event) ID() (msgId string) {
	return e.MsgId
}

func (e *Event) Type() (types string) {
	return e.Types
}

func (e *Event) Source() (source string) {
	return e.Sources
}

func (e *Event) String() (str string) {
	return
}

func (e *Event) Time() time.Time {
	return e.EventTime
}

func (e *Event) Data() []byte {
	b, err := json.Marshal(e.Datas)
	if err != nil {
		return make([]byte, 0)
	}
	return b
}

// DataAs attempts to populate the provided data object with the event payload.
// obj should be a pointer type.
func (e *Event) DataAs(obj interface{}) error {
	if obj == nil {
		return nil
	}
	data := e.Data()
	if len(data) == 0 {
		// No data.
		return nil
	}
	return json.Unmarshal(data, obj)
}
