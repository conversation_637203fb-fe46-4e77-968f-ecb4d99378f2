package csi

import (
	"context"
	"fmt"
	"net"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"strings"

	"github.com/container-storage-interface/spec/lib/go/csi"
	"github.com/redis/go-redis/v9"
	"golang.ttyuyin.com/harmony/csi-driver/pkg/csi/apollo_cfgs"
	"google.golang.org/grpc"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/klog/v2"
)

const (
	// DriverName to be registered
	DriverName        = "com.ttyuyin.cicd.csi"
	CSIHostPath       = "/data/tt-cicd-csi/ser"
	CSIHostPathGlobal = "/data/tt-cicd-csi/global"
	GlobalCfgCh       = "tt-cicd-csi-global-data"
)

type Driver struct {
	identitySer
	nodeService
	controllerService
	srv      *grpc.Server
	endpoint string
}

// NewDriver creates a new Driver
func NewDriver(endpoint, nodeID, serType string) *Driver {
	klog.Infof("Driver: %v serType:%s", DriverName, serType)

	driver := &Driver{
		endpoint: endpoint,
	}

	config, err := rest.InClusterConfig()
	if err != nil {
		klog.Fatalln(err.Error())
	}
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		klog.Fatalf("Failed to create K8S client: %v", err)
	}

	if serType == "NodeServer" {
		redisCli, err := InitRedis()
		if err != nil {
			klog.Fatalf("Failed to Init Redis client: %v", err)
		}

		mng, err := apollo_cfgs.InitPodCfgsMng()
		if err != nil {
			klog.Fatalf("Failed to Init PodCfgsMng(Apollo Clint): %v", err)
		}

		driver.nodeService = newNodeService(nodeID, clientset, redisCli, mng)

		err = driver.nodeService.recovery()
		if err != nil {
			klog.Fatalf("Failed to recovery : %v", err)
		}
	} else {
		driver.controllerService = newControllerService(clientset)
		driver.controllerService.pvcMng.Start()
	}

	return driver
}

func (d *Driver) Run() error {
	scheme, addr, err := ParseEndpoint(d.endpoint)
	if err != nil {
		return err
	}

	listener, err := net.Listen(scheme, addr)
	if err != nil {
		return err
	}

	logErr := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		resp, err := handler(ctx, req)
		if err != nil {
			klog.Errorf("GRPC error: %v", err)
		}
		return resp, err
	}
	opts := []grpc.ServerOption{
		grpc.UnaryInterceptor(logErr),
	}
	d.srv = grpc.NewServer(opts...)

	csi.RegisterIdentityServer(d.srv, d)
	csi.RegisterControllerServer(d.srv, d)
	csi.RegisterNodeServer(d.srv, d)

	klog.Infof("Listening for connection on address: %#v", listener.Addr())
	return d.srv.Serve(listener)
}

func ParseEndpoint(endpoint string) (string, string, error) {
	u, err := url.Parse(endpoint)
	if err != nil {
		return "", "", fmt.Errorf("could not parse endpoint: %v", err)
	}

	addr := path.Join(u.Host, filepath.FromSlash(u.Path))

	scheme := strings.ToLower(u.Scheme)
	switch scheme {
	case "tcp":
	case "unix":
		addr = path.Join("/", addr)
		if err := os.Remove(addr); err != nil && !os.IsNotExist(err) {
			return "", "", fmt.Errorf("could not remove unix domain socket %q: %v", addr, err)
		}
	default:
		return "", "", fmt.Errorf("unsupported protocol: %s", scheme)
	}

	return scheme, addr, nil
}

func InitRedis() (redisCli *redis.Client, err error) {
	config := &redis.Options{
		Addr: os.Getenv("CICD_REDIS_ADDR"),
	}

	redisCli = redis.NewClient(config)
	err = redisCli.Ping(context.TODO()).Err()
	if err != nil {
		return
	}
	return
}
