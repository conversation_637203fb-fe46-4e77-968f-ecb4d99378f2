apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/instance: csi-reposync-in-cluster-default
    app.oam.dev/appRevision: ""
    app.oam.dev/component: csi-reposync
    app.oam.dev/name: csi-reposync
    app.oam.dev/namespace: default
    app.oam.dev/resourceType: WORKLOAD
    workload.oam.dev/type: general
  name: csi-reposync
  namespace: default
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.oam.dev/component: csi-reposync
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.oam.dev/component: csi-reposync
        app.oam.dev/name: csi-reposync
    spec:
      containers:
      - imagePullPolicy: IfNotPresent
        env:
        - name: NFS_SERVER
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        - name: MODE
          value: "prod"
        - name: ENV
          value: "qa"
        - name: LOWER_DIR_ROOT_PATH
          value: "/tmp/clone"
        - name: LOWER_DIR_VERSION
          value: "v2"
        - name: REPO_ROOT_PATH
          value: "/data/gitlab-sync"
        - name: DB_HOST
          value: "************"
        - name: DB_PORT
          value: "3306"
        - name: DB_USERNAME
          value: "rd_dev"
        - name: DB_PASSWORD
          value: "vRcfj3W#2nGdBeu@"
        - name: DB_NAME
          value: "app"
        - name: GIT_CREDENTIALS
          value: "https://sys_cicd_root:<EMAIL>"
        - name: KAFKA_PL_E_NAME
          value: "reposync-receive-pipeline"
        - name: KAFKA_PL_TOPICS
          value: "cicd-pipeline-event-test"
        - name: KAFKA_APP_E_NAME
          value: "reposync-receive-app"
        - name: KAFKA_APP_TOPICS
          value: "cicd-app-event"
        - name: KAFKA_BROKERS
          value: "************:9092"
        - name: KAFKA_TOPICS
          value: "cicd-pipeline-event-dev"
        - name: REPOSYNC_GRPC_TARGET
          value: ":9050"
        - name: PIPELINE_RUN_GRPC_TARGET
          value: "cicd-pipeline:9000"
        image: cr.ttyuyin.com/devops/1/csi-driver:V20240705153629-42e16bd
        name: csi-reposync
        resources:
          limits:
            cpu: "3"
            memory: 2048Mi
          requests:
            cpu: 500m
            memory: 512Mi
        securityContext:
          privileged: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /tmp/clone
          name: test-volume
        - mountPath: /data/gitlab-sync
          name: test-volume
      dnsPolicy: ClusterFirst
      nodeSelector:
        kubernetes.io/hostname: ************
      tolerations:
        - key: tt.com/cicd
          value: csi
          effect: NoSchedule
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: csi-driver
      serviceAccountName: csi-driver
      terminationGracePeriodSeconds: 30
      volumes:
      - hostPath:
          path: /data/gitlab-sync/
          type: DirectoryOrCreate
        name: test-volume

---

apiVersion: v1
kind: Service
metadata:
  annotations: {}
  labels:
    app.oam.dev/component: csi-reposync
    app.oam.dev/name: csi-reposync
    app.oam.dev/namespace: default
  name: csi-reposync
  namespace: default
spec:
  ports:
  - name: rpc
    port: 9050
    protocol: TCP
    targetPort: 9050
  selector:
    app.oam.dev/component: csi-reposync
  type: ClusterIP