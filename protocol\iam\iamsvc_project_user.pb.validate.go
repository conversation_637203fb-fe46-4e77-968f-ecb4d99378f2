// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: iam/iamsvc_project_user.proto

package iam

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ProjectUser with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProjectUser) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProjectUser with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProjectUserMultiError, or
// nil if none found.
func (m *ProjectUser) ValidateAll() error {
	return m.validate(true)
}

func (m *ProjectUser) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for UserId

	// no validation rules for ProjectId

	// no validation rules for Role

	// no validation rules for ChineseName

	if len(errors) > 0 {
		return ProjectUserMultiError(errors)
	}

	return nil
}

// ProjectUserMultiError is an error wrapping multiple validation errors
// returned by ProjectUser.ValidateAll() if the designated constraints aren't met.
type ProjectUserMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProjectUserMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProjectUserMultiError) AllErrors() []error { return m }

// ProjectUserValidationError is the validation error returned by
// ProjectUser.Validate if the designated constraints aren't met.
type ProjectUserValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProjectUserValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProjectUserValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProjectUserValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProjectUserValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProjectUserValidationError) ErrorName() string { return "ProjectUserValidationError" }

// Error satisfies the builtin error interface
func (e ProjectUserValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProjectUser.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProjectUserValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProjectUserValidationError{}

// Validate checks the field values on ProjectUserResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProjectUserResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProjectUserResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProjectUserResponseMultiError, or nil if none found.
func (m *ProjectUserResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProjectUserResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetProjectUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProjectUserResponseValidationError{
						field:  fmt.Sprintf("ProjectUsers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProjectUserResponseValidationError{
						field:  fmt.Sprintf("ProjectUsers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProjectUserResponseValidationError{
					field:  fmt.Sprintf("ProjectUsers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProjectUserResponseMultiError(errors)
	}

	return nil
}

// ProjectUserResponseMultiError is an error wrapping multiple validation
// errors returned by ProjectUserResponse.ValidateAll() if the designated
// constraints aren't met.
type ProjectUserResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProjectUserResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProjectUserResponseMultiError) AllErrors() []error { return m }

// ProjectUserResponseValidationError is the validation error returned by
// ProjectUserResponse.Validate if the designated constraints aren't met.
type ProjectUserResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProjectUserResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProjectUserResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProjectUserResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProjectUserResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProjectUserResponseValidationError) ErrorName() string {
	return "ProjectUserResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProjectUserResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProjectUserResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProjectUserResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProjectUserResponseValidationError{}

// Validate checks the field values on ProjectUserParam with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ProjectUserParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProjectUserParam with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProjectUserParamMultiError, or nil if none found.
func (m *ProjectUserParam) ValidateAll() error {
	return m.validate(true)
}

func (m *ProjectUserParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ProjectUserParamMultiError(errors)
	}

	return nil
}

// ProjectUserParamMultiError is an error wrapping multiple validation errors
// returned by ProjectUserParam.ValidateAll() if the designated constraints
// aren't met.
type ProjectUserParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProjectUserParamMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProjectUserParamMultiError) AllErrors() []error { return m }

// ProjectUserParamValidationError is the validation error returned by
// ProjectUserParam.Validate if the designated constraints aren't met.
type ProjectUserParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProjectUserParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProjectUserParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProjectUserParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProjectUserParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProjectUserParamValidationError) ErrorName() string { return "ProjectUserParamValidationError" }

// Error satisfies the builtin error interface
func (e ProjectUserParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProjectUserParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProjectUserParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProjectUserParamValidationError{}

// Validate checks the field values on UserProParam with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserProParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserProParam with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserProParamMultiError, or
// nil if none found.
func (m *UserProParam) ValidateAll() error {
	return m.validate(true)
}

func (m *UserProParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for ProjectId

	if len(errors) > 0 {
		return UserProParamMultiError(errors)
	}

	return nil
}

// UserProParamMultiError is an error wrapping multiple validation errors
// returned by UserProParam.ValidateAll() if the designated constraints aren't met.
type UserProParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserProParamMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserProParamMultiError) AllErrors() []error { return m }

// UserProParamValidationError is the validation error returned by
// UserProParam.Validate if the designated constraints aren't met.
type UserProParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserProParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserProParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserProParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserProParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserProParamValidationError) ErrorName() string { return "UserProParamValidationError" }

// Error satisfies the builtin error interface
func (e UserProParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserProParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserProParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserProParamValidationError{}

// Validate checks the field values on UserQuery with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserQuery) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserQuery with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserQueryMultiError, or nil
// if none found.
func (m *UserQuery) ValidateAll() error {
	return m.validate(true)
}

func (m *UserQuery) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PageNum

	// no validation rules for PageSize

	// no validation rules for ProjectId

	// no validation rules for EmployNo

	// no validation rules for Search

	if len(errors) > 0 {
		return UserQueryMultiError(errors)
	}

	return nil
}

// UserQueryMultiError is an error wrapping multiple validation errors returned
// by UserQuery.ValidateAll() if the designated constraints aren't met.
type UserQueryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserQueryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserQueryMultiError) AllErrors() []error { return m }

// UserQueryValidationError is the validation error returned by
// UserQuery.Validate if the designated constraints aren't met.
type UserQueryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserQueryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserQueryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserQueryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserQueryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserQueryValidationError) ErrorName() string { return "UserQueryValidationError" }

// Error satisfies the builtin error interface
func (e UserQueryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserQuery.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserQueryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserQueryValidationError{}

// Validate checks the field values on UserInProject with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserInProject) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserInProject with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserInProjectMultiError, or
// nil if none found.
func (m *UserInProject) ValidateAll() error {
	return m.validate(true)
}

func (m *UserInProject) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Username

	// no validation rules for ChineseName

	// no validation rules for Email

	// no validation rules for EmployeeNo

	// no validation rules for Role

	if len(errors) > 0 {
		return UserInProjectMultiError(errors)
	}

	return nil
}

// UserInProjectMultiError is an error wrapping multiple validation errors
// returned by UserInProject.ValidateAll() if the designated constraints
// aren't met.
type UserInProjectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserInProjectMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserInProjectMultiError) AllErrors() []error { return m }

// UserInProjectValidationError is the validation error returned by
// UserInProject.Validate if the designated constraints aren't met.
type UserInProjectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserInProjectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserInProjectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserInProjectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserInProjectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserInProjectValidationError) ErrorName() string { return "UserInProjectValidationError" }

// Error satisfies the builtin error interface
func (e UserInProjectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserInProject.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserInProjectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserInProjectValidationError{}

// Validate checks the field values on UserPage with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserPage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserPage with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserPageMultiError, or nil
// if none found.
func (m *UserPage) ValidateAll() error {
	return m.validate(true)
}

func (m *UserPage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalRecords

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UserPageValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UserPageValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UserPageValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UserPageMultiError(errors)
	}

	return nil
}

// UserPageMultiError is an error wrapping multiple validation errors returned
// by UserPage.ValidateAll() if the designated constraints aren't met.
type UserPageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserPageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserPageMultiError) AllErrors() []error { return m }

// UserPageValidationError is the validation error returned by
// UserPage.Validate if the designated constraints aren't met.
type UserPageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserPageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserPageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserPageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserPageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserPageValidationError) ErrorName() string { return "UserPageValidationError" }

// Error satisfies the builtin error interface
func (e UserPageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserPage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserPageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserPageValidationError{}

// Validate checks the field values on UserInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserInfoMultiError, or nil
// if none found.
func (m *UserInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UserInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetProjectUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserInfoValidationError{
					field:  "ProjectUser",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserInfoValidationError{
					field:  "ProjectUser",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProjectUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserInfoValidationError{
				field:  "ProjectUser",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UserInfoMultiError(errors)
	}

	return nil
}

// UserInfoMultiError is an error wrapping multiple validation errors returned
// by UserInfo.ValidateAll() if the designated constraints aren't met.
type UserInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserInfoMultiError) AllErrors() []error { return m }

// UserInfoValidationError is the validation error returned by
// UserInfo.Validate if the designated constraints aren't met.
type UserInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserInfoValidationError) ErrorName() string { return "UserInfoValidationError" }

// Error satisfies the builtin error interface
func (e UserInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserInfoValidationError{}
