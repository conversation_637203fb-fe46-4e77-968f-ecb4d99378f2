// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: iam/user.proto

package iam

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UserParam with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserParam with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserParamMultiError, or nil
// if none found.
func (m *UserParam) ValidateAll() error {
	return m.validate(true)
}

func (m *UserParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	if len(errors) > 0 {
		return UserParamMultiError(errors)
	}

	return nil
}

// UserParamMultiError is an error wrapping multiple validation errors returned
// by UserParam.ValidateAll() if the designated constraints aren't met.
type UserParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserParamMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserParamMultiError) AllErrors() []error { return m }

// UserParamValidationError is the validation error returned by
// UserParam.Validate if the designated constraints aren't met.
type UserParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserParamValidationError) ErrorName() string { return "UserParamValidationError" }

// Error satisfies the builtin error interface
func (e UserParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserParamValidationError{}

// Validate checks the field values on UserLarkUnionId with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UserLarkUnionId) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserLarkUnionId with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserLarkUnionIdMultiError, or nil if none found.
func (m *UserLarkUnionId) ValidateAll() error {
	return m.validate(true)
}

func (m *UserLarkUnionId) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LarkUnionId

	if len(errors) > 0 {
		return UserLarkUnionIdMultiError(errors)
	}

	return nil
}

// UserLarkUnionIdMultiError is an error wrapping multiple validation errors
// returned by UserLarkUnionId.ValidateAll() if the designated constraints
// aren't met.
type UserLarkUnionIdMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserLarkUnionIdMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserLarkUnionIdMultiError) AllErrors() []error { return m }

// UserLarkUnionIdValidationError is the validation error returned by
// UserLarkUnionId.Validate if the designated constraints aren't met.
type UserLarkUnionIdValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserLarkUnionIdValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserLarkUnionIdValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserLarkUnionIdValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserLarkUnionIdValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserLarkUnionIdValidationError) ErrorName() string { return "UserLarkUnionIdValidationError" }

// Error satisfies the builtin error interface
func (e UserLarkUnionIdValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserLarkUnionId.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserLarkUnionIdValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserLarkUnionIdValidationError{}

// Validate checks the field values on UserSearchReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserSearchReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserSearchReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserSearchReqMultiError, or
// nil if none found.
func (m *UserSearchReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UserSearchReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Keyword

	if len(errors) > 0 {
		return UserSearchReqMultiError(errors)
	}

	return nil
}

// UserSearchReqMultiError is an error wrapping multiple validation errors
// returned by UserSearchReq.ValidateAll() if the designated constraints
// aren't met.
type UserSearchReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserSearchReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserSearchReqMultiError) AllErrors() []error { return m }

// UserSearchReqValidationError is the validation error returned by
// UserSearchReq.Validate if the designated constraints aren't met.
type UserSearchReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserSearchReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserSearchReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserSearchReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserSearchReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserSearchReqValidationError) ErrorName() string { return "UserSearchReqValidationError" }

// Error satisfies the builtin error interface
func (e UserSearchReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserSearchReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserSearchReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserSearchReqValidationError{}

// Validate checks the field values on GitlabQuery with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GitlabQuery) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GitlabQuery with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GitlabQueryMultiError, or
// nil if none found.
func (m *GitlabQuery) ValidateAll() error {
	return m.validate(true)
}

func (m *GitlabQuery) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GitlabId

	if len(errors) > 0 {
		return GitlabQueryMultiError(errors)
	}

	return nil
}

// GitlabQueryMultiError is an error wrapping multiple validation errors
// returned by GitlabQuery.ValidateAll() if the designated constraints aren't met.
type GitlabQueryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GitlabQueryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GitlabQueryMultiError) AllErrors() []error { return m }

// GitlabQueryValidationError is the validation error returned by
// GitlabQuery.Validate if the designated constraints aren't met.
type GitlabQueryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GitlabQueryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GitlabQueryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GitlabQueryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GitlabQueryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GitlabQueryValidationError) ErrorName() string { return "GitlabQueryValidationError" }

// Error satisfies the builtin error interface
func (e GitlabQueryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGitlabQuery.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GitlabQueryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GitlabQueryValidationError{}

// Validate checks the field values on User with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *User) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on User with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in UserMultiError, or nil if none found.
func (m *User) ValidateAll() error {
	return m.validate(true)
}

func (m *User) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Username

	// no validation rules for ChineseName

	// no validation rules for Email

	// no validation rules for EmployeeNo

	// no validation rules for LarkUnionId

	// no validation rules for Role

	// no validation rules for GitlabId

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProjectId

	for idx, item := range m.GetRoles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UserValidationError{
						field:  fmt.Sprintf("Roles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UserValidationError{
						field:  fmt.Sprintf("Roles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UserValidationError{
					field:  fmt.Sprintf("Roles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.RoleObj != nil {

		if all {
			switch v := interface{}(m.GetRoleObj()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UserValidationError{
						field:  "RoleObj",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UserValidationError{
						field:  "RoleObj",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRoleObj()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UserValidationError{
					field:  "RoleObj",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UserMultiError(errors)
	}

	return nil
}

// UserMultiError is an error wrapping multiple validation errors returned by
// User.ValidateAll() if the designated constraints aren't met.
type UserMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserMultiError) AllErrors() []error { return m }

// UserValidationError is the validation error returned by User.Validate if the
// designated constraints aren't met.
type UserValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserValidationError) ErrorName() string { return "UserValidationError" }

// Error satisfies the builtin error interface
func (e UserValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUser.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserValidationError{}

// Validate checks the field values on UserPreference with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserPreference) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserPreference with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserPreferenceMultiError,
// or nil if none found.
func (m *UserPreference) ValidateAll() error {
	return m.validate(true)
}

func (m *UserPreference) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for LatestProjectId

	if len(errors) > 0 {
		return UserPreferenceMultiError(errors)
	}

	return nil
}

// UserPreferenceMultiError is an error wrapping multiple validation errors
// returned by UserPreference.ValidateAll() if the designated constraints
// aren't met.
type UserPreferenceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserPreferenceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserPreferenceMultiError) AllErrors() []error { return m }

// UserPreferenceValidationError is the validation error returned by
// UserPreference.Validate if the designated constraints aren't met.
type UserPreferenceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserPreferenceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserPreferenceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserPreferenceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserPreferenceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserPreferenceValidationError) ErrorName() string { return "UserPreferenceValidationError" }

// Error satisfies the builtin error interface
func (e UserPreferenceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserPreference.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserPreferenceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserPreferenceValidationError{}

// Validate checks the field values on UserSearch with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserSearch) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserSearch with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserSearchMultiError, or
// nil if none found.
func (m *UserSearch) ValidateAll() error {
	return m.validate(true)
}

func (m *UserSearch) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for Page

	// no validation rules for Size

	// no validation rules for Username

	// no validation rules for EmployeeNo

	// no validation rules for Email

	// no validation rules for RoleId

	// no validation rules for RoleName

	if len(errors) > 0 {
		return UserSearchMultiError(errors)
	}

	return nil
}

// UserSearchMultiError is an error wrapping multiple validation errors
// returned by UserSearch.ValidateAll() if the designated constraints aren't met.
type UserSearchMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserSearchMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserSearchMultiError) AllErrors() []error { return m }

// UserSearchValidationError is the validation error returned by
// UserSearch.Validate if the designated constraints aren't met.
type UserSearchValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserSearchValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserSearchValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserSearchValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserSearchValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserSearchValidationError) ErrorName() string { return "UserSearchValidationError" }

// Error satisfies the builtin error interface
func (e UserSearchValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserSearch.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserSearchValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserSearchValidationError{}

// Validate checks the field values on ProjectQuery with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProjectQuery) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProjectQuery with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProjectQueryMultiError, or
// nil if none found.
func (m *ProjectQuery) ValidateAll() error {
	return m.validate(true)
}

func (m *ProjectQuery) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	if len(errors) > 0 {
		return ProjectQueryMultiError(errors)
	}

	return nil
}

// ProjectQueryMultiError is an error wrapping multiple validation errors
// returned by ProjectQuery.ValidateAll() if the designated constraints aren't met.
type ProjectQueryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProjectQueryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProjectQueryMultiError) AllErrors() []error { return m }

// ProjectQueryValidationError is the validation error returned by
// ProjectQuery.Validate if the designated constraints aren't met.
type ProjectQueryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProjectQueryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProjectQueryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProjectQueryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProjectQueryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProjectQueryValidationError) ErrorName() string { return "ProjectQueryValidationError" }

// Error satisfies the builtin error interface
func (e ProjectQueryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProjectQuery.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProjectQueryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProjectQueryValidationError{}

// Validate checks the field values on UserIdsQuery with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserIdsQuery) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserIdsQuery with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserIdsQueryMultiError, or
// nil if none found.
func (m *UserIdsQuery) ValidateAll() error {
	return m.validate(true)
}

func (m *UserIdsQuery) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UserIdsQueryMultiError(errors)
	}

	return nil
}

// UserIdsQueryMultiError is an error wrapping multiple validation errors
// returned by UserIdsQuery.ValidateAll() if the designated constraints aren't met.
type UserIdsQueryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserIdsQueryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserIdsQueryMultiError) AllErrors() []error { return m }

// UserIdsQueryValidationError is the validation error returned by
// UserIdsQuery.Validate if the designated constraints aren't met.
type UserIdsQueryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserIdsQueryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserIdsQueryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserIdsQueryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserIdsQueryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserIdsQueryValidationError) ErrorName() string { return "UserIdsQueryValidationError" }

// Error satisfies the builtin error interface
func (e UserIdsQueryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserIdsQuery.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserIdsQueryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserIdsQueryValidationError{}

// Validate checks the field values on UserQueryResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UserQueryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserQueryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserQueryResponseMultiError, or nil if none found.
func (m *UserQueryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UserQueryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Totals

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UserQueryResponseValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UserQueryResponseValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UserQueryResponseValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UserQueryResponseMultiError(errors)
	}

	return nil
}

// UserQueryResponseMultiError is an error wrapping multiple validation errors
// returned by UserQueryResponse.ValidateAll() if the designated constraints
// aren't met.
type UserQueryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserQueryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserQueryResponseMultiError) AllErrors() []error { return m }

// UserQueryResponseValidationError is the validation error returned by
// UserQueryResponse.Validate if the designated constraints aren't met.
type UserQueryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserQueryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserQueryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserQueryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserQueryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserQueryResponseValidationError) ErrorName() string {
	return "UserQueryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UserQueryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserQueryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserQueryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserQueryResponseValidationError{}

// Validate checks the field values on UserPreferenceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UserPreferenceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserPreferenceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserPreferenceResponseMultiError, or nil if none found.
func (m *UserPreferenceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UserPreferenceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUserPreferences()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserPreferenceResponseValidationError{
					field:  "UserPreferences",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserPreferenceResponseValidationError{
					field:  "UserPreferences",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserPreferences()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserPreferenceResponseValidationError{
				field:  "UserPreferences",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UserPreferenceResponseMultiError(errors)
	}

	return nil
}

// UserPreferenceResponseMultiError is an error wrapping multiple validation
// errors returned by UserPreferenceResponse.ValidateAll() if the designated
// constraints aren't met.
type UserPreferenceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserPreferenceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserPreferenceResponseMultiError) AllErrors() []error { return m }

// UserPreferenceResponseValidationError is the validation error returned by
// UserPreferenceResponse.Validate if the designated constraints aren't met.
type UserPreferenceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserPreferenceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserPreferenceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserPreferenceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserPreferenceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserPreferenceResponseValidationError) ErrorName() string {
	return "UserPreferenceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UserPreferenceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserPreferenceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserPreferenceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserPreferenceResponseValidationError{}

// Validate checks the field values on UserResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserResponseMultiError, or
// nil if none found.
func (m *UserResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UserResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserResponseValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserResponseValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserResponseValidationError{
				field:  "User",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UserResponseMultiError(errors)
	}

	return nil
}

// UserResponseMultiError is an error wrapping multiple validation errors
// returned by UserResponse.ValidateAll() if the designated constraints aren't met.
type UserResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserResponseMultiError) AllErrors() []error { return m }

// UserResponseValidationError is the validation error returned by
// UserResponse.Validate if the designated constraints aren't met.
type UserResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserResponseValidationError) ErrorName() string { return "UserResponseValidationError" }

// Error satisfies the builtin error interface
func (e UserResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserResponseValidationError{}

// Validate checks the field values on FindUserByUniqConditionReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FindUserByUniqConditionReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FindUserByUniqConditionReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FindUserByUniqConditionReqMultiError, or nil if none found.
func (m *FindUserByUniqConditionReq) ValidateAll() error {
	return m.validate(true)
}

func (m *FindUserByUniqConditionReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.UserId != nil {
		// no validation rules for UserId
	}

	if m.Username != nil {
		// no validation rules for Username
	}

	if m.Email != nil {
		// no validation rules for Email
	}

	if m.EmployeeNo != nil {
		// no validation rules for EmployeeNo
	}

	if m.LarkUnionId != nil {
		// no validation rules for LarkUnionId
	}

	if m.GitlabId != nil {
		// no validation rules for GitlabId
	}

	if len(errors) > 0 {
		return FindUserByUniqConditionReqMultiError(errors)
	}

	return nil
}

// FindUserByUniqConditionReqMultiError is an error wrapping multiple
// validation errors returned by FindUserByUniqConditionReq.ValidateAll() if
// the designated constraints aren't met.
type FindUserByUniqConditionReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FindUserByUniqConditionReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FindUserByUniqConditionReqMultiError) AllErrors() []error { return m }

// FindUserByUniqConditionReqValidationError is the validation error returned
// by FindUserByUniqConditionReq.Validate if the designated constraints aren't met.
type FindUserByUniqConditionReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FindUserByUniqConditionReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FindUserByUniqConditionReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FindUserByUniqConditionReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FindUserByUniqConditionReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FindUserByUniqConditionReqValidationError) ErrorName() string {
	return "FindUserByUniqConditionReqValidationError"
}

// Error satisfies the builtin error interface
func (e FindUserByUniqConditionReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFindUserByUniqConditionReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FindUserByUniqConditionReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FindUserByUniqConditionReqValidationError{}
