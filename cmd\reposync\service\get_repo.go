package service

import (
	"context"
	"time"

	"golang.ttyuyin.com/harmony/csi-driver/pkg/task"
	"golang.ttyuyin.com/harmony/pkg/gormx"
	"golang.ttyuyin.com/harmony/pkg/log"
)

const (
	getAllRepoSQL = "select distinct(repo_addr) from app where updated_at > ?"
)

// 目前没有接口获取所有的repo，所以这里继续使用数据库查询
func getAllRepoByDB(ctx context.Context) []string {
	var repos []string

	err := gormx.WithContext(ctx).Table("app").Raw(getAllRepoSQL, time.Now().AddDate(0, 0, -5)).Scan(&repos).Error
	if err != nil {
		log.Errorf("get all repo failed, err: %v", err)
	}

	log.Infof("get all repo succeed, count: %d", len(repos))
	//repos = []string{"https://gitlab.ttyuyin.com/lijucong/quicksilver.git"}
	return repos
}

func getAllRepo() []task.Params {
	repos := getAllRepoByDB(context.Background())
	items := make([]task.Params, 0, len(repos))
	for _, r := range repos {
		if r == "" {
			continue
		}
		items = append(items, task.Params{RepoURL: r, Branch: "master"})
	}
	return items
}

// 从通知中获取repo
func getReposFromNotify(ctx context.Context) []string {
	return []string{"https://gitlab.ttyuyin.com/lijucong/quicksilver.git"}
}
