package repo

import (
	"context"
	"errors"
	"os"
	"path"
	"strings"
	"time"

	"golang.ttyuyin.com/harmony/csi-driver/pkg/bootstrap"
	"golang.ttyuyin.com/harmony/pkg/log"
)

func cloneRepoV1(ctx context.Context, repoUrl, cloneDir string) {
	// check the repo is exists
	_, err := os.Stat(path.Join(cloneDir, repoExt))
	if err == nil {
		log.Infof("cloneDir(%s) is exists, continue clone", cloneDir)
		return
	}

	// create dir
	err = mkdir(ctx, cloneDir)
	if err != nil {
		log.Errorf("create dir(%s) failed, err: %v", cloneDir, err)
		return
	}

	log.Infof("clone repo %s to %s", repoUrl, cloneDir)
	err = gitCloneV1(ctx, repoUrl, cloneDir)
	if err != nil {
		log.Errorf("clone repo(%s) failed %v", repoUrl, err)
	} else {
		log.Infof("clone repo(%s) success", repoUrl)
	}

	if !bootstrap.DebugEnabled() {
		// check the repo is exists
		_, err := os.Stat(cloneDir)
		if err != nil && os.IsNotExist(err) {
			log.Infof("chown dir(%s) is not exists, continue chown", cloneDir)
			return
		}

		// chown git user
		err = execCommand(ctx, "chown", "-R", "65532:65532", cloneDir)

		if err != nil {
			log.Errorf("chown dir(%s) failed, err: %v", cloneDir, err)
		}
	}
}

func gitCloneV1(ctx context.Context, repoUrl, cloneDir string) error {
	gitCrendentials := os.Getenv("GIT_CRENDENTIALS")

	// rewrite
	if !strings.HasSuffix(repoUrl, ".git") {
		repoUrl = repoUrl + ".git"
	}
	strSlice := strings.Split(repoUrl, "/")
	if len(strSlice) < 5 {
		return errors.New("repoUrl is invalid")
	}

	urlSlice := []string{gitCrendentials}
	urlSlice = append(urlSlice, strSlice[3:]...)

	url := strings.Join(urlSlice, "/")
	return execCommand(ctx, "git", "clone", url, cloneDir)
}

// v1 版本的仓库名称提取方式，在多级子项目下提取会有问题，后续创建的目录难以搜寻，因此 v2 更新了提取方式
func extractRepoDirV1(repoUrl string) (groupName, projectName string) {
	if strings.HasSuffix(repoUrl, ".git") {
		repoUrl = strings.TrimSuffix(repoUrl, ".git")
	}

	strSlice := strings.Split(repoUrl, "/")
	if len(strSlice) < 5 {
		log.Warnf("repoUrl(%s) is invalid", repoUrl)
		return
	}

	groupName, projectName = strSlice[len(strSlice)-2], strSlice[len(strSlice)-1]
	return
}

func buildV1CloneDir(repoUrl string, rootPath string, isPreload bool) string {
	if rootPath == "" {
		rootPath = "/data"
	}

	gName, pName := extractRepoDirV1(repoUrl)
	if gName == "" || pName == "" {
		return ""
	}

	if isPreload {
		return path.Join(rootPath, gName, pName, getCurrentDateString())
	}
	return path.Join(rootPath, gName, pName, getNextDateString())
}

func getNextDateString() string {
	return time.Now().AddDate(0, 1, 0).Format("2006-01")
}

func getCurrentDateString() string {
	return time.Now().Format("2006-01")
}
