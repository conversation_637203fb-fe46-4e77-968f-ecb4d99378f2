// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: pipeline/pipeline.proto

package pipeline

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PipelineService_NewPipeline_FullMethodName                        = "/pipeline.PipelineService/NewPipeline"
	PipelineService_GetPipelineConfig_FullMethodName                  = "/pipeline.PipelineService/GetPipelineConfig"
	PipelineService_GetPipelineByAppId_FullMethodName                 = "/pipeline.PipelineService/GetPipelineByAppId"
	PipelineService_GetTaskById_FullMethodName                        = "/pipeline.PipelineService/GetTaskById"
	PipelineService_UpdatePipelineAppMsg_FullMethodName               = "/pipeline.PipelineService/UpdatePipelineAppMsg"
	PipelineService_UpdatePipelineTaskConfig_FullMethodName           = "/pipeline.PipelineService/UpdatePipelineTaskConfig"
	PipelineService_UpdatePipelineTaskMultiCloudConfig_FullMethodName = "/pipeline.PipelineService/UpdatePipelineTaskMultiCloudConfig"
	PipelineService_GetPipelineCountByAppIds_FullMethodName           = "/pipeline.PipelineService/GetPipelineCountByAppIds"
)

// PipelineServiceClient is the client API for PipelineService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PipelineServiceClient interface {
	NewPipeline(ctx context.Context, in *Pipeline, opts ...grpc.CallOption) (*PipelineResult, error)
	GetPipelineConfig(ctx context.Context, in *GetPipelineConfigReq, opts ...grpc.CallOption) (*GetPipelineConfigResp, error)
	GetPipelineByAppId(ctx context.Context, in *Pipeline, opts ...grpc.CallOption) (*PipelineArray, error)
	GetTaskById(ctx context.Context, in *Task, opts ...grpc.CallOption) (*Task, error)
	UpdatePipelineAppMsg(ctx context.Context, in *PipelineAppReq, opts ...grpc.CallOption) (*PipelineAppResp, error)
	UpdatePipelineTaskConfig(ctx context.Context, in *UpdatePipelineTaskConfigReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	UpdatePipelineTaskMultiCloudConfig(ctx context.Context, in *UpdatePipelineTaskMultiCloudConfigReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetPipelineCountByAppIds(ctx context.Context, in *PipelineCountReq, opts ...grpc.CallOption) (*PipelineCountResp, error)
}

type pipelineServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPipelineServiceClient(cc grpc.ClientConnInterface) PipelineServiceClient {
	return &pipelineServiceClient{cc}
}

func (c *pipelineServiceClient) NewPipeline(ctx context.Context, in *Pipeline, opts ...grpc.CallOption) (*PipelineResult, error) {
	out := new(PipelineResult)
	err := c.cc.Invoke(ctx, PipelineService_NewPipeline_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) GetPipelineConfig(ctx context.Context, in *GetPipelineConfigReq, opts ...grpc.CallOption) (*GetPipelineConfigResp, error) {
	out := new(GetPipelineConfigResp)
	err := c.cc.Invoke(ctx, PipelineService_GetPipelineConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) GetPipelineByAppId(ctx context.Context, in *Pipeline, opts ...grpc.CallOption) (*PipelineArray, error) {
	out := new(PipelineArray)
	err := c.cc.Invoke(ctx, PipelineService_GetPipelineByAppId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) GetTaskById(ctx context.Context, in *Task, opts ...grpc.CallOption) (*Task, error) {
	out := new(Task)
	err := c.cc.Invoke(ctx, PipelineService_GetTaskById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) UpdatePipelineAppMsg(ctx context.Context, in *PipelineAppReq, opts ...grpc.CallOption) (*PipelineAppResp, error) {
	out := new(PipelineAppResp)
	err := c.cc.Invoke(ctx, PipelineService_UpdatePipelineAppMsg_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) UpdatePipelineTaskConfig(ctx context.Context, in *UpdatePipelineTaskConfigReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PipelineService_UpdatePipelineTaskConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) UpdatePipelineTaskMultiCloudConfig(ctx context.Context, in *UpdatePipelineTaskMultiCloudConfigReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PipelineService_UpdatePipelineTaskMultiCloudConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) GetPipelineCountByAppIds(ctx context.Context, in *PipelineCountReq, opts ...grpc.CallOption) (*PipelineCountResp, error) {
	out := new(PipelineCountResp)
	err := c.cc.Invoke(ctx, PipelineService_GetPipelineCountByAppIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PipelineServiceServer is the server API for PipelineService service.
// All implementations must embed UnimplementedPipelineServiceServer
// for forward compatibility
type PipelineServiceServer interface {
	NewPipeline(context.Context, *Pipeline) (*PipelineResult, error)
	GetPipelineConfig(context.Context, *GetPipelineConfigReq) (*GetPipelineConfigResp, error)
	GetPipelineByAppId(context.Context, *Pipeline) (*PipelineArray, error)
	GetTaskById(context.Context, *Task) (*Task, error)
	UpdatePipelineAppMsg(context.Context, *PipelineAppReq) (*PipelineAppResp, error)
	UpdatePipelineTaskConfig(context.Context, *UpdatePipelineTaskConfigReq) (*emptypb.Empty, error)
	UpdatePipelineTaskMultiCloudConfig(context.Context, *UpdatePipelineTaskMultiCloudConfigReq) (*emptypb.Empty, error)
	GetPipelineCountByAppIds(context.Context, *PipelineCountReq) (*PipelineCountResp, error)
	mustEmbedUnimplementedPipelineServiceServer()
}

// UnimplementedPipelineServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPipelineServiceServer struct {
}

func (UnimplementedPipelineServiceServer) NewPipeline(context.Context, *Pipeline) (*PipelineResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewPipeline not implemented")
}
func (UnimplementedPipelineServiceServer) GetPipelineConfig(context.Context, *GetPipelineConfigReq) (*GetPipelineConfigResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPipelineConfig not implemented")
}
func (UnimplementedPipelineServiceServer) GetPipelineByAppId(context.Context, *Pipeline) (*PipelineArray, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPipelineByAppId not implemented")
}
func (UnimplementedPipelineServiceServer) GetTaskById(context.Context, *Task) (*Task, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaskById not implemented")
}
func (UnimplementedPipelineServiceServer) UpdatePipelineAppMsg(context.Context, *PipelineAppReq) (*PipelineAppResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePipelineAppMsg not implemented")
}
func (UnimplementedPipelineServiceServer) UpdatePipelineTaskConfig(context.Context, *UpdatePipelineTaskConfigReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePipelineTaskConfig not implemented")
}
func (UnimplementedPipelineServiceServer) UpdatePipelineTaskMultiCloudConfig(context.Context, *UpdatePipelineTaskMultiCloudConfigReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePipelineTaskMultiCloudConfig not implemented")
}
func (UnimplementedPipelineServiceServer) GetPipelineCountByAppIds(context.Context, *PipelineCountReq) (*PipelineCountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPipelineCountByAppIds not implemented")
}
func (UnimplementedPipelineServiceServer) mustEmbedUnimplementedPipelineServiceServer() {}

// UnsafePipelineServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PipelineServiceServer will
// result in compilation errors.
type UnsafePipelineServiceServer interface {
	mustEmbedUnimplementedPipelineServiceServer()
}

func RegisterPipelineServiceServer(s grpc.ServiceRegistrar, srv PipelineServiceServer) {
	s.RegisterService(&PipelineService_ServiceDesc, srv)
}

func _PipelineService_NewPipeline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Pipeline)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).NewPipeline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_NewPipeline_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).NewPipeline(ctx, req.(*Pipeline))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_GetPipelineConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPipelineConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).GetPipelineConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_GetPipelineConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).GetPipelineConfig(ctx, req.(*GetPipelineConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_GetPipelineByAppId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Pipeline)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).GetPipelineByAppId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_GetPipelineByAppId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).GetPipelineByAppId(ctx, req.(*Pipeline))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_GetTaskById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).GetTaskById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_GetTaskById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).GetTaskById(ctx, req.(*Task))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_UpdatePipelineAppMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PipelineAppReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).UpdatePipelineAppMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_UpdatePipelineAppMsg_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).UpdatePipelineAppMsg(ctx, req.(*PipelineAppReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_UpdatePipelineTaskConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePipelineTaskConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).UpdatePipelineTaskConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_UpdatePipelineTaskConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).UpdatePipelineTaskConfig(ctx, req.(*UpdatePipelineTaskConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_UpdatePipelineTaskMultiCloudConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePipelineTaskMultiCloudConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).UpdatePipelineTaskMultiCloudConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_UpdatePipelineTaskMultiCloudConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).UpdatePipelineTaskMultiCloudConfig(ctx, req.(*UpdatePipelineTaskMultiCloudConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_GetPipelineCountByAppIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PipelineCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).GetPipelineCountByAppIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_GetPipelineCountByAppIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).GetPipelineCountByAppIds(ctx, req.(*PipelineCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PipelineService_ServiceDesc is the grpc.ServiceDesc for PipelineService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PipelineService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pipeline.PipelineService",
	HandlerType: (*PipelineServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "NewPipeline",
			Handler:    _PipelineService_NewPipeline_Handler,
		},
		{
			MethodName: "GetPipelineConfig",
			Handler:    _PipelineService_GetPipelineConfig_Handler,
		},
		{
			MethodName: "GetPipelineByAppId",
			Handler:    _PipelineService_GetPipelineByAppId_Handler,
		},
		{
			MethodName: "GetTaskById",
			Handler:    _PipelineService_GetTaskById_Handler,
		},
		{
			MethodName: "UpdatePipelineAppMsg",
			Handler:    _PipelineService_UpdatePipelineAppMsg_Handler,
		},
		{
			MethodName: "UpdatePipelineTaskConfig",
			Handler:    _PipelineService_UpdatePipelineTaskConfig_Handler,
		},
		{
			MethodName: "UpdatePipelineTaskMultiCloudConfig",
			Handler:    _PipelineService_UpdatePipelineTaskMultiCloudConfig_Handler,
		},
		{
			MethodName: "GetPipelineCountByAppIds",
			Handler:    _PipelineService_GetPipelineCountByAppIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pipeline/pipeline.proto",
}
